#!/usr/bin/env python3
"""
极限舵机测试
使用更大的PWM范围来测试MG996R是否真的能动

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def extreme_pwm_test():
    """极限PWM测试 - 使用更大的PWM范围"""
    print("⚡ 极限PWM测试")
    print("="*25)
    print("⚠️  警告：使用极限PWM值，请小心观察舵机")
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        # 极限测试序列 - 更大的PWM范围
        extreme_sequence = [
            (7.5, "停止位置", 2),
            (5.0, "极限逆时针 (1.0ms)", 4),
            (7.5, "停止", 2),
            (10.0, "极限顺时针 (2.0ms)", 4),
            (7.5, "停止", 2),
            (4.5, "超极限逆时针 (0.9ms)", 4),
            (7.5, "停止", 2),
            (10.5, "超极限顺时针 (2.1ms)", 4),
            (7.5, "最终停止", 2)
        ]
        
        print("\n开始极限测试...")
        print("如果舵机在这个测试中还不动，可能是硬件问题")
        
        for duty, description, duration in extreme_sequence:
            change = duty - 7.5
            ms_value = duty / 50.0  # 转换为毫秒
            
            print(f"\n设置: {duty:.1f}% ({description})")
            print(f"  PWM变化: {change:+.1f}% ({ms_value:.2f}ms)")
            
            try:
                servo_pwm.duty(duty)
                print(f"  ✓ PWM设置成功")
                
                if duty > 7.5:
                    print(f"  ← 预期：强力顺时针转动")
                elif duty < 7.5:
                    print(f"  → 预期：强力逆时针转动")
                else:
                    print(f"  ⏸ 预期：停止")
                
                # 显示倒计时
                for i in range(duration, 0, -1):
                    print(f"  观察 {i} 秒... 舵机是否转动？", end='\r')
                    time.sleep(1)
                print("  完成！" + " " * 30)
                
            except Exception as e:
                print(f"  ❌ PWM设置失败: {e}")
        
        print("\n" + "="*25)
        print("极限测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def frequency_test():
    """测试不同PWM频率"""
    print("\n🔄 PWM频率测试")
    print("="*20)
    print("有些舵机对频率敏感，测试不同频率")
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        
        # 测试不同频率
        frequencies = [50, 60, 40, 100, 200]
        
        for freq in frequencies:
            print(f"\n测试频率: {freq}Hz")
            try:
                servo_pwm = pwm.PWM(6, freq=freq, duty=7.5, enable=True)
                print(f"  ✓ {freq}Hz PWM初始化成功")
                
                # 测试转动
                servo_pwm.duty(8.5)
                print(f"  设置8.5%占空比，观察2秒...")
                time.sleep(2)
                
                servo_pwm.duty(6.5)
                print(f"  设置6.5%占空比，观察2秒...")
                time.sleep(2)
                
                servo_pwm.duty(7.5)
                print(f"  回到停止位置")
                time.sleep(1)
                
            except Exception as e:
                print(f"  ❌ {freq}Hz测试失败: {e}")
        
    except Exception as e:
        print(f"❌ 频率测试失败: {e}")

def continuous_rotation_test():
    """连续旋转测试 - 模拟真正的360度舵机行为"""
    print("\n🔄 连续旋转测试")
    print("="*20)
    print("测试是否为真正的360度连续旋转舵机")
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        print("\n真正的360度舵机应该：")
        print("- 在7.5%时完全停止")
        print("- 在>7.5%时连续顺时针转动")
        print("- 在<7.5%时连续逆时针转动")
        print("- 转动速度与PWM值成正比")
        
        # 连续旋转测试
        rotation_tests = [
            (7.5, "停止测试", 3),
            (8.0, "慢速顺时针", 5),
            (7.5, "停止", 2),
            (7.0, "慢速逆时针", 5),
            (7.5, "停止", 2),
            (9.0, "快速顺时针", 5),
            (7.5, "停止", 2),
            (6.0, "快速逆时针", 5),
            (7.5, "最终停止", 2)
        ]
        
        for duty, description, duration in rotation_tests:
            print(f"\n{description}: {duty:.1f}%")
            servo_pwm.duty(duty)
            
            if duty == 7.5:
                print("  → 舵机应该完全停止，不应该有任何转动")
            elif duty > 7.5:
                speed = (duty - 7.5) / 1.5 * 100  # 估算速度百分比
                print(f"  → 舵机应该连续顺时针转动，速度约{speed:.0f}%")
            else:
                speed = (7.5 - duty) / 1.5 * 100  # 估算速度百分比
                print(f"  → 舵机应该连续逆时针转动，速度约{speed:.0f}%")
            
            time.sleep(duration)
        
        print("\n连续旋转测试完成")
        
    except Exception as e:
        print(f"❌ 连续旋转测试失败: {e}")

def hardware_diagnosis():
    """硬件诊断"""
    print("\n🔧 硬件诊断")
    print("="*15)
    
    print("请检查以下硬件问题：")
    print("\n1. 电源供应：")
    print("   - MG996R需要4.8V-7.2V电源")
    print("   - 电流需求：无负载时约100mA，有负载时可达2A")
    print("   - 检查电源是否能提供足够电流")
    
    print("\n2. 连接检查：")
    print("   - 红线：电源正极 (VCC)")
    print("   - 黑/棕线：电源负极 (GND)")
    print("   - 橙/黄线：信号线 (连接到A18)")
    
    print("\n3. 舵机类型确认：")
    print("   - 确认是MG996R 360度版本")
    print("   - 不是普通的180度版本")
    print("   - 360度版本通常标有'360°'或'Continuous'")
    
    print("\n4. 舵机状态：")
    print("   - 用手轻轻转动舵机轴，应该能自由转动")
    print("   - 如果卡死或很难转动，可能是机械故障")
    
    print("\n5. 测试建议：")
    print("   - 尝试用Arduino或其他开发板测试同一舵机")
    print("   - 更换一个确认工作的舵机进行对比")

def main():
    """主函数"""
    print("⚡ MG996R 360度舵机极限诊断")
    print("="*35)
    
    try:
        extreme_pwm_test()
        frequency_test()
        continuous_rotation_test()
        hardware_diagnosis()
        
        print("\n" + "="*35)
        print("🏁 所有测试完成！")
        print("\n📋 结果分析：")
        print("✅ 如果舵机在极限测试中转动了：")
        print("   → 需要在main.py中使用更大的PWM范围")
        print("\n❌ 如果舵机在所有测试中都不动：")
        print("   → 很可能是硬件问题（电源/连接/舵机故障）")
        print("   → 建议按照硬件诊断清单逐项检查")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
