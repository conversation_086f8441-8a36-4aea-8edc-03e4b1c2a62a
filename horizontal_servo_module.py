#!/usr/bin/env python3
"""
水平舵机控制模块
专门用于集成到main.py中的水平舵机控制类

特点：
1. 独立的水平舵机控制
2. 与main.py兼容的接口
3. 完整的错误处理
4. 调试信息支持

@author: AI Assistant
@date: 2025.8.2
"""

from maix import pwm, pinmap, time

class HorizontalServo:
    """水平舵机控制类"""
    
    def __init__(self, debug=False):
        """
        初始化水平舵机
        
        Args:
            debug (bool): 是否启用调试输出
        """
        self.debug = debug
        
        # 硬件配置
        self.pwm_channel = 6
        self.gpio_pin = "A18"
        self.pwm_freq = 50
        
        # LD-3015MG 270度位置舵机参数
        self.min_duty = 2.5      # 0度对应的占空比
        self.max_duty = 12.5     # 270度对应的占空比
        self.center_duty = 7.5   # 135度中位占空比
        
        # 安全运动范围
        self.min_angle = 45      # 最小角度
        self.max_angle = 225     # 最大角度
        self.center_angle = 135  # 中心角度
        self.current_angle = 135 # 当前角度
        
        # PWM对象和状态
        self.pwm = None
        self.enabled = False
        
        # 初始化
        self.init_success = self._init_pwm()
    
    def _init_pwm(self):
        """内部PWM初始化方法"""
        try:
            if self.debug:
                print(f"🔌 配置引脚: {self.gpio_pin} -> PWM{self.pwm_channel}")
            
            # 配置引脚功能
            pinmap.set_pin_function(self.gpio_pin, f"PWM{self.pwm_channel}")
            
            if self.debug:
                print(f"⚡ 初始化PWM{self.pwm_channel}: {self.pwm_freq}Hz, 禁用状态")
            
            # 初始化PWM对象
            self.pwm = pwm.PWM(self.pwm_channel, freq=self.pwm_freq, duty=0, enable=False)
            
            if self.debug:
                print("✅ 水平舵机PWM初始化成功")
                print(f"📋 参数: 范围{self.min_angle}°-{self.max_angle}°, 中心{self.center_angle}°")
            
            return True
            
        except Exception as e:
            print(f"❌ 水平舵机PWM初始化失败: {e}")
            self.pwm = None
            return False
    
    def is_initialized(self):
        """检查是否初始化成功"""
        return self.init_success and self.pwm is not None
    
    def is_enabled(self):
        """检查PWM是否已启用"""
        return self.enabled
    
    def angle_to_duty(self, angle):
        """角度转PWM占空比"""
        # 限制角度范围
        angle = max(self.min_angle, min(self.max_angle, angle))
        
        # 线性映射
        duty = self.min_duty + ((angle - self.min_angle) / (self.max_angle - self.min_angle)) * (self.max_duty - self.min_duty)
        return duty
    
    def set_angle(self, angle):
        """
        设置舵机角度
        
        Args:
            angle (float): 目标角度 (45-225度)
            
        Returns:
            bool: 设置是否成功
        """
        if not self.is_initialized():
            if self.debug:
                print("❌ 水平舵机未初始化")
            return False
        
        try:
            # 限制角度范围
            angle = max(self.min_angle, min(self.max_angle, angle))
            
            # 如果PWM未启用，先启用
            if not self.enabled:
                self.pwm.enable()
                self.enabled = True
                if self.debug:
                    print("🔒 水平舵机PWM已启用")
            
            # 计算并设置占空比
            duty = self.angle_to_duty(angle)
            self.pwm.duty(duty)
            self.current_angle = angle
            
            if self.debug:
                print(f"🔧 水平舵机: {angle}°, PWM: {duty:.2f}%")
            
            return True
            
        except Exception as e:
            if self.debug:
                print(f"❌ 设置水平舵机角度失败: {e}")
            return False
    
    def set_duty(self, duty):
        """
        直接设置PWM占空比
        
        Args:
            duty (float): PWM占空比
            
        Returns:
            bool: 设置是否成功
        """
        if not self.is_initialized():
            if self.debug:
                print("❌ 水平舵机未初始化")
            return False
        
        try:
            # 如果PWM未启用，先启用
            if not self.enabled:
                self.pwm.enable()
                self.enabled = True
                if self.debug:
                    print("🔒 水平舵机PWM已启用")
            
            self.pwm.duty(duty)
            
            if self.debug:
                print(f"🔧 水平舵机PWM: {duty:.2f}%")
            
            return True
            
        except Exception as e:
            if self.debug:
                print(f"❌ 设置水平舵机PWM失败: {e}")
            return False
    
    def enable(self):
        """启用舵机（锁定到中心位置）"""
        if self.debug:
            print("🔒 启用水平舵机...")
        return self.set_angle(self.center_angle)
    
    def disable(self):
        """禁用舵机（可手动搬动）"""
        if not self.is_initialized():
            return False
        
        try:
            self.pwm.disable()
            self.enabled = False
            if self.debug:
                print("🔓 水平舵机已禁用")
            return True
            
        except Exception as e:
            if self.debug:
                print(f"❌ 禁用水平舵机失败: {e}")
            return False
    
    def center(self):
        """舵机回中心位置"""
        if self.debug:
            print("🎯 水平舵机回中心...")
        return self.set_angle(self.center_angle)
    
    def get_current_angle(self):
        """获取当前角度"""
        return self.current_angle
    
    def get_angle_range(self):
        """获取角度范围"""
        return (self.min_angle, self.max_angle)
    
    def get_center_angle(self):
        """获取中心角度"""
        return self.center_angle
    
    def test_movement(self):
        """测试舵机运动"""
        if not self.is_initialized():
            print("❌ 舵机未初始化，无法测试")
            return False
        
        print("🧪 水平舵机运动测试")
        
        # 测试序列
        test_angles = [
            (self.center_angle, "中心位置"),
            (90, "左转45度"),
            (self.center_angle, "回中心"),
            (180, "右转45度"),
            (self.center_angle, "回中心")
        ]
        
        for angle, description in test_angles:
            print(f"📍 {description}: {angle}°")
            if self.set_angle(angle):
                time.sleep(1.5)
            else:
                print("❌ 测试失败")
                return False
        
        print("✅ 运动测试完成")
        return True
    
    def cleanup(self):
        """清理资源"""
        if self.is_initialized() and self.enabled:
            if self.debug:
                print("🧹 清理水平舵机资源...")
            # 回中心位置
            self.center()
            time.sleep(0.5)
            # 禁用PWM
            self.disable()

# 便捷函数
def create_horizontal_servo(debug=False):
    """
    创建水平舵机对象的便捷函数
    
    Args:
        debug (bool): 是否启用调试输出
        
    Returns:
        HorizontalServo: 舵机对象，如果初始化失败返回None
    """
    servo = HorizontalServo(debug=debug)
    if servo.is_initialized():
        return servo
    else:
        return None

# 测试函数
def test_horizontal_servo():
    """测试水平舵机功能"""
    print("🚀 水平舵机测试")
    print("=" * 30)
    
    # 创建舵机对象
    servo = create_horizontal_servo(debug=True)
    
    if servo is None:
        print("❌ 舵机初始化失败")
        return
    
    try:
        # 运动测试
        servo.test_movement()
        
        # 询问是否保持启用
        choice = input("\n是否保持舵机启用状态？(y/n): ").strip().lower()
        if choice != 'y':
            servo.disable()
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    
    finally:
        servo.cleanup()
        print("👋 测试结束")

if __name__ == "__main__":
    test_horizontal_servo()
