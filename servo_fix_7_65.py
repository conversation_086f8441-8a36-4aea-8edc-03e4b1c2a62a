#!/usr/bin/env python3
"""
基于7.65%停止位置的舵机修复
现在我们知道7.66%开始动，7.65%应该是停止位置

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def test_new_stop_position():
    """测试新的停止位置7.65%"""
    print("🎯 测试新停止位置 7.65%")
    print("="*30)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.65, enable=True)
        print("✓ PWM初始化成功，停止位置设为7.65%")
        
        print("\n测试7.65%作为停止位置的双向控制：")
        
        # 基于7.65%停止位置的测试
        test_sequence = [
            (7.65, "新停止位置", 3),
            (7.70, "小幅顺时针 (+0.05%)", 3),
            (7.65, "回到停止", 2),
            (7.60, "小幅逆时针 (-0.05%)", 3),
            (7.65, "回到停止", 2),
            (7.80, "中等顺时针 (+0.15%)", 3),
            (7.65, "回到停止", 2),
            (7.50, "中等逆时针 (-0.15%)", 3),
            (7.65, "回到停止", 2),
            (8.00, "大幅顺时针 (+0.35%)", 3),
            (7.65, "回到停止", 2),
            (7.30, "大幅逆时针 (-0.35%)", 3),
            (7.65, "最终停止", 2)
        ]
        
        working_directions = []
        
        for duty, description, duration in test_sequence:
            change = duty - 7.65
            print(f"\n设置: {duty:.2f}% ({description}) [变化: {change:+.2f}%]")
            
            try:
                servo_pwm.duty(duty)
                
                if duty > 7.65:
                    expected = "顺时针转动"
                    direction = "CW"
                elif duty < 7.65:
                    expected = "逆时针转动"
                    direction = "CCW"
                else:
                    expected = "完全停止"
                    direction = "STOP"
                
                print(f"  预期: {expected}")
                time.sleep(duration)
                
                # 记录工作的方向
                if direction != "STOP" and abs(change) >= 0.05:
                    working_directions.append((duty, direction, change))
                
            except Exception as e:
                print(f"  ❌ 设置失败: {e}")
        
        print(f"\n" + "="*30)
        print("测试完成！工作的PWM值：")
        for duty, direction, change in working_directions:
            print(f"  {duty:.2f}% ({direction}) 变化: {change:+.2f}%")
        
        return working_directions
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return []

def test_extreme_reverse_from_765():
    """从7.65%基础测试极端逆时针"""
    print("\n⚡ 基于7.65%的极端逆时针测试")
    print("="*35)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.65, enable=True)
        print("✓ PWM初始化成功")
        
        print("\n测试更极端的逆时针PWM值：")
        
        # 从7.65%开始，逐步减小
        extreme_tests = [
            7.00, 6.50, 6.00, 5.50, 5.00, 4.50, 4.00, 3.50, 3.00
        ]
        
        working_reverse = []
        
        for duty in extreme_tests:
            change = duty - 7.65
            print(f"\n测试极端逆时针: {duty:.2f}% (变化: {change:+.2f}%)")
            
            try:
                servo_pwm.duty(duty)
                print(f"  观察4秒，舵机是否逆时针转动...")
                time.sleep(4)
                
                # 如果到这里没有异常，记录这个值
                working_reverse.append(duty)
                print(f"  ✅ {duty:.2f}% PWM设置成功")
                
                # 回到停止位置
                servo_pwm.duty(7.65)
                time.sleep(1)
                
            except Exception as e:
                print(f"  ❌ {duty:.2f}% 设置失败: {e}")
        
        print(f"\n极端逆时针测试完成")
        if working_reverse:
            print(f"能设置的逆时针PWM值: {working_reverse}")
        else:
            print("没有找到能工作的极端逆时针PWM值")
        
        return working_reverse
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return []

def create_optimized_parameters(working_directions, working_reverse):
    """基于测试结果创建优化参数"""
    print("\n🔧 创建优化参数")
    print("="*20)
    
    # 分析测试结果
    cw_values = [duty for duty, direction, change in working_directions if direction == "CW"]
    ccw_values = [duty for duty, direction, change in working_directions if direction == "CCW"]
    ccw_values.extend(working_reverse)
    
    print(f"顺时针工作PWM值: {cw_values}")
    print(f"逆时针工作PWM值: {ccw_values}")
    
    # 确定参数
    stop_duty = 7.65
    
    if cw_values:
        cw_min = min(cw_values)
        cw_max = max([8.5, max(cw_values)])  # 至少到8.5%
        print(f"建议顺时针范围: {cw_min:.2f}% ~ {cw_max:.2f}%")
    else:
        cw_max = 8.5
        print("未找到顺时针工作值，使用默认8.5%")
    
    if ccw_values:
        ccw_min = min(ccw_values)
        ccw_max = max(ccw_values)
        print(f"建议逆时针范围: {ccw_min:.2f}% ~ {ccw_max:.2f}%")
    else:
        ccw_min = 6.5
        print("未找到逆时针工作值，使用默认6.5%")
    
    # 生成参数建议
    print(f"\n📋 建议的main.py参数：")
    print(f"self.horizontal_stop_duty = {stop_duty:.2f}")
    print(f"self.horizontal_cw_duty = {cw_max:.2f}")
    print(f"self.horizontal_ccw_duty = {ccw_min:.2f}")
    
    return stop_duty, cw_max, ccw_min

def test_optimized_parameters(stop_duty, cw_duty, ccw_duty):
    """测试优化后的参数"""
    print(f"\n🧪 测试优化参数")
    print("="*20)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=stop_duty, enable=True)
        print("✓ PWM初始化成功")
        
        print(f"测试参数: 停止={stop_duty:.2f}%, 顺时针={cw_duty:.2f}%, 逆时针={ccw_duty:.2f}%")
        
        # 模拟不同速度的控制
        speed_tests = [
            (10, "10%速度"),
            (-10, "-10%速度"),
            (20, "20%速度"),
            (-20, "-20%速度"),
            (5, "5%速度"),
            (-5, "-5%速度")
        ]
        
        for speed, description in speed_tests:
            print(f"\n测试{description}:")
            
            # 计算PWM值（模拟main.py逻辑）
            if speed == 0:
                duty = stop_duty
            elif speed > 0:
                duty_range = cw_duty - stop_duty
                duty_change = (abs(speed) / 100.0) * duty_range
                duty = stop_duty + duty_change
                duty = min(duty, cw_duty)
            else:
                duty_range = stop_duty - ccw_duty
                duty_change = (abs(speed) / 100.0) * duty_range
                duty = stop_duty - duty_change
                duty = max(duty, ccw_duty)
            
            change = duty - stop_duty
            print(f"  计算PWM: {duty:.3f}% (变化: {change:+.3f}%)")
            
            try:
                servo_pwm.duty(duty)
                time.sleep(3)
                
                # 回到停止
                servo_pwm.duty(stop_duty)
                time.sleep(1)
                
            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
        
        print("优化参数测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🔧 基于7.65%停止位置的舵机修复")
    print("="*35)
    
    try:
        print("已知信息：")
        print("- 7.66% 开始转动（顺时针）")
        print("- 推测 7.65% 是真正的停止位置")
        
        # 测试新停止位置
        working_directions = test_new_stop_position()
        
        # 测试极端逆时针
        working_reverse = test_extreme_reverse_from_765()
        
        # 创建优化参数
        stop_duty, cw_duty, ccw_duty = create_optimized_parameters(working_directions, working_reverse)
        
        # 测试优化参数
        test_optimized_parameters(stop_duty, cw_duty, ccw_duty)
        
        print("\n" + "="*35)
        print("🏁 修复完成！")
        print("\n📋 下一步：")
        print("1. 将建议的参数更新到main.py")
        print("2. 重新测试PID控制")
        print("3. 如果逆时针还是不工作，考虑单向控制方案")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
