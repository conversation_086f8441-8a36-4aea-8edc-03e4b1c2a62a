# 舵机方向问题诊断指南

## 🚨 问题描述
舵机控制后，摄像头离红线越来越远，而不是越来越近。

## 🔍 问题原因分析

### 可能的原因：
1. **PID控制方向错误** - 最常见
2. **舵机安装方向错误**
3. **舵机转向定义错误**
4. **误差计算方向错误**

## 🛠️ 诊断步骤

### 步骤1: 启用调试模式
在 `main.py` 中找到：
```python
# DEBUG=True                 # 打开调试模式，取消注释即可
```
改为：
```python
DEBUG=True                 # 打开调试模式，取消注释即可
```

### 步骤2: 运行方向测试
启用DEBUG模式后，程序会自动运行舵机方向测试：
1. 观察水平舵机转向
2. 观察垂直舵机转向
3. 记录实际转向与期望转向的关系

### 步骤3: 观察调试信息
运行程序时，控制台会显示：
```
PID调试: V_err=-10.0 V_out=2.0 V_angle=92.0
逼近控制: 原始err_x=50.0, H_err=50.0, H_speed=20.0
  → 目标在画面右侧，舵机速度=20.0% (顺时针)
```

### 步骤4: 分析转向逻辑
- **目标在右侧** (`err_x > 0`)：摄像头应该向右转
- **目标在左侧** (`err_x < 0`)：摄像头应该向左转
- **目标在上方** (`err_y < 0`)：摄像头应该向上转
- **目标在下方** (`err_y > 0`)：摄像头应该向下转

## 🔧 修复方法

### 修复水平方向错误

如果发现水平舵机转向错误，修改 `main.py` 第246行：

**当前代码：**
```python
horizontal_error = err_x  # 如果方向错误，改为 -err_x
```

**修改为：**
```python
horizontal_error = -err_x  # 反转水平方向
```

### 修复垂直方向错误

如果发现垂直舵机转向错误，修改 `main.py` 第217行：

**当前代码：**
```python
vertical_error = -err_y  # 注意方向，向上为负
```

**修改为：**
```python
vertical_error = err_y  # 反转垂直方向
```

## 📊 方向对照表

### 水平舵机（360°连续旋转）

| 目标位置 | err_x | 期望动作 | 舵机速度 | 如果错误 |
|----------|-------|----------|----------|----------|
| 画面右侧 | > 0 | 向右转 | 正值或负值 | 改变符号 |
| 画面左侧 | < 0 | 向左转 | 负值或正值 | 改变符号 |
| 画面中心 | = 0 | 停止 | 0 | 正确 |

### 垂直舵机（180°位置控制）

| 目标位置 | err_y | 期望动作 | 角度变化 | 如果错误 |
|----------|-------|----------|----------|----------|
| 画面上方 | < 0 | 向上转 | 角度减小 | 改变符号 |
| 画面下方 | > 0 | 向下转 | 角度增大 | 改变符号 |
| 画面中心 | = 0 | 停止 | 不变 | 正确 |

## 🧪 测试验证

### 手动测试方法：
1. 将红线放在画面右侧
2. 观察舵机是否向右转
3. 将红线放在画面左侧
4. 观察舵机是否向左转

### 预期结果：
- 舵机应该始终朝向红线方向转动
- 红线应该逐渐移向画面中心
- 最终红线应该变成一个点（摄像头正对红线）

## 🔄 常见修复组合

### 组合1: 水平方向反转
```python
# 第246行
horizontal_error = -err_x  # 反转水平方向
```

### 组合2: 垂直方向反转
```python
# 第217行
vertical_error = err_y  # 反转垂直方向
```

### 组合3: 两个方向都反转
```python
# 第217行
vertical_error = err_y  # 反转垂直方向
# 第246行
horizontal_error = -err_x  # 反转水平方向
```

## 📝 修复步骤总结

1. **启用DEBUG模式**
2. **运行程序，观察调试信息**
3. **手动测试：将目标放在不同位置**
4. **观察舵机转向是否正确**
5. **如果错误，修改对应的符号**
6. **重新测试验证**

## ⚠️ 注意事项

1. **一次只修改一个方向**，避免混乱
2. **每次修改后都要测试**，确认效果
3. **记录有效的修改**，避免重复调试
4. **确保舵机安装牢固**，避免机械问题
5. **检查电源供应**，确保舵机有足够动力

## 🎯 最终目标

修复后的效果应该是：
- 红线在右侧 → 舵机向右转 → 红线向中心移动
- 红线在左侧 → 舵机向左转 → 红线向中心移动
- 红线在上方 → 舵机向上转 → 红线向中心移动
- 红线在下方 → 舵机向下转 → 红线向中心移动
- 最终红线在画面中心变成一个点

## 🔍 高级调试

如果基本修复无效，可能需要检查：

### 1. 舵机安装方向
- 确认舵机轴的转向与期望一致
- 检查舵机臂的安装角度

### 2. PWM信号定义
- 检查顺时针/逆时针的PWM占空比定义
- 验证舵机的死区设置

### 3. 坐标系定义
- 确认画面坐标系的原点和方向
- 检查误差计算的坐标变换

记住：**耐心调试，一步一步来，最终一定能解决方向问题！**
