from maix import camera, display, image, touchscreen, app  
import json  
import os  
  
class LABThresholdAdjuster:  
    def __init__(self):  
        self.cam = camera.Camera(320, 240)  
        self.disp = display.Display()  
        self.ts = touchscreen.TouchScreen()  
          
        # 退出按钮相关  
        self.need_exit = False  
          
        # 默认LAB阈值  
        self.default_thresholds = {  
            "red": [0, 80, 40, 80, 10, 80],  
            "green": [0, 80, -120, -10, 0, 30],  
            "blue": [0, 80, 30, 100, -120, -60]  
        }  
          
        # 配置文件路径  
        self.config_file = "/root/lab_thresholds.json"  
          
        # 当前选中的颜色和阈值  
        self.current_color = "red"  
        self.thresholds = self.load_thresholds()
        # 确保thresholds是字典类型  
        if not isinstance(self.thresholds, dict):  
            print("配置数据格式错误，使用默认阈值")  
            self.thresholds = self.default_thresholds.copy()  
        self.binary_mode = False  # 二值化模式  
          
        # UI参数  
        self.param_names = ["L_MIN", "L_MAX", "A_MIN", "A_MAX", "B_MIN", "B_MAX"]  
          
        # 按钮区域定义 (使用相对于摄像头图像的坐标)  
        self.color_buttons = [  
            {"name": "RED", "color": "red", "rect": (10, 10, 50, 20)},  
            {"name": "GREEN", "color": "green", "rect": (70, 10, 50, 20)},  
            {"name": "BLUE", "color": "blue", "rect": (130, 10, 50, 20)}  
        ]  
          
        self.save_button = {"name": "SAVE", "rect": (190, 10, 50, 20)}  
        self.binary_button = {"name": "BINARY", "rect": (250, 10, 60, 20)}  
        self.exit_button = {"name": "EXIT", "rect": (10, 210, 50, 25)}  # 添加退出按钮  
          
        # 滑杆区域定义  
        self.sliders = []  
        for i, param in enumerate(self.param_names):  
            y_pos = 40 + i * 30  
            self.sliders.append({  
                "name": param,  
                "index": i,  
                "label_rect": (10, y_pos, 40, 15),  
                "slider_rect": (55, y_pos, 120, 15),  
                "value_rect": (180, y_pos, 30, 15)  
            })  
      
    def load_thresholds(self):  
        """从文件加载保存的阈值"""  
        try:  
            if os.path.exists(self.config_file):  
                with open(self.config_file, 'r') as f:  
                    saved_thresholds = json.load(f)  
                # 验证配置文件格式是否正确  
                if isinstance(saved_thresholds, dict) and all(color in saved_thresholds for color in self.default_thresholds):  
                    print("已加载保存的阈值配置")  
                    return saved_thresholds  
                else:  
                    print("配置文件格式不正确")  
        except Exception as e:  
            print(f"加载阈值配置失败: {e}")  
          
        print("使用默认阈值配置")  
        return self.default_thresholds.copy()  
      
    def save_thresholds(self):  
        """保存当前阈值到文件"""  
        try:  
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)  
            with open(self.config_file, 'w') as f:  
                json.dump(self.thresholds, f, indent=2)  
            print("阈值配置已保存")  
            return True  
        except Exception as e:  
            print(f"保存阈值配置失败: {e}")  
            return False  
      
    def get_current_threshold(self):  
        """获取当前选中颜色的阈值"""  
        # 确保当前颜色存在于阈值字典中  
        if self.current_color not in self.thresholds:  
            self.thresholds[self.current_color] = self.default_thresholds.get(self.current_color, [0, 100, -128, 127, -128, 127])  
        return self.thresholds[self.current_color]  
      
    def set_threshold_value(self, param_index, value):  
        """设置指定参数的值"""  
        threshold = self.get_current_threshold()  
          
        # 根据参数类型设置范围  
        if param_index in [0, 1]:  # L通道  
            threshold[param_index] = max(min(value, 100), 0)  
        else:  # A和B通道  
            threshold[param_index] = max(min(value, 127), -128)  
          
        self.thresholds[self.current_color] = threshold  
      
    def map_touch_to_image(self, touch_x, touch_y):  
        """将触摸坐标映射到图像坐标"""  
        # 参考 tic_tac_toe 项目的坐标映射方法  
        cam_w = self.cam.width()  
        cam_h = self.cam.height()  
        disp_w = self.disp.width()  
        disp_h = self.disp.height()  
          
        # 计算映射后的坐标  
        real_x = int(touch_x * cam_w / disp_w)  
        real_y = int(touch_y * cam_h / disp_h)  
          
        return real_x, real_y  
      
    def draw_button(self, img, rect, text, is_selected=False, is_pressed=False):  
        """绘制按钮"""  
        x, y, w, h = rect  
          
        # 按钮背景色  
        if is_pressed:  
            bg_color = image.COLOR_GRAY  
        elif is_selected:  
            bg_color = image.COLOR_GREEN  
        else:  
            bg_color = image.COLOR_BLUE  
          
        # 绘制按钮背景  
        img.draw_rect(x, y, w, h, bg_color, -1)  
        img.draw_rect(x, y, w, h, image.COLOR_WHITE, 1)  
          
        # 绘制按钮文字  
        text_color = image.COLOR_WHITE if not is_pressed else image.COLOR_BLACK  
        img.draw_string(x + 2, y + 2, text, text_color, scale=0.8)  
      
    def draw_slider(self, img, slider_info):  
        """绘制滑杆"""  
        param_index = slider_info["index"]  
        threshold = self.get_current_threshold()  
        current_value = threshold[param_index]  
          
        # 计算值的范围和位置  
        if param_index in [0, 1]:  # L通道 0-100  
            min_val, max_val = 0, 100  
        else:  # A和B通道 -128到127  
            min_val, max_val = -128, 127  
          
        # 计算滑块位置  
        value_ratio = (current_value - min_val) / (max_val - min_val)  
        slider_x, slider_y, slider_w, slider_h = slider_info["slider_rect"]  
        knob_x = int(slider_x + value_ratio * (slider_w - 8))  
          
        # 绘制标签  
        label_x, label_y, label_w, label_h = slider_info["label_rect"]  
        img.draw_string(label_x, label_y, slider_info["name"], image.COLOR_WHITE, scale=0.7)  
          
        # 绘制滑杆轨道  
        img.draw_rect(slider_x, slider_y + 3, slider_w, 8, image.COLOR_GRAY, -1)  
        img.draw_rect(slider_x, slider_y + 3, slider_w, 8, image.COLOR_WHITE, 1)  
          
        # 绘制滑块  
        img.draw_rect(knob_x, slider_y, 8, slider_h, image.COLOR_RED, -1)  
        img.draw_rect(knob_x, slider_y, 8, slider_h, image.COLOR_WHITE, 1)  
          
        # 绘制数值  
        value_x, value_y, value_w, value_h = slider_info["value_rect"]  
        img.draw_string(value_x, value_y, str(current_value), image.COLOR_YELLOW, scale=0.7)  
      
    def draw_ui(self, img):  
        """绘制用户界面"""  
        # 绘制颜色选择按钮  
        for button in self.color_buttons:  
            is_selected = (button["color"] == self.current_color)  
            self.draw_button(img, button["rect"], button["name"], is_selected)  
          
        # 绘制保存按钮  
        self.draw_button(img, self.save_button["rect"], self.save_button["name"])  
          
        # 绘制二值化按钮  
        self.draw_button(img, self.binary_button["rect"], self.binary_button["name"], self.binary_mode)  
          
        # 绘制退出按钮  
        self.draw_button(img, self.exit_button["rect"], self.exit_button["name"])  
          
        # 绘制所有滑杆  
        for slider in self.sliders:  
            self.draw_slider(img, slider)  
          
        # 显示当前阈值  
        threshold = self.get_current_threshold()  
        threshold_str = f"[{threshold[0]},{threshold[1]},{threshold[2]},{threshold[3]},{threshold[4]},{threshold[5]}]"  
        img.draw_string(70, 220, f"Threshold: {threshold_str}", image.COLOR_WHITE, scale=0.9)  
      
    def handle_touch(self, touch_x, touch_y):  
        """处理触摸事件"""  
        # 映射触摸坐标到图像坐标  
        x, y = self.map_touch_to_image(touch_x, touch_y)  
          
        # 检查退出按钮  
        bx, by, bw, bh = self.exit_button["rect"]  
        if bx <= x <= bx + bw and by <= y <= by + bh:  
            self.need_exit = True  
            print("退出程序")  
            return  
          
        # 检查颜色按钮  
        for button in self.color_buttons:  
            bx, by, bw, bh = button["rect"]  
            if bx <= x <= bx + bw and by <= y <= by + bh:  
                self.current_color = button["color"]  
                print(f"切换到颜色: {self.current_color}")  
                return  
          
        # 检查保存按钮  
        bx, by, bw, bh = self.save_button["rect"]  
        if bx <= x <= bx + bw and by <= y <= by + bh:  
            if self.save_thresholds():  
                self.show_save_message()  
            return  
          
        # 检查二值化按钮  
        bx, by, bw, bh = self.binary_button["rect"]  
        if bx <= x <= bx + bw and by <= y <= by + bh:  
            self.binary_mode = not self.binary_mode  
            print(f"二值化模式: {'开启' if self.binary_mode else '关闭'}")  
            return  
          
        # 检查滑杆  
        for slider in self.sliders:  
            sx, sy, sw, sh = slider["slider_rect"]  
            if sx <= x <= sx + sw and sy <= y <= sy + sh:  
                # 计算新值  
                param_index = slider["index"]  
                if param_index in [0, 1]:  # L通道  
                    min_val, max_val = 0, 100  
                else:  # A和B通道  
                    min_val, max_val = -128, 127  
                  
                # 根据触摸位置计算值  
                ratio = (x - sx) / sw  
                new_value = int(min_val + ratio * (max_val - min_val))  
                self.set_threshold_value(param_index, new_value)  
                print(f"调节 {slider['name']}: {new_value}")  
                return  
      
    def show_save_message(self):  
        """显示保存成功消息"""  
        msg_img = image.Image(320, 240, image.Format.FMT_RGB888)  
        msg_img.draw_rect(0, 0, 320, 240, image.COLOR_BLACK, -1)  
        msg_img.draw_string(120, 120, "SAVED!", image.COLOR_GREEN, scale=2)  
        self.disp.show(msg_img)  
          
        import time  
        time.sleep(1)  
      
    def apply_binary_threshold(self, img, threshold):  
        """应用二值化处理 - 白色为识别物体，黑色为背景"""  
        # 创建二值化图像，默认为黑色背景  
        binary_img = image.Image(img.width(), img.height(), image.Format.FMT_RGB888)  
        binary_img.draw_rect(0, 0, img.width(), img.height(), image.COLOR_BLACK, -1)  
          
        # 使用find_blobs检测色块  
        blobs = img.find_blobs([threshold], pixels_threshold=1)  
          
        # 将检测到的区域填充为白色  
        for blob in blobs:  
            # 直接填充整个blob区域为白色  
            binary_img.draw_rect(blob.x(), blob.y(), blob.w(), blob.h(), image.COLOR_WHITE, -1)  
          
        return binary_img  
      
    def run(self):  
        """主运行循环"""  
        print("LAB阈值调节器启动")  
        print("操作说明:")  
        print("- 点击颜色按钮切换颜色")  
        print("- 拖动滑杆调节LAB参数")  
        print("- 点击SAVE按钮保存设置")  
        print("- 点击BINARY按钮切换二值化显示")  
        print("- 点击EXIT按钮退出程序")  
          
        while not app.need_exit() and not self.need_exit:  
            img = self.cam.read()  
              
            # 使用当前阈值检测色块  
            current_threshold = self.get_current_threshold()  
              
            if self.binary_mode:  
                # 二值化模式：显示二值化结果  
                binary_img = self.apply_binary_threshold(img, current_threshold)  
                # 在二值化图像上绘制UI  
                self.draw_ui(binary_img)  
                display_img = binary_img  
            else:  
                # 正常模式：显示色块检测结果  
                blobs = img.find_blobs([current_threshold], pixels_threshold=100)  
                  
                 # 绘制检测到的色块  
                for blob in blobs:  
                    img.draw_rect(blob.x(), blob.y(), blob.w(), blob.h(), image.COLOR_GREEN, 2)  
                    img.draw_circle(blob.cx(), blob.cy(), 5, image.COLOR_RED, -1)  
                  
                # 绘制UI  
                self.draw_ui(img)  
                display_img = img  
              
            # 处理触摸输入  
            touch_data = self.ts.read()  
            if touch_data[2]:  # 如果有触摸  
                self.handle_touch(touch_data[0], touch_data[1])  
              
            self.disp.show(display_img)  
  
# 使用示例  
if __name__ == "__main__":  
    adjuster = LABThresholdAdjuster()  
    adjuster.run()  
