#!/usr/bin/env python3
"""
温和舵机测试
使用较小的参数测试舵机控制

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def gentle_servo_test():
    """温和的舵机测试"""
    print("🌟 温和舵机测试")
    print("="*20)
    print("使用较小的参数，避免过度驱动")
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        # 温和参数
        stop_duty = 7.5
        cw_duty = 8.5      # 适中范围
        ccw_duty = 6.5     # 适中范围
        
        print(f"PWM参数: 停止={stop_duty}%, 顺时针={cw_duty}%, 逆时针={ccw_duty}%")
        
        # 温和测试序列
        test_sequence = [
            (7.5, "停止位置", 2),
            (7.7, "微小顺时针", 3),
            (7.5, "停止", 1),
            (7.3, "微小逆时针", 3),
            (7.5, "停止", 1),
            (8.0, "小幅顺时针", 3),
            (7.5, "停止", 1),
            (7.0, "小幅逆时针", 3),
            (7.5, "停止", 1),
            (8.5, "最大顺时针", 3),
            (7.5, "停止", 1),
            (6.5, "最大逆时针", 3),
            (7.5, "最终停止", 2)
        ]
        
        print("\n开始温和测试序列...")
        print("观察舵机是否能平稳控制：")
        
        for duty, description, duration in test_sequence:
            change = duty - 7.5
            print(f"\n设置: {duty:.1f}% ({description}) [变化: {change:+.1f}%]")
            servo_pwm.duty(duty)
            
            if duty > 7.5:
                print(f"  ← 预期：顺时针转动（向左）")
            elif duty < 7.5:
                print(f"  → 预期：逆时针转动（向右）")
            else:
                print(f"  ⏸ 预期：完全停止")
            
            time.sleep(duration)
        
        print("\n" + "="*20)
        print("温和测试完成！")
        
        # 分析结果
        print("\n📊 PWM变化分析：")
        print(f"最大顺时针变化: +{8.5-7.5:.1f}% (7.5% → 8.5%)")
        print(f"最大逆时针变化: {6.5-7.5:.1f}% (7.5% → 6.5%)")
        print(f"总变化范围: {8.5-6.5:.1f}% (6.5% ~ 8.5%)")
        print(f"最小变化: ±0.2% (微调控制)")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def simulate_pid_gentle():
    """模拟温和的PID控制"""
    print("\n🧮 模拟温和PID控制")
    print("="*25)
    
    # 模拟PID计算过程（使用新的温和参数）
    test_cases = [
        (50, "大误差"),
        (30, "中等误差"),
        (10, "小误差"),
        (5, "很小误差"),
        (-10, "小负误差"),
        (-30, "中等负误差")
    ]
    
    for err_x, desc in test_cases:
        print(f"\n测试: {desc} (err_x={err_x})")
        
        # PID逻辑（与main.py一致）
        horizontal_error = -err_x
        
        # 逼近控制逻辑
        if abs(horizontal_error) <= 3:  # 逼近阈值
            horizontal_speed = 0
            control_zone = "逼近区域"
        elif abs(horizontal_error) <= 12:  # 精细区域
            horizontal_speed = horizontal_error * 0.3
            horizontal_speed = max(-4, min(4, horizontal_speed))
            control_zone = "精细区域"
        elif abs(horizontal_error) <= 25:  # 减速区域
            horizontal_speed = horizontal_error * 0.4
            horizontal_speed = max(-8, min(8, horizontal_speed))
            control_zone = "减速区域"
        elif abs(horizontal_error) <= 50:  # 正常区域
            horizontal_speed = horizontal_error * 0.5
            horizontal_speed = max(-12, min(12, horizontal_speed))
            control_zone = "正常区域"
        else:  # PID区域
            horizontal_speed = horizontal_error * 0.6  # 简化的PID
            horizontal_speed = max(-16, min(16, horizontal_speed))
            control_zone = "PID区域"
        
        # 应用温和参数
        speed_multiplier = 0.4  # 新的温和倍数
        speed_scale = 1.0       # 标准缩放因子
        final_speed = horizontal_speed * speed_scale * speed_multiplier
        
        # PWM计算
        if final_speed > 0:
            duty_change = (abs(final_speed) / 100.0) * (8.5 - 7.5)  # 使用温和PWM范围
            final_duty = 7.5 + duty_change
        elif final_speed < 0:
            duty_change = (abs(final_speed) / 100.0) * (7.5 - 6.5)  # 使用温和PWM范围
            final_duty = 7.5 - duty_change
        else:
            final_duty = 7.5
        
        print(f"  控制区域: {control_zone}")
        print(f"  horizontal_error: {horizontal_error}")
        print(f"  horizontal_speed: {horizontal_speed:.1f}%")
        print(f"  final_speed: {final_speed:.1f}%")
        print(f"  final_duty: {final_duty:.3f}%")
        print(f"  PWM变化: {final_duty-7.5:+.3f}%")

def precision_test():
    """精度测试 - 测试小幅度控制"""
    print("\n🎯 精度控制测试")
    print("="*20)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        print("\n测试精细控制能力...")
        
        # 精度测试序列 - 小幅度变化
        precision_sequence = [
            (7.5, "基准位置"),
            (7.6, "微调+0.1%"),
            (7.7, "微调+0.2%"),
            (7.8, "微调+0.3%"),
            (7.5, "回到基准"),
            (7.4, "微调-0.1%"),
            (7.3, "微调-0.2%"),
            (7.2, "微调-0.3%"),
            (7.5, "最终基准")
        ]
        
        for duty, description in precision_sequence:
            change = duty - 7.5
            print(f"设置: {duty:.1f}% ({description}) [变化: {change:+.1f}%]")
            servo_pwm.duty(duty)
            time.sleep(2)
        
        print("精度测试完成")
        
    except Exception as e:
        print(f"❌ 精度测试失败: {e}")

def main():
    """主函数"""
    print("🌟 温和舵机控制测试")
    print("="*25)
    print("线路问题已解决，使用温和参数测试")
    
    try:
        gentle_servo_test()
        simulate_pid_gentle()
        precision_test()
        
        print("\n" + "="*25)
        print("🏁 所有测试完成！")
        print("\n📋 温和参数总结：")
        print("✅ 全局速度倍数: 0.4 (温和)")
        print("✅ PWM范围: 6.5% ~ 8.5% (适中)")
        print("✅ 速度缩放: 1.0 (标准)")
        print("\n这些参数应该提供：")
        print("- 平稳的舵机控制")
        print("- 精确的位置调整")
        print("- 避免过度驱动")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
