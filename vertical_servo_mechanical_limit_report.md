# 垂直舵机机械限制修正报告

## 🔧 问题说明
虽然LD-3015MG是270度舵机，但由于**机械结构限制**，垂直舵机应该限制在180度范围内，避免：
- 机械碰撞
- 过度转动
- 云台结构损坏
- 摄像头视角超出合理范围

## ✅ 已完成的修正

### 1. 垂直舵机参数重新配置
```python
# 修正前 (270度范围)
self.vertical_min_duty = 2.5      # 0度
self.vertical_max_duty = 12.5     # 270度  
self.vertical_center_duty = 7.5   # 135度中位
self.vertical_current_angle = 135 # 当前角度

# 修正后 (180度机械限制)
self.vertical_min_angle = 0       # 最小角度（上极限）
self.vertical_max_angle = 180     # 最大角度（下极限，机械限制）
self.vertical_center_angle = 90   # 中心角度（180度范围的中心）
self.vertical_current_angle = 90  # 当前角度（90度中心位置）
```

### 2. 角度转换函数优化
```python
def vertical_angle_to_duty(self, angle):
    """将角度转换为PWM占空比 (垂直舵机机械限制到180度)"""
    # 限制角度范围在机械安全范围内
    angle = max(self.vertical_min_angle, min(self.vertical_max_angle, angle))

    # 线性映射到270度舵机的PWM范围：
    # 0度 -> 2.5% (500μs)
    # 90度 -> 7.5% (1500μs) 
    # 180度 -> 12.5% (2500μs)
    # 使用180度范围映射到270度舵机的完整PWM范围
    duty = 2.5 + (angle / 180.0) * (12.5 - 2.5)
    return duty
```

### 3. 角度映射验证
```python
# 关键角度点验证
0度:   duty = 2.5 + (0/180) * 10 = 2.5%    # 500μs  (上极限)
90度:  duty = 2.5 + (90/180) * 10 = 7.5%   # 1500μs (中心位置)
180度: duty = 2.5 + (180/180) * 10 = 12.5% # 2500μs (下极限)
```

### 4. 中心位置调整
```python
# 修正前
self.set_vertical_angle(135)  # 270度舵机的中心

# 修正后  
self.set_vertical_angle(90)   # 180度范围的中心
```

### 5. 测试函数更新
```python
# 垂直舵机测试序列
test_sequence = [
    (45, "向上45度"),     # 上方向测试
    (135, "向下135度"),   # 下方向测试  
    (90, "回中心90度")    # 中心位置
]
```

## 📊 双轴舵机配置对比

| 舵机 | 硬件规格 | 机械限制 | 实际使用范围 | 中心位置 |
|------|----------|----------|-------------|----------|
| 垂直舵机 | LD-3015MG (270°) | **0°-180°** | 180度 | **90°** |
| 水平舵机 | LD-3015MG (270°) | 45°-225° | 180度 | 135° |

## 🎯 优势分析

### 1. 安全性提升
- ✅ **避免机械碰撞**: 180度限制确保安全运动
- ✅ **保护云台结构**: 防止过度转动损坏
- ✅ **合理视角范围**: 180度覆盖足够的垂直视野

### 2. 控制精度优化
- ✅ **更高精度**: 180度范围映射到完整PWM范围
- ✅ **更好线性度**: 简化的角度映射关系
- ✅ **标准化控制**: 90度中心位置更直观

### 3. 兼容性改进
- ✅ **标准180度**: 与常见舵机控制逻辑一致
- ✅ **易于理解**: 90度中心位置符合直觉
- ✅ **调试友好**: 角度范围更容易验证

## 🔧 PWM映射详解

### 垂直舵机 (0°-180°机械限制)
```
角度范围: 0° ←→ 180°
PWM范围:  2.5% ←→ 12.5%
脉宽范围: 500μs ←→ 2500μs

关键点:
- 0°   = 2.5%  = 500μs  (向上极限)
- 90°  = 7.5%  = 1500μs (水平中心)  
- 180° = 12.5% = 2500μs (向下极限)
```

### 水平舵机 (45°-225°安全范围)
```
角度范围: 45° ←→ 225° (270度舵机的安全范围)
PWM范围:  约3.2% ←→ 11.8%
脉宽范围: 约650μs ←→ 2350μs

关键点:
- 45°  ≈ 3.2%  ≈ 650μs  (左极限)
- 135° = 7.5%  = 1500μs (中心位置)
- 225° ≈ 11.8% ≈ 2350μs (右极限)
```

## 🧪 测试建议

### 1. 垂直舵机测试
```python
test_choice = "3"  # 测试垂直舵机角度控制
# 测试序列: 90° → 45° → 135° → 90°
```

### 2. 双轴方向测试
```python
test_choice = "4"  # 测试双轴舵机方向
# 验证垂直90度中心，水平135度中心
```

### 3. 完整功能测试
```python
test_choice = "5"  # 执行完整舵机功能测试
```

## 📋 机械限制的合理性

### 为什么限制垂直舵机到180度？

1. **物理结构**: 云台垂直轴通常不需要超过180度转动
2. **视觉需求**: 摄像头垂直视野180度已足够覆盖所需范围
3. **机械保护**: 避免线缆缠绕和结构碰撞
4. **控制简化**: 90度中心位置更符合直觉操作
5. **标准兼容**: 与大多数云台系统的设计标准一致

## 🎉 修正完成

垂直舵机现在已经正确配置为：
- ✅ **机械安全**: 0°-180°范围，避免碰撞
- ✅ **精确控制**: 180度映射到完整PWM范围
- ✅ **合理中心**: 90度水平中心位置
- ✅ **高精度**: 充分利用270度舵机的精度优势

这样的配置既保证了机械安全，又充分发挥了LD-3015MG舵机的高精度特性！
