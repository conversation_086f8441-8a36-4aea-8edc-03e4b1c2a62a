# K210风格PID控制说明

## 概述
基于您的K210 CODE.py代码，我重新设计了MaixCAM Pro的舵机PID控制系统，完全复制了K210的控制逻辑和参数。

## K210原始控制逻辑分析

### 原始K210代码关键部分：
```python
# K210 CODE.py 中的PID控制
ZX = (cor[0][0] + cor[1][0] + cor[2][0] + cor[3][0]) / 4  # 目标中心X
ZY = (cor[0][1] + cor[1][1] + cor[2][1] + cor[3][1]) / 4  # 目标中心Y

P = (ZX - xin) * 0    # 比例项系数为0
P2 = (ZY - yin) * 0   # 比例项系数为0

I = (ZX - xin) * 1 + I    # 积分项系数为1
I2 = (ZY - yin) * 1 + I2  # 积分项系数为1

PI = P + I     # 水平PID输出
PI2 = P2 + I2  # 垂直PID输出

# 舵机控制
func_servo(ID1, int(positx - PI), interval)   # 水平舵机
func_servo(ID2, int(posit - PI2), interval)   # 垂直舵机
```

### 控制特点：
1. **纯积分控制**：只使用积分项，比例和微分系数都为0
2. **位置控制**：通过位置值（1000-3000）控制舵机
3. **误差累积**：积分项持续累积误差，直到目标居中

## MaixCAM Pro适配实现

### 1. 舵机类型适配
- **垂直舵机**：180°位置舵机，直接使用K210的位置控制方式
- **水平舵机**：360°连续旋转舵机，通过位置偏移控制转速

### 2. PID控制实现
```python
# 计算误差（相对于画面中心）
error_x = target_x - self.center_x
error_y = target_y - self.center_y

# 水平方向PID（完全复制K210逻辑）
horizontal_p = error_x * self.horizontal_kp  # Kp=0
self.horizontal_integral += error_x * self.horizontal_ki  # Ki=1
horizontal_d = (error_x - self.horizontal_last_error) * self.horizontal_kd  # Kd=0

horizontal_output = horizontal_p + self.horizontal_integral + horizontal_d

# 垂直方向PID（完全复制K210逻辑）
vertical_p = error_y * self.vertical_kp  # Kp=0
self.vertical_integral += error_y * self.vertical_ki  # Ki=1
vertical_d = (error_y - self.vertical_last_error) * self.vertical_kd  # Kd=0

vertical_output = vertical_p + self.vertical_integral + vertical_d

# 计算新位置（复制K210逻辑）
new_horizontal_position = self.horizontal_center_position - horizontal_output
new_vertical_position = self.vertical_center_position - vertical_output
```

### 3. 位置到PWM转换
- **垂直舵机**：位置值1000-3000映射到角度0°-180°，再转换为PWM占空比
- **水平舵机**：位置偏移映射到转速，正偏移=顺时针，负偏移=逆时针

## 配置文件系统

### k210_pid_config.py 配置选项：

#### 1. K210原始配置（推荐）
```python
CURRENT_CONFIG = "k210_original"
# Kp=0.0, Ki=1.0, Kd=0.0（完全复制K210）
```

#### 2. 改进配置
```python
CURRENT_CONFIG = "improved_v1"  # 添加比例控制，减少超调
CURRENT_CONFIG = "improved_v2"  # 添加微分控制，减少震荡
CURRENT_CONFIG = "improved_v3"  # 平衡控制，适合大多数情况
```

## 使用方法

### 1. 基本使用
1. 确保 `k210_pid_config.py` 文件存在
2. 运行 `main.py`，系统会自动使用K210风格PID控制
3. 观察舵机响应，调整 `GLOBAL_SPEED_MULTIPLIER` 控制速度

### 2. 参数调整
```python
# 在 k210_pid_config.py 中修改
GLOBAL_SPEED_MULTIPLIER = 0.3  # 降低速度
CURRENT_CONFIG = "improved_v1"  # 切换配置
```

### 3. 实时调试
```python
# 在程序运行时
servo_controller.set_speed_multiplier(0.5)  # 调整速度
servo_controller.reset_pid()  # 重置积分项
```

## 调试建议

### 第一步：验证基本功能
1. 设置 `DEBUG = True`
2. 运行程序，观察控制台输出
3. 检查舵机是否能正确响应目标位置

### 第二步：调整响应速度
1. 如果响应太快：减小 `GLOBAL_SPEED_MULTIPLIER`
2. 如果响应太慢：增大 `GLOBAL_SPEED_MULTIPLIER`
3. 推荐范围：0.2-0.8

### 第三步：优化控制性能
1. 如果有超调：使用 `improved_v1` 配置
2. 如果有震荡：使用 `improved_v2` 配置
3. 如果需要平衡：使用 `improved_v3` 配置

## 与原版本的区别

### 原版本（分区域控制）
- 复杂的分区域速度控制
- 多层PID参数
- 适合精密控制

### K210版本（纯积分控制）
- 简单的积分控制
- 参数少，易调试
- 更接近原始K210行为

## 常见问题

### Q: 舵机不动或响应很慢？
A: 
1. 检查 `GLOBAL_速度_MULTIPLIER` 是否太小
2. 检查积分限制 `INTEGRAL_LIMIT` 是否太小
3. 尝试增大 `Ki` 系数

### Q: 舵机震荡或超调？
A: 
1. 减小 `GLOBAL_SPEED_MULTIPLIER`
2. 切换到 `improved_v1` 或 `improved_v2` 配置
3. 减小 `OUTPUT_LIMIT`

### Q: 如何回到原来的控制方式？
A: 
1. 删除或重命名 `k210_pid_config.py` 文件
2. 程序会自动使用原来的分区域控制方式

### Q: 如何自定义PID参数？
A: 
1. 在 `k210_pid_config.py` 中修改对应配置字典
2. 或者创建新的配置字典并设置 `CURRENT_CONFIG`

## 性能对比

| 特性 | 原版本 | K210版本 |
|------|--------|----------|
| 控制复杂度 | 高 | 低 |
| 参数数量 | 多 | 少 |
| 调试难度 | 难 | 易 |
| 响应特性 | 精密 | 简单 |
| 稳定性 | 高 | 中等 |
| 适用场景 | 精密控制 | 快速部署 |

## 推荐设置

### 初学者推荐
```python
CURRENT_CONFIG = "k210_original"
GLOBAL_SPEED_MULTIPLIER = 0.3
```

### 性能优化推荐
```python
CURRENT_CONFIG = "improved_v3"
GLOBAL_SPEED_MULTIPLIER = 0.5
```

### 高精度推荐
```python
CURRENT_CONFIG = "improved_v2"
GLOBAL_SPEED_MULTIPLIER = 0.2
```
