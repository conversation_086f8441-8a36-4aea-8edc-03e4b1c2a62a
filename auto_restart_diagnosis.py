#!/usr/bin/env python3
"""
自动重启问题诊断工具
分析和解决MaixCAM Pro运行时自动重启的问题

@author: AI Assistant
@date: 2025.8.2
"""

import gc
import time
import psutil
import os
from maix import camera, display, app

class AutoRestartDiagnosis:
    def __init__(self):
        """初始化诊断工具"""
        print("🔍 自动重启问题诊断工具")
        print("=" * 40)
        
        self.start_time = time.time()
        self.frame_count = 0
        self.memory_samples = []
        self.error_log = []
        
    def check_memory_usage(self):
        """检查内存使用情况"""
        try:
            # 获取内存信息
            memory_info = psutil.virtual_memory()
            memory_percent = memory_info.percent
            memory_available = memory_info.available / (1024 * 1024)  # MB
            
            # Python垃圾回收信息
            gc_counts = gc.get_count()
            
            sample = {
                'time': time.time() - self.start_time,
                'memory_percent': memory_percent,
                'memory_available_mb': memory_available,
                'gc_counts': gc_counts
            }
            
            self.memory_samples.append(sample)
            
            # 保留最近100个样本
            if len(self.memory_samples) > 100:
                self.memory_samples.pop(0)
            
            return sample
            
        except Exception as e:
            self.error_log.append(f"内存检查失败: {e}")
            return None
    
    def check_temperature(self):
        """检查CPU温度（如果可用）"""
        try:
            # 尝试读取CPU温度
            temp_files = [
                '/sys/class/thermal/thermal_zone0/temp',
                '/sys/class/thermal/thermal_zone1/temp'
            ]
            
            temperatures = []
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    with open(temp_file, 'r') as f:
                        temp = int(f.read().strip()) / 1000.0  # 转换为摄氏度
                        temperatures.append(temp)
            
            return temperatures
            
        except Exception as e:
            self.error_log.append(f"温度检查失败: {e}")
            return []
    
    def analyze_memory_trend(self):
        """分析内存使用趋势"""
        if len(self.memory_samples) < 10:
            return "数据不足"
        
        # 计算内存使用趋势
        recent_samples = self.memory_samples[-10:]
        first_memory = recent_samples[0]['memory_percent']
        last_memory = recent_samples[-1]['memory_percent']
        
        trend = last_memory - first_memory
        
        if trend > 5:
            return "内存泄漏风险"
        elif trend > 2:
            return "内存使用上升"
        elif trend < -2:
            return "内存使用下降"
        else:
            return "内存使用稳定"
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        try:
            before_count = gc.get_count()
            collected = gc.collect()
            after_count = gc.get_count()
            
            print(f"🗑️ 垃圾回收: 回收了{collected}个对象")
            print(f"   回收前: {before_count}")
            print(f"   回收后: {after_count}")
            
            return collected
            
        except Exception as e:
            self.error_log.append(f"垃圾回收失败: {e}")
            return 0
    
    def run_stability_test(self, duration_minutes=5):
        """运行稳定性测试"""
        print(f"\n🧪 运行{duration_minutes}分钟稳定性测试")
        print("监控内存、温度和错误...")
        
        try:
            # 初始化摄像头和显示
            cam = camera.Camera(448, 448)
            disp = display.Display()
            
            test_start = time.time()
            test_duration = duration_minutes * 60
            
            while not app.need_exit() and (time.time() - test_start) < test_duration:
                try:
                    # 读取图像
                    img = cam.read()
                    self.frame_count += 1
                    
                    # 检查内存
                    memory_info = self.check_memory_usage()
                    
                    # 检查温度
                    temperatures = self.check_temperature()
                    
                    # 每100帧强制垃圾回收
                    if self.frame_count % 100 == 0:
                        collected = self.force_garbage_collection()
                        
                        # 显示状态
                        elapsed = time.time() - test_start
                        remaining = test_duration - elapsed
                        
                        print(f"\n📊 测试进度: {elapsed/60:.1f}/{duration_minutes}分钟")
                        print(f"   帧数: {self.frame_count}")
                        
                        if memory_info:
                            print(f"   内存使用: {memory_info['memory_percent']:.1f}%")
                            print(f"   可用内存: {memory_info['memory_available_mb']:.1f}MB")
                        
                        if temperatures:
                            print(f"   CPU温度: {temperatures[0]:.1f}°C")
                        
                        trend = self.analyze_memory_trend()
                        print(f"   内存趋势: {trend}")
                        
                        if collected > 0:
                            print(f"   垃圾回收: {collected}个对象")
                    
                    # 在图像上显示诊断信息
                    if memory_info:
                        img.draw_string(10, 10, f"Memory: {memory_info['memory_percent']:.1f}%", 
                                       color=(255, 255, 255), scale=1.5)
                        img.draw_string(10, 35, f"Frame: {self.frame_count}", 
                                       color=(255, 255, 255), scale=1.2)
                        
                        if temperatures:
                            img.draw_string(10, 60, f"Temp: {temperatures[0]:.1f}C", 
                                           color=(255, 255, 255), scale=1.2)
                    
                    # 显示图像
                    disp.show(img)
                    
                    # 检查内存警告
                    if memory_info and memory_info['memory_percent'] > 85:
                        print("⚠️ 内存使用过高，强制垃圾回收...")
                        self.force_garbage_collection()
                    
                    # 检查温度警告
                    if temperatures and max(temperatures) > 70:
                        print("⚠️ CPU温度过高，可能导致重启...")
                    
                    time.sleep(0.033)  # 约30fps
                    
                except Exception as e:
                    self.error_log.append(f"帧处理错误: {e}")
                    print(f"❌ 帧处理错误: {e}")
                    
                    # 尝试恢复
                    try:
                        self.force_garbage_collection()
                        time.sleep(0.1)
                    except:
                        pass
            
            print(f"\n✅ 稳定性测试完成")
            self.generate_report()
            
        except Exception as e:
            print(f"❌ 稳定性测试失败: {e}")
            self.error_log.append(f"测试失败: {e}")
    
    def generate_report(self):
        """生成诊断报告"""
        print(f"\n📋 诊断报告")
        print("=" * 40)
        
        # 基本统计
        total_time = time.time() - self.start_time
        avg_fps = self.frame_count / total_time if total_time > 0 else 0
        
        print(f"运行时间: {total_time:.1f}秒")
        print(f"处理帧数: {self.frame_count}")
        print(f"平均帧率: {avg_fps:.1f}fps")
        
        # 内存分析
        if self.memory_samples:
            memory_values = [s['memory_percent'] for s in self.memory_samples]
            min_memory = min(memory_values)
            max_memory = max(memory_values)
            avg_memory = sum(memory_values) / len(memory_values)
            
            print(f"\n💾 内存使用:")
            print(f"   最小: {min_memory:.1f}%")
            print(f"   最大: {max_memory:.1f}%")
            print(f"   平均: {avg_memory:.1f}%")
            print(f"   趋势: {self.analyze_memory_trend()}")
        
        # 错误统计
        if self.error_log:
            print(f"\n❌ 错误记录 ({len(self.error_log)}个):")
            for i, error in enumerate(self.error_log[-5:], 1):  # 显示最近5个错误
                print(f"   {i}. {error}")
        else:
            print(f"\n✅ 无错误记录")
        
        # 建议
        print(f"\n💡 建议:")
        
        if self.memory_samples:
            latest_memory = self.memory_samples[-1]['memory_percent']
            if latest_memory > 80:
                print("   ⚠️ 内存使用过高，建议:")
                print("     - 增加垃圾回收频率")
                print("     - 减少图像处理复杂度")
                print("     - 检查内存泄漏")
        
        if len(self.error_log) > 10:
            print("   ⚠️ 错误频繁，建议:")
            print("     - 检查硬件连接")
            print("     - 增加异常处理")
            print("     - 降低处理频率")
        
        if avg_fps < 20:
            print("   ⚠️ 帧率较低，建议:")
            print("     - 优化图像处理算法")
            print("     - 减少调试输出")
            print("     - 检查CPU负载")

def main():
    """主函数"""
    print("🔍 MaixCAM Pro 自动重启诊断工具")
    print("=" * 50)
    
    diagnosis = AutoRestartDiagnosis()
    
    try:
        # 运行稳定性测试
        diagnosis.run_stability_test(duration_minutes=3)  # 3分钟测试
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        diagnosis.generate_report()
    
    except Exception as e:
        print(f"\n❌ 诊断工具异常: {e}")
        diagnosis.error_log.append(f"诊断工具异常: {e}")
        diagnosis.generate_report()

if __name__ == "__main__":
    main()
