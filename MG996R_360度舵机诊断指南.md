# MG996R 360度舵机不动问题诊断指南

## 🚨 问题描述
MG996R 360度舵机完全不动，垂直180度舵机工作正常。

## 🔍 已修复的关键问题

### 1. PWM占空比范围错误 ❌→✅
**问题**：之前使用7.0%-8.0%，范围太小
```python
# 错误设置
self.horizontal_cw_duty = 8.0      # 只有0.5%变化
self.horizontal_ccw_duty = 7.0     # 只有0.5%变化
```

**修复**：使用MG996R标准范围5.0%-10.0%
```python
# 正确设置
self.horizontal_cw_duty = 10.0     # 2.5%变化范围
self.horizontal_ccw_duty = 5.0     # 2.5%变化范围
```

### 2. 额外的0.5倍缩放 ❌→✅
**问题**：PWM变化量被额外缩小50%
```python
# 错误：额外缩小
duty_change = (actual_speed / 100.0) * duty_range * 0.5
```

**修复**：移除额外缩放
```python
# 正确：直接计算
duty_change = (abs(speed) / 100.0) * duty_range
```

### 3. 复杂的速度映射 ❌→✅
**问题**：复杂的速度映射可能引入错误
**修复**：简化为直接线性映射

## 🧪 新增诊断工具

### 1. PWM直接测试
```python
servo_controller.test_horizontal_pwm_direct()
```
直接测试PWM占空比：5.0%, 6.0%, 7.0%, 7.5%, 8.0%, 9.0%, 10.0%

### 2. 详细初始化检查
现在会显示每个PWM通道的初始化状态

### 3. 速度缩放过程显示
显示速度从原始值到最终PWM的完整过程

## 📊 PWM参数对比

| 参数 | 修复前 | 修复后 | 说明 |
|------|--------|--------|------|
| 停止占空比 | 7.5% | 7.5% | 不变 |
| 顺时针占空比 | 8.0% | 10.0% | 增大2.5倍 |
| 逆时针占空比 | 7.0% | 5.0% | 增大2.5倍 |
| 变化范围 | ±0.5% | ±2.5% | 增大5倍 |
| 额外缩放 | ×0.5 | ×1.0 | 移除缩放 |

## 🔧 诊断步骤

### 步骤1: 启用调试模式
```python
DEBUG = True
```

### 步骤2: 观察初始化信息
```
正在初始化PWM...
✓ 垂直舵机PWM7初始化成功
✓ 水平舵机PWM6初始化成功
舵机PWM初始化完成
```

### 步骤3: 运行PWM直接测试
观察每个占空比是否设置成功：
```
🔧 水平舵机PWM直接测试
测试PWM占空比: 5.0%
  ✓ 设置成功
测试PWM占空比: 10.0%
  ✓ 设置成功
```

### 步骤4: 观察舵机响应
- **5.0%**: 应该逆时针转动
- **7.5%**: 应该停止
- **10.0%**: 应该顺时针转动

## ⚡ 硬件检查清单

如果PWM设置成功但舵机仍不动：

### 1. 电源检查 ⚡
- [ ] 舵机是否有独立5V电源
- [ ] 电源电流是否足够（MG996R需要1.5A+）
- [ ] 电源电压是否稳定（4.8V-6V）
- [ ] 电源线是否接触良好

### 2. 信号线检查 📡
- [ ] PWM信号线是否连接到A18引脚
- [ ] 信号线是否有断路
- [ ] 地线是否与MaixCAM Pro共地
- [ ] 信号线是否有干扰

### 3. 舵机检查 🔧
- [ ] 舵机型号是否确实是360度版本
- [ ] 手动转动舵机轴是否有阻力
- [ ] 舵机是否有机械卡死
- [ ] 更换已知正常的舵机测试

### 4. 引脚检查 📍
- [ ] A18引脚是否支持PWM6
- [ ] 引脚是否有物理损坏
- [ ] 尝试更换其他PWM引脚

## 🎯 预期结果

修复后应该看到：
```
速度缩放: 20.00 → 12.00 (缩放因子: 1.0 × 0.6 = 0.600)
逼近控制: 原始err_x=50.0, H_err=-50.0, H_speed=-12.0
水平速度设置为: -12.0%, 占空比: 6.200%
  → 目标在画面右侧，舵机速度=-12.0% (逆时针)
```

**关键指标**：
- 占空比应该在5.0%-10.0%范围内变化
- 不同速度应该产生明显不同的占空比
- 舵机应该有明显的转动

## 🔄 MG996R 360度舵机特性

### 标准PWM参数：
- **频率**: 50Hz (20ms周期)
- **脉宽范围**: 1.0ms-2.0ms
- **占空比范围**: 5.0%-10.0%
- **停止脉宽**: 1.5ms (7.5%占空比)

### 转向特性：
- **1.0ms (5.0%)**: 最大逆时针
- **1.5ms (7.5%)**: 停止
- **2.0ms (10.0%)**: 最大顺时针

### 死区特性：
- 通常在1.45ms-1.55ms之间有死区
- 对应占空比约7.25%-7.75%

## 🚨 常见错误

### 1. 混淆180度和360度舵机
- 180度舵机：位置控制，有角度反馈
- 360度舵机：速度控制，无角度反馈

### 2. PWM范围设置错误
- 不能使用180度舵机的PWM范围
- 必须使用1.0ms-2.0ms的标准范围

### 3. 电源不足
- 360度舵机通常比180度舵机耗电更大
- 需要足够的电流供应

## 📝 最终检查

如果所有软件修复都完成但舵机仍不动：

1. **确认舵机型号**：是否真的是MG996R 360度版本
2. **更换舵机**：用已知正常的360度舵机测试
3. **更换引脚**：尝试其他PWM引脚
4. **示波器检查**：测量PWM信号是否正确输出
5. **电源测试**：使用万用表测量电源电压和电流

记住：**硬件问题比软件问题更难诊断，但通过系统性排查一定能找到原因！**
