#!/usr/bin/env python3
"""
快速偏移调节工具
简化版本，专门用于快速调节offset_x和offset_y
参考main.py的"强制设置不同速度，观察舵机是否转动"风格

@author: AI Assistant  
@date: 2025.8.2
"""

from maix import camera, display, image, time, app
import math

class QuickOffsetTuner:
    def __init__(self):
        """初始化快速调节工具"""
        print("⚡ 快速偏移调节工具")
        print("强制设置不同偏移值，观察补偿效果...")
        print("=" * 40)
        
        # 初始化硬件
        self.disp = display.Display()
        self.cam = camera.Camera(448, 448, image.Format.FMT_RGB888)  # 使用main.py的分辨率
        
        # 当前偏移值（从main.py复制）
        self.offset_x = -12
        self.offset_y = -12
        
        # 画面中心
        self.center_x = self.cam.width() // 2   # 224
        self.center_y = self.cam.height() // 2  # 224
        
        # 模拟目标位置（可以调整）
        self.target_x = self.center_x + 30  # 右侧30像素
        self.target_y = self.center_y - 20  # 上方20像素
        
        print(f"✅ 初始化完成")
        print(f"   画面: {self.cam.width()}x{self.cam.height()}")
        print(f"   中心: ({self.center_x}, {self.center_y})")
        print(f"   目标: ({self.target_x}, {self.target_y})")
        print(f"   初始偏移: X={self.offset_x}, Y={self.offset_y}")
    
    def calculate_errors(self):
        """计算误差（完全模拟main.py的计算方式）"""
        # 原始误差（不含偏移补偿）
        raw_err_x = self.target_x - self.center_x
        raw_err_y = self.target_y - self.center_y
        
        # 偏移补偿后的误差（main.py的计算方式）
        compensated_err_x = self.target_x - (self.center_x + self.offset_x)
        compensated_err_y = self.target_y - (self.center_y + self.offset_y)
        
        return raw_err_x, raw_err_y, compensated_err_x, compensated_err_y
    
    def draw_visualization(self, img):
        """绘制可视化界面"""
        # 计算误差
        raw_err_x, raw_err_y, comp_err_x, comp_err_y = self.calculate_errors()
        
        # 绘制画面中心（蓝色十字）
        img.draw_line(self.center_x - 15, self.center_y, self.center_x + 15, self.center_y, 
                     image.COLOR_BLUE, thickness=3)
        img.draw_line(self.center_x, self.center_y - 15, self.center_x, self.center_y + 15, 
                     image.COLOR_BLUE, thickness=3)
        
        # 绘制补偿后的中心（绿色十字）
        comp_center_x = self.center_x + self.offset_x
        comp_center_y = self.center_y + self.offset_y
        img.draw_line(comp_center_x - 10, comp_center_y, comp_center_x + 10, comp_center_y, 
                     image.COLOR_GREEN, thickness=2)
        img.draw_line(comp_center_x, comp_center_y - 10, comp_center_x, comp_center_y + 10, 
                     image.COLOR_GREEN, thickness=2)
        
        # 绘制目标（红色圆圈）
        img.draw_circle(self.target_x, self.target_y, 8, image.COLOR_RED, thickness=3)
        
        # 绘制误差线
        img.draw_line(self.center_x, self.center_y, self.target_x, self.target_y, 
                     image.COLOR_BLUE, thickness=2)  # 原始误差线
        img.draw_line(comp_center_x, comp_center_y, self.target_x, self.target_y, 
                     image.COLOR_GREEN, thickness=3)  # 补偿后误差线
        
        # 显示数值信息
        y_pos = 10
        img.draw_string(10, y_pos, f"Offset: X={self.offset_x:+d}, Y={self.offset_y:+d}", 
                       image.COLOR_WHITE, scale=1.5, thickness=2)
        y_pos += 25
        
        img.draw_string(10, y_pos, f"Raw: X={raw_err_x:+.0f}, Y={raw_err_y:+.0f}", 
                       image.COLOR_BLUE, scale=1.2, thickness=2)
        y_pos += 25
        
        img.draw_string(10, y_pos, f"Comp: X={comp_err_x:+.0f}, Y={comp_err_y:+.0f}", 
                       image.COLOR_GREEN, scale=1.2, thickness=2)
        y_pos += 25
        
        # 误差大小
        raw_mag = math.sqrt(raw_err_x**2 + raw_err_y**2)
        comp_mag = math.sqrt(comp_err_x**2 + comp_err_y**2)
        img.draw_string(10, y_pos, f"Error: {raw_mag:.0f} -> {comp_mag:.0f}", 
                       image.COLOR_YELLOW, scale=1.2, thickness=2)
        
        # 显示图例
        legend_y = img.height() - 80
        img.draw_string(10, legend_y, "Blue: Original center", image.COLOR_BLUE, scale=1.0)
        img.draw_string(10, legend_y + 20, "Green: Compensated center", image.COLOR_GREEN, scale=1.0)
        img.draw_string(10, legend_y + 40, "Red: Target position", image.COLOR_RED, scale=1.0)
    
    def test_offset_values(self):
        """强制设置不同偏移值，观察补偿效果"""
        print("\n🧪 强制设置不同偏移值，观察补偿效果...")
        
        # 测试序列（参考main.py的测试风格）
        test_offsets = [
            (0, 0, "无偏移基准"),
            (-5, -5, "小幅偏移"),
            (-10, -10, "中等偏移"),
            (-15, -15, "较大偏移"),
            (-20, -20, "大幅偏移"),
            (-12, -12, "推荐偏移"),
            (-10, 0, "仅X轴偏移"),
            (0, -10, "仅Y轴偏移"),
            (5, 5, "反向偏移"),
            (-12, -12, "最终推荐值")
        ]
        
        for offset_x, offset_y, description in test_offsets:
            print(f"\n📍 强制设置偏移: X={offset_x:+d}, Y={offset_y:+d} ({description})")
            
            # 设置偏移值
            self.offset_x = offset_x
            self.offset_y = offset_y
            
            # 计算误差
            raw_err_x, raw_err_y, comp_err_x, comp_err_y = self.calculate_errors()
            raw_magnitude = math.sqrt(raw_err_x**2 + raw_err_y**2)
            comp_magnitude = math.sqrt(comp_err_x**2 + comp_err_y**2)
            
            print(f"   原始误差: ({raw_err_x:+.0f}, {raw_err_y:+.0f}), 大小: {raw_magnitude:.1f}")
            print(f"   补偿误差: ({comp_err_x:+.0f}, {comp_err_y:+.0f}), 大小: {comp_magnitude:.1f}")
            
            if raw_magnitude > 0:
                improvement = (raw_magnitude - comp_magnitude) / raw_magnitude * 100
                print(f"   改善程度: {improvement:+.1f}%")
            
            # 显示3秒，让用户观察效果
            print(f"   观察补偿效果中...")
            for i in range(90):  # 3秒 @ 30fps
                img = self.cam.read()
                self.draw_visualization(img)
                
                # 在图像上显示当前测试信息
                img.draw_string(10, img.height() - 120, f"Testing: {description}", 
                               image.COLOR_YELLOW, scale=1.3, thickness=2)
                img.draw_string(10, img.height() - 100, f"Progress: {i//3 + 1}/30", 
                               image.COLOR_WHITE, scale=1.0, thickness=1)
                
                self.disp.show(img)
                time.sleep_ms(33)
        
        print("\n✅ 偏移测试完成")
    
    def run_interactive_mode(self):
        """运行交互模式"""
        print("\n🎮 交互模式 - 实时观察偏移效果")
        print("强制显示当前偏移设置，观察实时补偿效果...")
        
        frame_count = 0
        
        try:
            while not app.need_exit():
                img = self.cam.read()
                self.draw_visualization(img)
                
                # 每5秒自动微调一次偏移值（演示用）
                if frame_count % 150 == 0:  # 5秒
                    # 微调偏移值
                    if frame_count % 300 == 0:
                        self.offset_x += 1
                        print(f"🔧 微调offset_x: {self.offset_x}")
                    else:
                        self.offset_y += 1
                        print(f"🔧 微调offset_y: {self.offset_y}")
                
                self.disp.show(img)
                frame_count += 1
                time.sleep_ms(33)
                
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断")
    
    def generate_code_output(self):
        """生成代码输出"""
        print(f"\n📋 推荐的偏移值:")
        print(f"   offset_x = {self.offset_x}  # X轴偏移补偿（像素）")
        print(f"   offset_y = {self.offset_y}  # Y轴偏移补偿（像素）")
        
        print(f"\n💻 复制到main.py中:")
        print(f"# 偏移补偿参数（参考111.py，用于修正打靶位置）")
        print(f"offset_x = {self.offset_x}                             # X轴偏移补偿（像素）")
        print(f"offset_y = {self.offset_y}                             # Y轴偏移补偿（像素）")
        
        print(f"\n🎯 使用说明:")
        print(f"   如果打靶偏右 → 减小offset_x值（当前: {self.offset_x}）")
        print(f"   如果打靶偏左 → 增大offset_x值")
        print(f"   如果打靶偏下 → 减小offset_y值（当前: {self.offset_y}）")
        print(f"   如果打靶偏上 → 增大offset_y值")

def main():
    """主函数"""
    print("⚡ 快速偏移调节工具")
    print("参考main.py: '强制设置不同速度，观察舵机是否转动'")
    print("=" * 50)
    
    try:
        # 创建调节工具
        tuner = QuickOffsetTuner()
        
        # 运行偏移测试
        tuner.test_offset_values()
        
        # 生成代码输出
        tuner.generate_code_output()
        
        # 可选：运行交互模式
        print(f"\n❓ 是否运行交互模式？（按Ctrl+C退出）")
        tuner.run_interactive_mode()
        
    except Exception as e:
        print(f"❌ 工具运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
