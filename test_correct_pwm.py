#!/usr/bin/env python3
"""
测试正确的PWM逻辑
验证逆时针PWM从7.15%向下减小的逻辑

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def test_correct_pwm_logic():
    """测试正确的PWM控制逻辑"""
    print("🔧 测试正确的PWM控制逻辑")
    print("="*30)
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.65, enable=True)
        print("✓ PWM初始化成功")
        
        # 正确的PWM参数
        horizontal_stop_duty = 7.65    # 停止位置
        horizontal_cw_duty = 9.0       # 顺时针最大值
        horizontal_ccw_duty = 5.0      # 逆时针最小值
        global_speed_multiplier = 1.5
        
        print(f"PWM参数:")
        print(f"  停止位置: {horizontal_stop_duty}%")
        print(f"  顺时针范围: {horizontal_stop_duty}% → {horizontal_cw_duty}%")
        print(f"  逆时针范围: {horizontal_stop_duty}% → {horizontal_ccw_duty}%")
        
        def test_speed_with_correct_logic(speed, description):
            """使用正确逻辑测试速度"""
            print(f"\n{description}: {speed}%")
            
            # 应用缩放
            final_speed = speed * global_speed_multiplier
            
            # 正确的PWM计算逻辑
            if final_speed == 0:
                duty = horizontal_stop_duty
                direction = "停止"
            elif final_speed > 0:
                # 顺时针：从停止位置向上增加
                duty_range = horizontal_cw_duty - horizontal_stop_duty  # 9.0 - 7.65 = 1.35
                duty_change = (abs(final_speed) / 100.0) * duty_range
                duty = horizontal_stop_duty + duty_change  # 7.65 + 变化量
                duty = min(duty, horizontal_cw_duty)  # 不超过9.0
                direction = "顺时针"
            else:
                # 逆时针：从停止位置向下减少
                duty_range = horizontal_stop_duty - horizontal_ccw_duty  # 7.65 - 5.0 = 2.65
                duty_change = (abs(final_speed) / 100.0) * duty_range
                duty = horizontal_stop_duty - duty_change  # 7.65 - 变化量
                duty = max(duty, horizontal_ccw_duty)  # 不小于5.0
                direction = "逆时针"
            
            change = duty - horizontal_stop_duty
            
            print(f"  最终速度: {final_speed:.1f}%")
            print(f"  PWM占空比: {duty:.3f}% (变化: {change:+.3f}%)")
            print(f"  预期方向: {direction}")
            
            # 设置PWM并观察
            servo_pwm.duty(duty)
            time.sleep(3)
            
            return duty
        
        # 测试不同速度
        test_cases = [
            (0, "停止测试"),
            (5, "小速度顺时针"),
            (-5, "小速度逆时针"),
            (10, "中速度顺时针"),
            (-10, "中速度逆时针"),
            (20, "高速度顺时针"),
            (-20, "高速度逆时针"),
            (30, "很高速度顺时针"),
            (-30, "很高速度逆时针"),
            (0, "最终停止")
        ]
        
        print("\n开始正确逻辑测试...")
        
        for speed, description in test_cases:
            duty = test_speed_with_correct_logic(speed, description)
            
            # 回到停止位置
            if speed != 0:
                servo_pwm.duty(horizontal_stop_duty)
                time.sleep(1)
        
        print("\n" + "="*30)
        print("正确逻辑测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def show_pwm_calculation_examples():
    """显示PWM计算示例"""
    print("\n📊 PWM计算示例")
    print("="*20)
    
    # 参数
    stop = 7.65
    cw_max = 9.0
    ccw_min = 5.0
    multiplier = 1.5
    
    print(f"参数: 停止={stop}, 顺时针最大={cw_max}, 逆时针最小={ccw_min}")
    print(f"全局倍数: {multiplier}")
    
    print(f"\n计算示例:")
    print(f"{'速度':<8} {'最终速度':<10} {'PWM占空比':<12} {'变化量':<10} {'方向'}")
    print("-" * 50)
    
    test_speeds = [0, 5, -5, 10, -10, 20, -20, 30, -30]
    
    for speed in test_speeds:
        final_speed = speed * multiplier
        
        if final_speed == 0:
            duty = stop
            direction = "停止"
        elif final_speed > 0:
            duty_range = cw_max - stop  # 1.35
            duty_change = (abs(final_speed) / 100.0) * duty_range
            duty = stop + duty_change
            duty = min(duty, cw_max)
            direction = "顺时针"
        else:
            duty_range = stop - ccw_min  # 2.65
            duty_change = (abs(final_speed) / 100.0) * duty_range
            duty = stop - duty_change
            duty = max(duty, ccw_min)
            direction = "逆时针"
        
        change = duty - stop
        
        print(f"{speed:<8} {final_speed:<10.1f} {duty:<12.3f} {change:<10.3f} {direction}")

def main():
    """主函数"""
    print("🔧 正确PWM逻辑测试")
    print("="*25)
    
    try:
        print("修正后的PWM逻辑：")
        print("- 停止位置: 7.65%")
        print("- 顺时针: 7.65% → 9.0% (PWM增大)")
        print("- 逆时针: 7.65% → 5.0% (PWM减小)")
        
        # 显示计算示例
        show_pwm_calculation_examples()
        
        # 实际测试
        test_correct_pwm_logic()
        
        print("\n" + "="*25)
        print("🏁 测试完成！")
        print("\n📋 如果测试成功：")
        print("✅ 顺时针应该正常工作")
        print("✅ 逆时针应该正常工作")
        print("✅ 停止应该精确")
        
        print("\n🚀 现在可以测试main.py了！")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
