#!/usr/bin/env python3
"""
快速水平舵机初始化代码
简化版本，专门用于快速初始化和基本测试

使用方法：
1. 直接运行此脚本进行快速初始化
2. 或在其他代码中导入使用

@author: AI Assistant
@date: 2025.8.2
"""

from maix import pwm, pinmap, time

def init_horizontal_servo():
    """
    快速初始化水平舵机
    
    Returns:
        tuple: (pwm_object, success_flag)
    """
    print("🔧 快速初始化水平舵机...")
    
    try:
        # 配置引脚
        print("📌 配置A18引脚为PWM6")
        pinmap.set_pin_function("A18", "PWM6")
        
        # 初始化PWM（50Hz，初始禁用）
        print("⚡ 初始化PWM6: 50Hz, 禁用状态")
        horizontal_pwm = pwm.PWM(6, freq=50, duty=0, enable=False)
        
        print("✅ 水平舵机初始化成功")
        print("📋 舵机信息:")
        print("   引脚: A18 -> PWM6")
        print("   型号: LD-3015MG (270度位置舵机)")
        print("   频率: 50Hz")
        print("   状态: 禁用（可手动搬动）")
        print("   控制范围: 45° - 225°")
        print("   中心位置: 135°")
        
        return horizontal_pwm, True
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        print("请检查:")
        print("1. A18引脚连接是否正确")
        print("2. 舵机电源是否正常")
        print("3. MaixCAM Pro是否支持PWM6")
        return None, False

def set_servo_angle(pwm_obj, angle):
    """
    设置舵机角度
    
    Args:
        pwm_obj: PWM对象
        angle: 目标角度 (45-225度)
    
    Returns:
        bool: 设置是否成功
    """
    if pwm_obj is None:
        print("❌ PWM对象无效")
        return False
    
    try:
        # 限制角度范围
        angle = max(45, min(225, angle))
        
        # 角度转PWM占空比
        # 45度 -> 2.5%, 135度 -> 7.5%, 225度 -> 12.5%
        duty = 2.5 + ((angle - 45) / 180.0) * (12.5 - 2.5)
        
        # 启用PWM并设置占空比
        pwm_obj.enable()
        pwm_obj.duty(duty)
        
        print(f"✅ 角度设置: {angle}°, PWM: {duty:.2f}%")
        return True
        
    except Exception as e:
        print(f"❌ 设置角度失败: {e}")
        return False

def center_servo(pwm_obj):
    """舵机回中心位置 (135度)"""
    print("🎯 舵机回中心位置...")
    return set_servo_angle(pwm_obj, 135)

def disable_servo(pwm_obj):
    """禁用舵机"""
    if pwm_obj is None:
        return False
    
    try:
        pwm_obj.disable()
        print("🔓 舵机已禁用，可手动搬动")
        return True
    except Exception as e:
        print(f"❌ 禁用失败: {e}")
        return False

def quick_test(pwm_obj):
    """快速功能测试"""
    print("\n🧪 快速功能测试")
    print("=" * 25)
    
    if pwm_obj is None:
        print("❌ PWM对象无效，无法测试")
        return False
    
    # 简单的角度测试序列
    test_angles = [135, 90, 135, 180, 135]  # 中心 -> 左 -> 中心 -> 右 -> 中心
    test_names = ["中心", "左45°", "中心", "右45°", "中心"]
    
    for angle, name in zip(test_angles, test_names):
        print(f"📍 {name}: {angle}°")
        if set_servo_angle(pwm_obj, angle):
            time.sleep(1.5)
        else:
            print("❌ 测试失败")
            return False
    
    print("✅ 快速测试完成")
    return True

def main():
    """主函数 - 演示使用方法"""
    print("🚀 快速水平舵机初始化")
    print("=" * 30)
    
    # 初始化舵机
    servo_pwm, success = init_horizontal_servo()
    
    if not success:
        print("❌ 初始化失败，程序退出")
        return
    
    try:
        # 询问是否进行测试
        print("\n是否进行快速测试？")
        choice = input("输入 'y' 进行测试，其他键跳过: ").strip().lower()
        
        if choice == 'y':
            # 进行快速测试
            quick_test(servo_pwm)
            
            # 询问是否保持启用状态
            print("\n是否保持舵机启用状态？")
            choice = input("输入 'y' 保持启用，其他键禁用: ").strip().lower()
            
            if choice != 'y':
                disable_servo(servo_pwm)
        else:
            print("⏸️ 跳过测试，舵机保持禁用状态")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    
    except Exception as e:
        print(f"❌ 运行错误: {e}")
    
    finally:
        print("\n📋 使用说明:")
        print("在其他代码中使用此模块:")
        print("```python")
        print("from quick_horizontal_servo_init import init_horizontal_servo, set_servo_angle")
        print("servo_pwm, success = init_horizontal_servo()")
        print("if success:")
        print("    set_servo_angle(servo_pwm, 135)  # 设置到中心位置")
        print("```")
        print("\n👋 程序结束")

# 如果作为模块导入，提供简单的初始化函数
def simple_init():
    """
    最简单的初始化函数
    返回已初始化的PWM对象
    """
    servo_pwm, success = init_horizontal_servo()
    if success:
        return servo_pwm
    else:
        return None

if __name__ == "__main__":
    main()
