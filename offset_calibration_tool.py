#!/usr/bin/env python3
"""
偏移补偿校准工具
脱机调节offset_x和offset_y参数
在屏幕上实时显示和调节偏移值

参考main.py的强制设置风格
适用于MaixCAM Pro环境

@author: AI Assistant
@date: 2025.8.2
"""

from maix import camera, display, image, time, app
import math

class OffsetCalibrationTool:
    def __init__(self):
        """初始化偏移校准工具"""
        print("🎯 偏移补偿校准工具")
        print("=" * 40)
        
        # 初始化显示和摄像头
        self.disp = display.Display()
        self.cam = camera.Camera(640, 480, image.Format.FMT_RGB888)
        
        # 当前偏移值
        self.offset_x = -12  # 初始值
        self.offset_y = -12  # 初始值
        
        # 调节步长
        self.step_size = 1   # 每次调节的步长
        
        # 画面中心
        self.center_x = self.cam.width() // 2
        self.center_y = self.cam.height() // 2
        
        # 模拟目标位置（可以手动设置或检测）
        self.target_x = self.center_x + 50  # 模拟目标在右侧50像素
        self.target_y = self.center_y - 30  # 模拟目标在上方30像素
        
        # 显示模式
        self.show_help = True
        self.show_grid = True
        
        print("✅ 校准工具初始化完成")
        print("📋 初始设置:")
        print(f"   画面尺寸: {self.cam.width()}x{self.cam.height()}")
        print(f"   画面中心: ({self.center_x}, {self.center_y})")
        print(f"   初始偏移: X={self.offset_x}, Y={self.offset_y}")
        print(f"   模拟目标: ({self.target_x}, {self.target_y})")
        
    def calculate_error(self):
        """计算误差（模拟main.py中的误差计算）"""
        # 原始误差（不含偏移补偿）
        raw_err_x = self.target_x - self.center_x
        raw_err_y = self.target_y - self.center_y
        
        # 偏移补偿后的误差
        compensated_err_x = self.target_x - (self.center_x + self.offset_x)
        compensated_err_y = self.target_y - (self.center_y + self.offset_y)
        
        return raw_err_x, raw_err_y, compensated_err_x, compensated_err_y
    
    def draw_interface(self, img):
        """绘制用户界面"""
        # 计算误差
        raw_err_x, raw_err_y, comp_err_x, comp_err_y = self.calculate_error()
        
        # 绘制网格（如果启用）
        if self.show_grid:
            self.draw_grid(img)
        
        # 绘制画面中心（蓝色十字）
        self.draw_cross(img, self.center_x, self.center_y, image.COLOR_BLUE, 20, 3)
        
        # 绘制补偿后的中心（绿色十字）
        comp_center_x = self.center_x + self.offset_x
        comp_center_y = self.center_y + self.offset_y
        self.draw_cross(img, comp_center_x, comp_center_y, image.COLOR_GREEN, 15, 2)
        
        # 绘制模拟目标（红色圆圈）
        img.draw_circle(self.target_x, self.target_y, 10, image.COLOR_RED, thickness=3)
        
        # 绘制误差线
        # 原始误差线（蓝色虚线）
        img.draw_line(self.center_x, self.center_y, self.target_x, self.target_y, 
                     image.COLOR_BLUE, thickness=2)
        
        # 补偿后误差线（绿色实线）
        img.draw_line(comp_center_x, comp_center_y, self.target_x, self.target_y, 
                     image.COLOR_GREEN, thickness=3)
        
        # 绘制信息文本
        self.draw_info_text(img, raw_err_x, raw_err_y, comp_err_x, comp_err_y)
        
        # 绘制帮助信息
        if self.show_help:
            self.draw_help_text(img)
    
    def draw_cross(self, img, x, y, color, size, thickness):
        """绘制十字标记"""
        half_size = size // 2
        # 水平线
        img.draw_line(x - half_size, y, x + half_size, y, color, thickness)
        # 垂直线
        img.draw_line(x, y - half_size, x, y + half_size, color, thickness)
    
    def draw_grid(self, img):
        """绘制网格"""
        # 绘制网格线（浅灰色）
        grid_color = image.COLOR_WHITE
        
        # 垂直线
        for x in range(0, img.width(), 50):
            img.draw_line(x, 0, x, img.height(), grid_color, 1)
        
        # 水平线
        for y in range(0, img.height(), 50):
            img.draw_line(0, y, img.width(), y, grid_color, 1)
    
    def draw_info_text(self, img, raw_err_x, raw_err_y, comp_err_x, comp_err_y):
        """绘制信息文本"""
        y_pos = 10
        line_height = 25
        
        # 当前偏移值
        img.draw_string(10, y_pos, f"Offset: X={self.offset_x:+d}, Y={self.offset_y:+d}", 
                       image.COLOR_WHITE, scale=1.5, thickness=2)
        y_pos += line_height
        
        # 原始误差
        img.draw_string(10, y_pos, f"Raw Error: X={raw_err_x:+.0f}, Y={raw_err_y:+.0f}", 
                       image.COLOR_BLUE, scale=1.2, thickness=2)
        y_pos += line_height
        
        # 补偿后误差
        img.draw_string(10, y_pos, f"Comp Error: X={comp_err_x:+.0f}, Y={comp_err_y:+.0f}", 
                       image.COLOR_GREEN, scale=1.2, thickness=2)
        y_pos += line_height
        
        # 误差大小
        raw_magnitude = math.sqrt(raw_err_x**2 + raw_err_y**2)
        comp_magnitude = math.sqrt(comp_err_x**2 + comp_err_y**2)
        img.draw_string(10, y_pos, f"Magnitude: {raw_magnitude:.0f} -> {comp_magnitude:.0f}", 
                       image.COLOR_YELLOW, scale=1.2, thickness=2)
        y_pos += line_height
        
        # 调节步长
        img.draw_string(10, y_pos, f"Step: {self.step_size}", 
                       image.COLOR_WHITE, scale=1.0, thickness=1)
    
    def draw_help_text(self, img):
        """绘制帮助文本"""
        help_y = img.height() - 200
        line_height = 20
        
        help_texts = [
            "Controls:",
            "W/S: Adjust offset_y (+/-)",
            "A/D: Adjust offset_x (-/+)", 
            "Q/E: Change step size",
            "R: Reset offsets to 0",
            "T: Move target position",
            "G: Toggle grid",
            "H: Toggle help",
            "ESC: Exit"
        ]
        
        for i, text in enumerate(help_texts):
            color = image.COLOR_YELLOW if i == 0 else image.COLOR_WHITE
            scale = 1.2 if i == 0 else 1.0
            img.draw_string(10, help_y + i * line_height, text, color, scale=scale, thickness=1)
    
    def handle_key_input(self):
        """处理按键输入（模拟，实际需要根据MaixCAM Pro的输入方式调整）"""
        # 这里是模拟的按键处理，实际使用时需要根据具体硬件调整
        # 可以通过串口、GPIO按钮或其他方式实现
        pass
    
    def adjust_offset_x(self, delta):
        """调节offset_x"""
        old_value = self.offset_x
        self.offset_x += delta
        print(f"🔧 调节offset_x: {old_value} -> {self.offset_x} (变化: {delta:+d})")
        
        # 显示调节效果
        raw_err_x, raw_err_y, comp_err_x, comp_err_y = self.calculate_error()
        print(f"   误差变化: X轴 {comp_err_x:+.0f}")
    
    def adjust_offset_y(self, delta):
        """调节offset_y"""
        old_value = self.offset_y
        self.offset_y += delta
        print(f"🔧 调节offset_y: {old_value} -> {self.offset_y} (变化: {delta:+d})")
        
        # 显示调节效果
        raw_err_x, raw_err_y, comp_err_x, comp_err_y = self.calculate_error()
        print(f"   误差变化: Y轴 {comp_err_y:+.0f}")
    
    def change_step_size(self, direction):
        """改变调节步长"""
        if direction > 0:
            self.step_size = min(10, self.step_size + 1)
        else:
            self.step_size = max(1, self.step_size - 1)
        print(f"📏 调节步长: {self.step_size}")
    
    def reset_offsets(self):
        """重置偏移值"""
        self.offset_x = 0
        self.offset_y = 0
        print("🔄 偏移值已重置为 (0, 0)")
    
    def move_target(self):
        """移动模拟目标位置"""
        # 循环移动目标到不同位置
        positions = [
            (self.center_x + 50, self.center_y - 30),   # 右上
            (self.center_x - 40, self.center_y + 20),   # 左下
            (self.center_x + 30, self.center_y + 40),   # 右下
            (self.center_x - 50, self.center_y - 20),   # 左上
            (self.center_x, self.center_y)              # 中心
        ]
        
        # 找到当前位置的索引
        current_pos = (self.target_x, self.target_y)
        try:
            current_index = positions.index(current_pos)
            next_index = (current_index + 1) % len(positions)
        except ValueError:
            next_index = 0
        
        self.target_x, self.target_y = positions[next_index]
        print(f"🎯 目标移动到: ({self.target_x}, {self.target_y})")
    
    def run_auto_demo(self):
        """运行自动演示"""
        print("\n🎬 自动演示模式")
        print("强制设置不同偏移值，观察补偿效果...")
        
        # 演示不同的偏移值
        demo_offsets = [
            (0, 0, "无偏移"),
            (-10, -10, "左上偏移"),
            (10, 10, "右下偏移"),
            (-20, 0, "纯左偏移"),
            (0, -20, "纯上偏移"),
            (-12, -12, "推荐偏移")
        ]
        
        for offset_x, offset_y, description in demo_offsets:
            print(f"\n📍 {description}: offset_x={offset_x}, offset_y={offset_y}")
            
            self.offset_x = offset_x
            self.offset_y = offset_y
            
            # 计算并显示误差
            raw_err_x, raw_err_y, comp_err_x, comp_err_y = self.calculate_error()
            raw_mag = math.sqrt(raw_err_x**2 + raw_err_y**2)
            comp_mag = math.sqrt(comp_err_x**2 + comp_err_y**2)
            
            print(f"   原始误差: ({raw_err_x:+.0f}, {raw_err_y:+.0f}), 大小: {raw_mag:.0f}")
            print(f"   补偿误差: ({comp_err_x:+.0f}, {comp_err_y:+.0f}), 大小: {comp_mag:.0f}")
            print(f"   改善程度: {((raw_mag - comp_mag) / raw_mag * 100):+.1f}%")
            
            # 显示3秒
            for i in range(30):  # 30帧 = 约1秒
                img = self.cam.read()
                self.draw_interface(img)
                self.disp.show(img)
                time.sleep_ms(100)
        
        print("\n✅ 自动演示完成")
    
    def run(self):
        """运行校准工具"""
        print("\n🚀 开始偏移校准")
        print("强制设置不同偏移值，观察屏幕上的补偿效果...")
        print("=" * 50)
        
        try:
            frame_count = 0
            
            while not app.need_exit():
                # 读取摄像头图像
                img = self.cam.read()
                
                # 绘制界面
                self.draw_interface(img)
                
                # 显示图像
                self.disp.show(img)
                
                # 每100帧自动演示一次
                frame_count += 1
                if frame_count % 300 == 0:  # 约10秒一次
                    self.move_target()
                
                # 处理输入（这里可以添加按键处理）
                self.handle_key_input()
                
                time.sleep_ms(33)  # 约30fps
                
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断")
        
        finally:
            print(f"\n📊 最终偏移值:")
            print(f"   offset_x = {self.offset_x}")
            print(f"   offset_y = {self.offset_y}")
            print("\n💡 将这些值复制到main.py中:")
            print(f"   offset_x = {self.offset_x}  # X轴偏移补偿")
            print(f"   offset_y = {self.offset_y}  # Y轴偏移补偿")

def main():
    """主函数"""
    print("🎯 偏移补偿校准工具")
    print("参考main.py强制设置风格")
    print("=" * 40)
    
    try:
        # 创建校准工具
        calibrator = OffsetCalibrationTool()
        
        # 先运行自动演示
        calibrator.run_auto_demo()
        
        # 然后运行交互式校准
        calibrator.run()
        
    except Exception as e:
        print(f"❌ 校准工具运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
