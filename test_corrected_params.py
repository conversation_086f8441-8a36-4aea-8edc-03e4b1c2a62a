#!/usr/bin/env python3
"""
测试修正后的PWM参数
验证逆时针PWM计算是否正确

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def test_corrected_parameters():
    """测试修正后的PWM参数"""
    print("🔧 测试修正后的PWM参数")
    print("="*30)
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.65, enable=True)
        print("✓ PWM初始化成功")
        
        # 修正后的参数
        horizontal_stop_duty = 7.65
        horizontal_cw_duty = 8.5      # 顺时针范围：7.66% ~ 8.5%
        horizontal_ccw_duty = 7.15    # 逆时针范围：7.15% ~ 7.64%
        global_speed_multiplier = 1.5
        
        print(f"修正参数:")
        print(f"  停止位置: {horizontal_stop_duty}%")
        print(f"  顺时针范围: 7.66% ~ {horizontal_cw_duty}% (范围: {horizontal_cw_duty - 7.66:.2f}%)")
        print(f"  逆时针范围: {horizontal_ccw_duty}% ~ 7.64% (范围: {7.64 - horizontal_ccw_duty:.2f}%)")
        
        def test_speed_calculation(speed, description):
            """测试速度计算和PWM设置"""
            print(f"\n{description}: {speed}%")
            
            # 应用缩放
            final_speed = speed * global_speed_multiplier
            print(f"  缩放后速度: {final_speed:.1f}%")
            
            # PWM计算（与main.py完全一致）
            if final_speed == 0:
                duty = horizontal_stop_duty
                print(f"  停止: PWM = {duty:.3f}%")
            elif final_speed > 0:
                # 顺时针计算
                duty_range = horizontal_cw_duty - horizontal_stop_duty
                duty_change = (abs(final_speed) / 100.0) * duty_range
                duty = horizontal_stop_duty + duty_change
                duty = min(duty, horizontal_cw_duty)
                print(f"  顺时针: 范围={duty_range:.2f}%, 变化={duty_change:.3f}%, PWM={duty:.3f}%")
            else:
                # 逆时针计算
                duty_range = horizontal_stop_duty - horizontal_ccw_duty
                duty_change = (abs(final_speed) / 100.0) * duty_range
                duty = horizontal_stop_duty - duty_change
                duty = max(duty, horizontal_ccw_duty)
                print(f"  逆时针: 范围={duty_range:.2f}%, 变化={duty_change:.3f}%, PWM={duty:.3f}%")
            
            change = duty - horizontal_stop_duty
            print(f"  最终PWM: {duty:.3f}% (变化: {change:+.3f}%)")
            
            # 检查PWM值是否合理
            if duty < 7.15 or duty > 8.5:
                print(f"  ⚠️ PWM值超出预期范围!")
            elif 7.15 <= duty < 7.65:
                print(f"  ✅ 逆时针PWM值正常")
            elif duty == 7.65:
                print(f"  ✅ 停止PWM值正常")
            elif 7.65 < duty <= 8.5:
                print(f"  ✅ 顺时针PWM值正常")
            
            # 设置PWM并测试
            try:
                servo_pwm.duty(duty)
                time.sleep(3)
                return True
            except Exception as e:
                print(f"  ❌ PWM设置失败: {e}")
                return False
        
        # 测试不同速度
        test_cases = [
            (0, "停止测试"),
            (5, "小速度顺时针"),
            (-5, "小速度逆时针"),
            (10, "中速度顺时针"),
            (-10, "中速度逆时针"),
            (15, "高速度顺时针"),
            (-15, "高速度逆时针")
        ]
        
        print(f"\n开始测试不同速度...")
        success_count = 0
        
        for speed, description in test_cases:
            if test_speed_calculation(speed, description):
                success_count += 1
            
            # 回到停止位置
            servo_pwm.duty(horizontal_stop_duty)
            time.sleep(1)
        
        print(f"\n" + "="*30)
        print(f"测试完成! 成功: {success_count}/{len(test_cases)}")
        
        if success_count == len(test_cases):
            print("🎉 所有测试通过！PWM参数修正成功")
        else:
            print("⚠️ 部分测试失败，可能需要进一步调整")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def show_pwm_range_analysis():
    """显示PWM范围分析"""
    print("\n📊 PWM范围分析")
    print("="*20)
    
    horizontal_stop_duty = 7.65
    horizontal_cw_duty = 8.5
    horizontal_ccw_duty = 7.15
    
    print("实测数据:")
    print(f"  停止位置: {horizontal_stop_duty}%")
    print(f"  顺时针开始: 7.66%")
    print(f"  逆时针开始: {horizontal_ccw_duty}%")
    
    print(f"\n设置的PWM范围:")
    print(f"  顺时针范围: {horizontal_stop_duty}% ~ {horizontal_cw_duty}% (可用: {horizontal_cw_duty - horizontal_stop_duty:.2f}%)")
    print(f"  逆时针范围: {horizontal_ccw_duty}% ~ {horizontal_stop_duty}% (可用: {horizontal_stop_duty - horizontal_ccw_duty:.2f}%)")
    
    print(f"\n速度映射示例 (1.5倍数):")
    speeds = [10, 20, -10, -20]
    for speed in speeds:
        final_speed = speed * 1.5
        if speed > 0:
            duty_range = horizontal_cw_duty - horizontal_stop_duty
            duty_change = (abs(final_speed) / 100.0) * duty_range
            duty = horizontal_stop_duty + duty_change
        else:
            duty_range = horizontal_stop_duty - horizontal_ccw_duty
            duty_change = (abs(final_speed) / 100.0) * duty_range
            duty = horizontal_stop_duty - duty_change
        
        print(f"  {speed:3d}% → {final_speed:4.1f}% → {duty:.3f}%")

def main():
    """主函数"""
    print("🔧 修正后PWM参数测试")
    print("="*25)
    
    try:
        print("修正内容:")
        print("- horizontal_ccw_duty: 6.0% → 7.15% (实测逆时针开始位置)")
        print("- horizontal_cw_duty: 9.0% → 8.5% (适中的顺时针范围)")
        print("- 这样逆时针PWM值就不会出现奇怪的7.526%了")
        
        show_pwm_range_analysis()
        test_corrected_parameters()
        
        print("\n" + "="*25)
        print("🏁 修正测试完成！")
        print("\n如果测试成功，现在可以:")
        print("1. 运行 python main.py")
        print("2. 测试PID控制是否正常工作")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
