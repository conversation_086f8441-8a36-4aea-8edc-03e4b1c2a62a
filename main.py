'''
    2025电赛E题找A4 UV纸圆心，可以找到圆心和第三个圆圈，帧率 > 25fps。
    有多种设置和算法，根据实际情况选择。
    控制云台可以基于中心点误差 err_center 进行 PID 控制
    <AUTHOR> & lxo@sieed 协助
    @license MIT
    @date 2025.7.30
'''

from maix import camera, display, image, nn, app, time, pwm, pinmap, gpio
disp = display.Display()
img = image.Image(disp.width(), disp.height())
msg = "Loading ..."
size = image.string_size(msg, scale = 1.5, thickness=2)
img.draw_string((img.width() - size.width()) // 2, (img.height() - size.height()) // 2, msg, scale=1.5, thickness=2)
disp.show(img)

import cv2
import numpy as np
import os
import math

# 导入高级PID控制器
from advanced_pid_controller import AdvancedPIDController, ServoStabilityController, AdvancedServoController

# 舵机速度配置参数 - 直接内嵌，避免导入问题
print("使用内嵌舵机速度配置")

# ==================== 舵机速度控制参数 ====================

# 全局速度倍数 (0.1-1.0)
# 0.1 = 非常慢, 0.3 = 慢, 0.6 = 中等, 0.8 = 快, 1.0 = 最快
GLOBAL_SPEED_MULTIPLIER = 0.15  # 降低速度，避免舵机转动过快

# 水平舵机基础速度参数
HORIZONTAL_MAX_SPEED = 12      # 最大速度百分比 (5-30) - 降低最大速度
HORIZONTAL_MIN_SPEED = 2       # 最小有效速度百分比 (1-10) - 降低最小速度

# 垂直舵机速度参数
VERTICAL_MAX_STEP = 5.0        # 垂直舵机每次最大角度变化 (0.5-5.0度) - 大幅提升速度

# 垂直舵机PID参数
VERTICAL_PID_KP = 0.2          # 比例系数 (0.05-0.2) - 进一步提升响应
VERTICAL_PID_KI = 0.02        # 积分系数 (0.001-0.02) - 增强积分作用
VERTICAL_PID_KD = 0.04         # 微分系数 (0.01-0.1) - 适当增加阻尼

# 水平舵机PID参数
HORIZONTAL_PID_KP = 0.06       # 比例系数 (0.03-0.15) - 稍微保守避免震荡
HORIZONTAL_PID_KI = 0.001      # 积分系数 (0.001-0.01) - 减小积分避免超调
HORIZONTAL_PID_KD = 0.015      # 微分系数 (0.005-0.05) - 增加阻尼稳定系统

# 逼近控制阈值
APPROACH_THRESHOLD = 5         # 停止阈值 (3-10像素) - 适中阈值，确保能启动
FINE_ZONE = 15                 # 精细控制区 (8-20像素)
SLOW_ZONE = 30                 # 减速区 (15-40像素)
NORMAL_ZONE = 60               # 正常控制区 (30-80像素)

# 速度限制 (在不同区域的最大速度) - 恢复到能正常追踪的速度
FINE_ZONE_MAX_SPEED = 6        # 精细区最大速度 (3-10) - 稍微提升
SLOW_ZONE_MAX_SPEED = 12       # 减速区最大速度 (8-15) - 恢复有效速度
NORMAL_ZONE_MAX_SPEED = 18     # 正常区最大速度 (12-25) - 提升追踪能力
PID_ZONE_MAX_SPEED = 25        # PID区最大速度 (15-30) - 确保能快速接近目标

print(f"舵机配置已加载 - 当前为慢速安全配置")
print(f"全局速度倍数: {GLOBAL_SPEED_MULTIPLIER}")
print("=" * 50)
print("🔧 舵机速度调节说明:")
print(f"   如果舵机移动太快 → 减小 GLOBAL_SPEED_MULTIPLIER (当前: {GLOBAL_SPEED_MULTIPLIER})")
print(f"   如果舵机移动太慢 → 增大 GLOBAL_SPEED_MULTIPLIER (最大: 1.0)")
print("   推荐值: 0.2(很慢) 0.3(慢) 0.5(中等) 0.7(快)")
print("   修改位置: main.py 第30行")
print("=" * 50)

# A22继电器控制类
class A22RelayController:
    def __init__(self, gpio_pin="A22"):
        """
        初始化A22继电器控制器

        Args:
            gpio_pin: GPIO引脚名称，默认"A22"
        """
        self.gpio_pin = gpio_pin
        self.relay_gpio = None
        self.is_relay_on = False  # 初始化为高电平，所以继电器状态为关闭

        # 初始化GPIO
        self.init_gpio()

    def init_gpio(self):
        """初始化A22 GPIO引脚"""
        try:
            # 设置引脚功能为GPIO
            print(f"🔧 设置A22引脚映射: {self.gpio_pin}")
            pinmap.set_pin_function(self.gpio_pin, f"GPIO{self.gpio_pin}")

            # 创建GPIO对象
            gpio_name = f"GPIO{self.gpio_pin}"
            print(f"🔧 创建GPIO对象: {gpio_name}")
            self.relay_gpio = gpio.GPIO(gpio_name, gpio.Mode.OUT)

            # 设置初始状态为高电平（继电器关闭，假设低电平触发）
            self.relay_gpio.value(1)
            actual_value = self.relay_gpio.value()
            print(f"🔍 A22 GPIO初始化后状态: {actual_value} (期望: 1)")

            if actual_value != 1:
                print(f"⚠️  警告：A22 GPIO初始状态设置可能失败")

            print(f"✅ A22继电器控制GPIO初始化成功: {gpio_name}")
            print(f"💡 控制方式: 低电平触发继电器，高电平关闭继电器")
            print("🔋 初始状态: 高电平 (3.3V) - 继电器关闭状态")
            print("🎯 触发功能: 目标居中时输出1秒低电平信号")
            return True
        except Exception as e:
            print(f"❌ A22继电器控制GPIO初始化失败: {e}")
            import traceback
            traceback.print_exc()
            self.relay_gpio = None
            return False

    def turn_on_relay(self):
        """打开继电器（低电平触发）"""
        if self.relay_gpio is None:
            print("❌ A22 GPIO未初始化，无法控制继电器")
            return False

        try:
            before_value = self.relay_gpio.value()
            print(f"🔍 继电器开启前GPIO状态: {before_value}")

            self.relay_gpio.value(0)  # 低电平触发继电器

            after_value = self.relay_gpio.value()
            print(f"🔍 继电器开启后GPIO状态: {after_value}")

            self.is_relay_on = True
            print(f"🟢 A22继电器已开启")

            if after_value != 0:
                print("❌ 警告：GPIO设置失败，实际值不是0！")
                return False

            return True
        except Exception as e:
            print(f"❌ A22继电器开启失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def turn_off_relay(self):
        """关闭继电器（高电平关闭）"""
        if self.relay_gpio is None:
            print("❌ A22 GPIO未初始化，无法控制继电器")
            return False

        try:
            before_value = self.relay_gpio.value()
            print(f"🔍 继电器关闭前GPIO状态: {before_value}")

            self.relay_gpio.value(1)  # 高电平关闭继电器

            after_value = self.relay_gpio.value()
            print(f"🔍 继电器关闭后GPIO状态: {after_value}")

            self.is_relay_on = False
            print(f"🔴 A22继电器已关闭")

            if after_value != 1:
                print("❌ 警告：GPIO设置失败，实际值不是1！")
                return False

            return True
        except Exception as e:
            print(f"❌ A22继电器关闭失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def toggle_relay(self):
        """切换继电器状态"""
        if self.is_relay_on:
            return self.turn_off_relay()
        else:
            return self.turn_on_relay()

    def get_relay_status(self):
        """获取继电器状态"""
        if self.relay_gpio is None:
            return "GPIO_ERROR"

        try:
            gpio_value = self.relay_gpio.value()
            if gpio_value == 0:
                return "ON"
            else:
                return "OFF"
        except:
            return "READ_ERROR"

    def test_relay(self, on_duration=2.0, off_duration=1.0, cycles=3):
        """测试继电器功能"""
        print(f"🧪 开始A22继电器测试 - {cycles}个周期")
        print(f"⏰ 开启时间: {on_duration}s, 关闭时间: {off_duration}s")

        for i in range(cycles):
            print(f"\n--- 测试周期 {i+1}/{cycles} ---")

            # 开启继电器
            print(f"🟢 开启继电器...")
            if self.turn_on_relay():
                print(f"⏰ 保持开启 {on_duration}秒")
                time.sleep(on_duration)
            else:
                print("❌ 开启失败，跳过此周期")
                continue

            # 关闭继电器
            print(f"🔴 关闭继电器...")
            if self.turn_off_relay():
                print(f"⏰ 保持关闭 {off_duration}秒")
                time.sleep(off_duration)
            else:
                print("❌ 关闭失败")

        print("✅ A22继电器测试完成")

    def close(self):
        """关闭继电器控制器"""
        if self.relay_gpio:
            try:
                self.relay_gpio.value(1)  # 确保关闭（高电平）
                print("✅ A22继电器控制器已关闭")
            except Exception as e:
                print(f"⚠️  关闭A22继电器控制器时出错: {e}")

# 舵机控制类
class ServoController:
    def __init__(self):
        """初始化舵机控制器"""
        try:
            # 参考E题.py的正确初始化方式
            # 配置引脚功能为PWM (根据MaixCam Pro引脚定义)
            pinmap.set_pin_function("A19", "PWM7")  # 垂直舵机 (上下转动) - PWM7通道
            pinmap.set_pin_function("A18", "PWM6")  # 水平舵机 (左右转动) - PWM6通道

            # 初始化PWM (LD-3015MG舵机标准频率50Hz)
            # 重要：先初始化但不启用，避免舵机立即锁定
            print("正在初始化PWM...")
            self.vertical_pwm = pwm.PWM(7, freq=50, duty=0, enable=False)    # PWM通道7，初始禁用
            print("✓ 垂直舵机PWM7初始化成功（未启用）")

            self.horizontal_pwm = pwm.PWM(6, freq=50, duty=0, enable=False)  # PWM通道6，初始禁用
            print("✓ 水平舵机PWM6初始化成功（未启用）")

            print("舵机PWM初始化完成")
            print("A19引脚 -> PWM7 -> 垂直舵机 LD-3015MG (270°位置控制)")
            print("A18引脚 -> PWM6 -> 水平舵机 LD-3015MG (270°位置控制)")
            print("⚠️  舵机当前未启用，可以手动搬动")

            # 验证PWM对象是否有效
            if self.horizontal_pwm is None:
                raise Exception("水平舵机PWM初始化失败")
            if self.vertical_pwm is None:
                raise Exception("垂直舵机PWM初始化失败")

            # 垂直舵机参数（LD-3015MG 270度舵机，整体向上偏移45度）
            self.vertical_min_angle = 45      # 最小角度（向上45度）
            self.vertical_max_angle = 225     # 最大角度（向下225度）
            self.vertical_center_angle = 135  # 中心角度（偏移后的中心）
            self.vertical_current_angle = 135 # 当前角度（135度中心位置）

            # 水平舵机参数（LD-3015MG 270度位置舵机）
            self.horizontal_min_duty = 2.5      # 0度对应的占空比
            self.horizontal_max_duty = 12.5     # 270度对应的占空比
            self.horizontal_center_duty = 7.5   # 135度中位占空比
            self.horizontal_current_angle = 135 # 当前角度（270度舵机的中位是135度）

            # 水平舵机运动范围限制（避免机械碰撞）
            self.horizontal_min_angle = 45     # 最小角度（左极限）
            self.horizontal_max_angle = 225    # 最大角度（右极限）
            self.horizontal_center_angle = 135 # 中心角度（270度舵机的中心）、

            # PWM启用状态跟踪
            self.vertical_pwm_enabled = False
            self.horizontal_pwm_enabled = False

            # 速度控制参数 - 使用配置文件中的参数
            self.max_speed_percent = HORIZONTAL_MAX_SPEED  # 从配置文件读取
            self.min_speed_percent = 3         # 最小有效速度百分比
            self.speed_scale_factor = 1.0      # 基础速度缩放因子（恢复到1.0，避免重复缩放）

            # 可调节的速度参数
            self.global_speed_multiplier = GLOBAL_SPEED_MULTIPLIER  # 从配置文件读取

            # 垂直舵机PID控制参数 - 将在后面被配置文件参数覆盖
            self.vertical_pid_kp = VERTICAL_PID_KP      # 使用配置文件参数
            self.vertical_pid_ki = VERTICAL_PID_KI      # 使用配置文件参数
            self.vertical_pid_kd = VERTICAL_PID_KD      # 使用配置文件参数

            # 水平舵机PID控制参数 - 将在后面被配置文件参数覆盖
            self.horizontal_pid_kp = HORIZONTAL_PID_KP  # 使用配置文件参数
            self.horizontal_pid_ki = HORIZONTAL_PID_KI  # 使用配置文件参数
            self.horizontal_pid_kd = HORIZONTAL_PID_KD  # 使用配置文件参数

            # PID状态变量（保留原有的，用于兼容）
            self.vertical_error_sum = 0
            self.horizontal_error_sum = 0
            self.vertical_last_error = 0
            self.horizontal_last_error = 0

            # 初始化高级PID控制器（参考222.py）
            try:
                # 高级PID参数（参考222.py的优化参数）
                advanced_pid_params = {
                    "Kp": 0.2,           # 比例系数（进一步降低）
                    "Ki": 0.008,         # 积分系数（很小的积分）
                    "Kd": 0.75,          # 微分系数（增加阻尼）
                    "error_threshold": 8, # 积分分离阈值
                    "integral_limit": 10, # 积分限幅值
                    "min_output": 1,     # 最小输出
                    "max_output": 15,    # 最大输出限制
                    "max_step": 2        # PID输出变化率限制
                }

                # 稳定性参数
                stability_params = {
                    "stability_check_frames": 5,  # 连续稳定帧数要求
                    "max_stable_error": 6,        # 认为稳定的最大误差
                    "error_dead_zone": 5          # 误差死区
                }

                # 创建高级舵机控制器
                self.advanced_controller = AdvancedServoController(
                    pid_params=advanced_pid_params,
                    stability_params=stability_params,
                    debug=False  # 可以设置为True开启调试
                )

                print("✅ 高级PID控制器初始化成功")
                print(f"📊 PID参数: Kp={advanced_pid_params['Kp']}, Ki={advanced_pid_params['Ki']}, Kd={advanced_pid_params['Kd']}")
                print(f"🔧 稳定性参数: 死区={stability_params['error_dead_zone']}, 稳定阈值={stability_params['max_stable_error']}")

            except Exception as e:
                print(f"❌ 高级PID控制器初始化失败: {e}")
                self.advanced_controller = None

            print("舵机控制器初始化成功")
            print(f"垂直舵机PWM7初始化完成，当前占空比: 7.5%")
            print(f"水平舵机PWM6初始化完成，当前占空比: 7.5%")

        except Exception as e:
            print(f"舵机控制器初始化失败: {e}")
            print("请检查：")
            print("1. MaixCAM Pro是否支持PWM6和PWM7")
            print("2. A18和A19引脚是否正确连接")
            print("3. 舵机电源是否正常供电")
            self.vertical_pwm = None
            self.horizontal_pwm = None

    def vertical_angle_to_duty(self, angle):
        """将角度转换为PWM占空比 (垂直舵机45-225度范围，向上偏移45度)"""
        # 限制角度范围在设定的安全范围内
        angle = max(self.vertical_min_angle, min(self.vertical_max_angle, angle))

        # 线性映射到270度舵机的PWM范围：
        # 45度 -> 2.5% (500μs)   - 向上极限
        # 135度 -> 7.5% (1500μs) - 中心位置（向上偏移45度）
        # 225度 -> 12.5% (2500μs) - 向下极限
        # 使用180度范围(45-225)映射到270度舵机的完整PWM范围
        duty = 2.5 + ((angle - 45) / 180.0) * (12.5 - 2.5)
        return duty

    def horizontal_angle_to_duty(self, angle):
        """将角度转换为PWM占空比 (水平270度舵机)"""
        # 限制角度范围在允许的运动范围内
        angle = max(self.horizontal_min_angle, min(self.horizontal_max_angle, angle))

        # 线性映射：0度 -> 2.5%, 135度 -> 7.5%, 270度 -> 12.5%
        duty = self.horizontal_min_duty + (angle / 270.0) * (self.horizontal_max_duty - self.horizontal_min_duty)
        return duty

    def set_vertical_angle(self, angle):
        """设置垂直舵机角度 (45-225度，向上偏移45度)"""
        if self.vertical_pwm is None:
            print("警告：垂直舵机PWM未初始化")
            return

        # 限制角度范围在设定的安全范围内
        angle = max(self.vertical_min_angle, min(self.vertical_max_angle, angle))

        duty = self.vertical_angle_to_duty(angle)
        try:
            # 如果舵机未启用，先启用
            if not self.vertical_pwm_enabled:
                self.vertical_pwm.enable()
                self.vertical_pwm_enabled = True
                if DEBUG:
                    print("🔒 垂直舵机自动启用")

            self.vertical_pwm.duty(duty)
            self.vertical_current_angle = angle
            if DEBUG:
                print(f"垂直角度设置为: {angle}°, 占空比: {duty:.2f}% (范围45-225°，向上偏移45°)")
        except Exception as e:
            print(f"设置垂直舵机角度失败: {e}")

    def set_horizontal_angle(self, angle):
        """设置水平舵机角度 (45-225度) - LD-3015MG 270度位置舵机"""
        if self.horizontal_pwm is None:
            print("警告：水平舵机PWM未初始化")
            return

        # 限制角度范围在安全运动范围内
        angle = max(self.horizontal_min_angle, min(self.horizontal_max_angle, angle))

        duty = self.horizontal_angle_to_duty(angle)
        try:
            # 如果舵机未启用，先启用
            if not self.horizontal_pwm_enabled:
                self.horizontal_pwm.enable()
                self.horizontal_pwm_enabled = True
                if DEBUG:
                    print("🔒 水平舵机自动启用")

            self.horizontal_pwm.duty(duty)
            self.horizontal_current_angle = angle
            if DEBUG:
                print(f"🔧 水平舵机角度设置: {angle}°, 占空比: {duty:.2f}%")
        except Exception as e:
            print(f"❌ 设置水平舵机角度失败: {e}")

    def adjust_horizontal_angle(self, delta_angle):
        """调整水平舵机角度 (增量控制)"""
        new_angle = self.horizontal_current_angle + delta_angle
        self.set_horizontal_angle(new_angle)

    def pid_control(self, err_x, err_y):
        """基于误差进行双轴PID位置控制 - 两个舵机都是180度位置控制"""
        if self.vertical_pwm is None or self.horizontal_pwm is None:
            return

        # 垂直方向PID控制 (位置式 - 180度舵机)
        vertical_error = err_y  # 修复方向：目标在上方时舵机向上转
        self.vertical_error_sum += vertical_error
        vertical_derivative = vertical_error - self.vertical_last_error

        vertical_output = (self.vertical_pid_kp * vertical_error +
                          self.vertical_pid_ki * self.vertical_error_sum +
                          self.vertical_pid_kd * vertical_derivative)

        # 限制垂直舵机的移动速度
        try:
            max_vertical_step = VERTICAL_MAX_STEP * self.global_speed_multiplier
        except NameError:
            max_vertical_step = 2.0 * self.global_speed_multiplier
        vertical_output = max(-max_vertical_step, min(max_vertical_step, vertical_output))

        # 转换为角度调整 (增量式)
        new_vertical_angle = self.vertical_current_angle + vertical_output
        if DEBUG:
            print(f"🔧 垂直舵机: err_y={err_y:.1f} → output={vertical_output:.2f} → 当前角度={self.vertical_current_angle:.1f}° → 新角度={new_vertical_angle:.1f}°")
        self.set_vertical_angle(new_vertical_angle)

        # 水平方向PID位置控制 (180度舵机的位置控制)
        # 方向控制逻辑分析：
        # err_x > 0: 目标在画面右侧，舵机需要向右转（增大角度）
        # err_x < 0: 目标在画面左侧，舵机需要向左转（减小角度）
        horizontal_error = -err_x  # 修复方向：目标在右侧时舵机向右转

        # 水平舵机PID控制
        self.horizontal_error_sum += horizontal_error
        # 限制积分项，防止积分饱和
        self.horizontal_error_sum = max(-100, min(100, self.horizontal_error_sum))

        horizontal_derivative = horizontal_error - self.horizontal_last_error

        horizontal_output = (self.horizontal_pid_kp * horizontal_error +
                           self.horizontal_pid_ki * self.horizontal_error_sum +
                           self.horizontal_pid_kd * horizontal_derivative)

        # 限制水平舵机的移动速度
        try:
            max_horizontal_step = VERTICAL_MAX_STEP * self.global_speed_multiplier  # 使用相同的步长限制
        except NameError:
            max_horizontal_step = 2.0 * self.global_speed_multiplier
        horizontal_output = max(-max_horizontal_step, min(max_horizontal_step, horizontal_output))

        # 转换为角度调整 (增量式)
        # 注意：err_x > 0 表示目标在右侧，需要增大角度（向右转）
        new_horizontal_angle = self.horizontal_current_angle + horizontal_output
        if DEBUG:
            print(f"🔧 水平舵机: err_x={err_x:.1f} → output={horizontal_output:.2f} → 当前角度={self.horizontal_current_angle:.1f}° → 新角度={new_horizontal_angle:.1f}°")
        self.set_horizontal_angle(new_horizontal_angle)
        # 调试信息
        if DEBUG:
            print(f"PID调试: V_err={vertical_error:.1f} V_out={vertical_output:.2f} V_angle={self.vertical_current_angle:.1f}°")
            print(f"PID调试: H_err={horizontal_error:.1f} H_out={horizontal_output:.2f} H_angle={self.horizontal_current_angle:.1f}°")
            if err_x > 0:
                print(f"  → 目标在画面右侧，水平舵机角度={self.horizontal_current_angle:.1f}°")
            elif err_x < 0:
                print(f"  → 目标在画面左侧，水平舵机角度={self.horizontal_current_angle:.1f}°")
            else:
                print(f"  → 目标在画面中心，舵机角度稳定")

        # 更新上次误差
        self.vertical_last_error = vertical_error
        self.horizontal_last_error = horizontal_error

    def advanced_pid_control(self, target_x, target_y, center_x, center_y):
        """
        使用高级PID控制器进行舵机控制（参考222.py）

        Args:
            target_x, target_y: 目标位置
            center_x, center_y: 画面中心

        Returns:
            bool: 是否进行了舵机移动
        """
        if self.advanced_controller is None:
            print("❌ 高级PID控制器未初始化，回退到传统PID")
            err_x = target_x - center_x
            err_y = target_y - center_y
            self.pid_control(err_x, err_y)
            return True

        try:
            # 使用高级控制器计算控制输出
            should_move, control_x, control_y, error_info = self.advanced_controller.compute_control(
                target_x, target_y, center_x, center_y
            )

            if should_move:
                # 将控制输出转换为角度调整
                # 垂直舵机控制
                vertical_angle_delta = control_y * self.global_speed_multiplier
                new_vertical_angle = self.vertical_current_angle + vertical_angle_delta
                self.set_vertical_angle(new_vertical_angle)

                # 水平舵机控制
                horizontal_angle_delta = control_x * self.global_speed_multiplier
                new_horizontal_angle = self.horizontal_current_angle + horizontal_angle_delta
                self.set_horizontal_angle(new_horizontal_angle)

                if DEBUG:
                    print(f"🎯 高级PID控制: 误差=({error_info['err_x']:+.1f}, {error_info['err_y']:+.1f})")
                    print(f"   控制输出=({control_x:+.2f}, {control_y:+.2f})")
                    print(f"   角度调整=({horizontal_angle_delta:+.2f}°, {vertical_angle_delta:+.2f}°)")
                    print(f"   新角度=({new_horizontal_angle:.1f}°, {new_vertical_angle:.1f}°)")

                return True
            else:
                if DEBUG:
                    status = "稳定" if error_info['is_stable'] else "死区"
                    print(f"💤 高级PID: 舵机静止 ({status}) - 误差=({error_info['err_x']:+.1f}, {error_info['err_y']:+.1f})")
                return False

        except Exception as e:
            print(f"❌ 高级PID控制出错: {e}")
            # 回退到传统PID控制
            err_x = target_x - center_x
            err_y = target_y - center_y
            self.pid_control(err_x, err_y)
            return True

    def center_servos(self):
        """两个舵机都回中位"""
        self.set_vertical_angle(135)      # 垂直舵机回135度（向上偏移45度后的中位）
        self.set_horizontal_angle(135)    # 水平舵机回135度（270度舵机中位）

    def center_vertical(self):
        """垂直舵机回中位"""
        self.set_vertical_angle(135)

    def center_horizontal(self):
        """水平舵机回中位"""
        self.set_horizontal_angle(135)

    def enable_servos(self):
        """启用舵机控制（舵机将锁定到当前设置的位置）"""
        try:
            if self.vertical_pwm is not None:
                # 先设置到中心位置，再启用
                center_duty = self.vertical_angle_to_duty(self.vertical_center_angle)
                self.vertical_pwm.duty(center_duty)
                self.vertical_pwm.enable()
                self.vertical_pwm_enabled = True
                print(f"✅ 垂直舵机已启用，设置到中心位置 {self.vertical_center_angle}°")

            if self.horizontal_pwm is not None:
                # 先设置到中心位置，再启用
                center_duty = self.horizontal_angle_to_duty(self.horizontal_center_angle)
                self.horizontal_pwm.duty(center_duty)
                self.horizontal_pwm.enable()
                self.horizontal_pwm_enabled = True
                print(f"✅ 水平舵机已启用，设置到中心位置 {self.horizontal_center_angle}°")

            print("🔒 舵机已启用并锁定，开始位置控制")
        except Exception as e:
            print(f"❌ 启用舵机失败: {e}")

    def disable_servos(self):
        """禁用舵机控制（舵机可以手动搬动）"""
        try:
            if self.vertical_pwm is not None:
                self.vertical_pwm.disable()
                self.vertical_pwm_enabled = False
                print("🔓 垂直舵机已禁用")

            if self.horizontal_pwm is not None:
                self.horizontal_pwm.disable()
                self.horizontal_pwm_enabled = False
                print("🔓 水平舵机已禁用")

            print("✅ 舵机已禁用，可以手动搬动")
        except Exception as e:
            print(f"❌ 禁用舵机失败: {e}")

    def is_servo_enabled(self):
        """检查舵机是否已启用"""
        return self.vertical_pwm_enabled or self.horizontal_pwm_enabled

    def stop_horizontal(self):
        """停止水平舵机（位置舵机保持当前位置）"""
        # 位置舵机不需要"停止"，它会自动保持当前位置
        # 但我们可以清除PID积分项，防止残留控制信号
        self.horizontal_error_sum = 0
        self.horizontal_last_error = 0
        if DEBUG:
            print(f"🛑 水平舵机保持当前位置: {self.horizontal_current_angle:.1f}°，清除PID状态")

    def calibrate_stop_position(self):
        """校准水平舵机的停止位置"""
        print("\n🔧 水平舵机停止位置校准")
        print("="*40)
        print("正在测试不同的PWM占空比，找到真正的停止位置...")

        test_duties = [7.0, 7.1, 7.15, 7.2, 7.25, 7.3, 7.4]  # 围绕7.15%测试

        for duty in test_duties:
            print(f"\n测试PWM占空比: {duty:.1f}%")
            try:
                self.horizontal_pwm.duty(duty)
                print(f"  设置完成，观察舵机是否停止...")
                time.sleep(2)  # 观察2秒

                # 询问用户舵机是否停止
                print(f"  PWM {duty:.1f}% - 舵机是否完全停止？")
                print("  如果停止，请记住这个值")

            except Exception as e:
                print(f"  ❌ 设置PWM {duty:.1f}%失败: {e}")

        print("\n校准完成！")
        print("请根据观察结果，修改代码第110行的 self.horizontal_stop_duty 值")

    def quick_stop_test(self):
        """快速测试当前停止位置是否有效"""
        print(f"\n🔧 快速测试当前停止位置: {self.horizontal_stop_duty}%")

        # 先让舵机转动
        print("1. 让舵机转动...")
        self.horizontal_pwm.duty(8.0)  # 顺时针转动
        time.sleep(1)

        # 然后停止
        print(f"2. 设置停止位置: {self.horizontal_stop_duty}%")
        self.horizontal_pwm.duty(self.horizontal_stop_duty)
        time.sleep(2)

        print("观察舵机是否完全停止")
        print(f"如果还在转动，说明{self.horizontal_stop_duty}%不是正确的停止位置")
        print("根据用户测试，7.15%应该是正确的停止位置")

    def test_stop_position_715(self):
        """专门测试7.15%停止位置"""
        print(f"\n🎯 专门测试7.15%停止位置")
        print("="*40)

        # 测试序列：转动 → 7.15%停止 → 转动 → 7.15%停止
        test_sequence = [
            (20, "顺时针转动", 2),
            (7.15, "设置7.15%停止", 3),
            (-20, "逆时针转动", 2),
            (7.15, "再次设置7.15%停止", 3),
            (30, "快速顺时针", 1),
            (7.15, "最终7.15%停止", 3)
        ]

        for i, (value, description, duration) in enumerate(test_sequence, 1):
            print(f"\n步骤{i}: {description}")
            if value in [7.15]:
                # 直接设置PWM占空比
                self.horizontal_pwm.duty(value)
                print(f"  设置PWM占空比: {value}%")
            else:
                # 设置速度
                self.set_horizontal_speed(value)
                print(f"  设置速度: {value}%")

            time.sleep(duration)

        print("\n测试完成！如果舵机在7.15%时完全停止，说明校准正确")
        print("如果还在转动，请尝试微调停止位置（如7.14%或7.16%）")

    def test_deadzone_4us(self):
        """测试4微秒死区范围"""
        print(f"\n🎯 测试4微秒死区范围 (±0.02%)")
        print("="*45)

        # 测试死区边界值
        test_sequence = [
            (7.15, "停止位置 7.15%", 2),
            (7.17, "死区边界 7.17% (应该开始顺时针)", 3),
            (7.15, "回到停止位置", 2),
            (7.13, "死区边界 7.13% (应该开始逆时针)", 3),
            (7.15, "回到停止位置", 2),
            (7.16, "死区内 7.16% (应该停止)", 3),
            (7.14, "死区内 7.14% (应该停止)", 3),
            (7.15, "最终停止位置", 2)
        ]

        for i, (pwm_value, description, duration) in enumerate(test_sequence, 1):
            print(f"\n步骤{i}: {description}")
            self.horizontal_pwm.duty(pwm_value)
            print(f"  设置PWM: {pwm_value}%")

            if "应该停止" in description:
                print(f"  ⏸ 预期：舵机应该停止（在4微秒死区内）")
            elif "顺时针" in description:
                print(f"  ↻ 预期：舵机应该开始顺时针转动")
            elif "逆时针" in description:
                print(f"  ↺ 预期：舵机应该开始逆时针转动")

            time.sleep(duration)

        print("\n死区测试完成！")
        print("正确的4微秒死区应该表现为：")
        print("- 7.13%以下：逆时针转动")
        print("- 7.13%-7.17%：死区，舵机停止")
        print("- 7.17%以上：顺时针转动")
        print("- 7.15%：精确停止位置")

    def reset_pid(self):
        """重置PID状态"""
        self.vertical_error_sum = 0
        self.horizontal_error_sum = 0
        self.vertical_last_error = 0
        self.horizontal_last_error = 0

        # 重置高级PID控制器
        if self.advanced_controller:
            self.advanced_controller.reset_all()
            print("✅ 传统PID和高级PID状态已重置")
        else:
            print("✅ 传统PID状态已重置")

    def set_speed_multiplier(self, multiplier):
        """设置全局速度倍数 (0.1-1.0)"""
        self.global_speed_multiplier = max(0.1, min(1.0, multiplier))
        print(f"全局速度倍数设置为: {self.global_speed_multiplier:.2f}")

    def set_advanced_pid_debug(self, debug):
        """设置高级PID调试模式"""
        if self.advanced_controller:
            self.advanced_controller.set_debug(debug)
            print(f"✅ 高级PID调试模式已设置为: {debug}")
        else:
            print("❌ 高级PID控制器未初始化")

    def get_advanced_pid_status(self):
        """获取高级PID状态信息"""
        if self.advanced_controller:
            return self.advanced_controller.get_full_status()
        else:
            return None

    def get_speed_multiplier(self):
        """获取当前全局速度倍数"""
        return self.global_speed_multiplier

    def increase_speed(self, step=0.1):
        """增加速度"""
        new_multiplier = min(1.0, self.global_speed_multiplier + step)
        self.set_speed_multiplier(new_multiplier)

    def decrease_speed(self, step=0.1):
        """减少速度"""
        new_multiplier = max(0.1, self.global_speed_multiplier - step)
        self.set_speed_multiplier(new_multiplier)

    def test_servos(self):
        """测试舵机是否正常工作"""
        print("开始测试舵机...")

        # 测试垂直舵机 (向上偏移45度，范围45-225度)
        print("测试垂直舵机 (向上偏移45度，范围45-225度)...")
        self.set_vertical_angle(90)   # 90度 (向上45度)
        time.sleep(1)
        self.set_vertical_angle(180)  # 180度 (向下45度)
        time.sleep(1)
        self.set_vertical_angle(135)  # 回中位 (水平)
        time.sleep(1)

        # 测试水平舵机 (360度连续旋转舵机) - 重点测试停止功能
        print("测试水平舵机 (360度连续旋转舵机)...")
        print("测试小速度控制...")
        self.set_horizontal_speed(10)   # 小速度顺时针
        time.sleep(1)
        self.set_horizontal_speed(0)    # 停止
        time.sleep(2)  # 观察是否真的停止

        self.set_horizontal_speed(-10)  # 小速度逆时针
        time.sleep(1)
        self.set_horizontal_speed(0)    # 停止
        time.sleep(2)  # 观察是否真的停止

        print("测试中等速度...")
        self.set_horizontal_speed(25)   # 中等速度顺时针
        time.sleep(1)
        self.set_horizontal_speed(0)    # 停止
        time.sleep(1)

        print("舵机测试完成 - 请观察水平舵机是否能正确停止")

    def test_servo_direction(self):
        """测试舵机转向是否正确 - 用于调试方向问题"""
        print("\n" + "="*50)
        print("🔧 舵机方向测试 - 请观察舵机转向")
        print("="*50)

        # 回到中位
        print("1. 舵机回中位...")
        self.center_servos()
        time.sleep(2)

        # 测试水平舵机方向
        print("2. 测试水平舵机方向 (LD-3015MG 270度位置控制):")
        print("   180度 - 摄像头应该向右看")
        self.set_horizontal_angle(180)
        time.sleep(3)

        print("   回中心 135度")
        self.set_horizontal_angle(135)
        time.sleep(1)

        print("   90度 - 摄像头应该向左看")
        self.set_horizontal_angle(90)
        time.sleep(3)

        print("   回中心 135度")
        self.set_horizontal_angle(135)
        time.sleep(1)

        # 测试垂直舵机方向
        print("3. 测试垂直舵机方向 (向上偏移45度，范围45-225度):")
        print("   90度 - 摄像头应该向上看")
        self.set_vertical_angle(90)
        time.sleep(2)

        print("   180度 - 摄像头应该向下看")
        self.set_vertical_angle(180)
        time.sleep(2)

        print("   135度 - 摄像头回中位 (水平)")
        self.set_vertical_angle(135)
        time.sleep(1)

        print("="*50)
        print("🔍 方向测试完成！")
        print("✅ 已修复舵机方向：")
        print("  - 水平方向：horizontal_error = -err_x (目标在右侧时舵机向右转)")
        print("  - 垂直方向：vertical_error = -err_y (目标在上方时舵机向上转)")
        print("  - 如果方向仍然错误，可以再次取反误差符号")
        print("  - 现在两个舵机都是LD-3015MG位置控制舵机")
        print("="*50)

    def test_horizontal_only(self):
        """专门测试水平舵机角度控制 - LD-3015MG位置舵机"""
        print("\n🔧 水平舵机专项测试 (LD-3015MG)")
        print("="*40)
        print("测试模式：角度位置控制")

        # 角度测试序列（270度舵机）
        test_sequence = [
            (135, "中心位置", 2),
            (90, "向左45度", 2),
            (135, "回中心", 2),
            (180, "向右45度", 2),
            (135, "回中心", 2),
            (60, "向左75度", 2),
            (210, "向右75度", 2),
            (135, "最终回中心", 2)
        ]

        for angle, description, duration in test_sequence:
            print(f"\n设置角度: {angle}° ({description})")
            self.set_horizontal_angle(angle)

            if angle < 135:
                print(f"  ← 预期：舵机转向左侧 ({angle}°)")
            elif angle > 135:
                print(f"  → 预期：舵机转向右侧 ({angle}°)")
            else:
                print(f"  ⏸ 预期：舵机在中心位置 (135°)")

            time.sleep(duration)

        print("\n水平舵机专项测试完成")
        print("LD-3015MG位置舵机应该能精确到达指定角度")
        print(f"当前角度: {self.horizontal_current_angle}°")

    def test_horizontal_pwm_direct(self):
        """直接测试水平舵机PWM占空比 - 最底层测试"""
        print("\n🔧 水平舵机PWM直接测试")
        print("="*35)

        if self.horizontal_pwm is None:
            print("❌ 水平舵机PWM未初始化")
            return

        # 测试不同的PWM占空比
        test_duties = [5.0, 6.0, 7.0, 7.5, 8.0, 9.0, 10.0]

        for duty in test_duties:
            print(f"测试PWM占空比: {duty:.1f}%")
            try:
                self.horizontal_pwm.duty(duty)
                time.sleep(2)
                print(f"  ✓ 设置成功")
            except Exception as e:
                print(f"  ❌ 设置失败: {e}")

        # 回到停止位置
        print("回到停止位置: 7.5%")
        try:
            self.horizontal_pwm.duty(7.5)
        except Exception as e:
            print(f"回到停止位置失败: {e}")

        print("PWM直接测试完成")
        print("如果所有占空比都设置成功但舵机不动，可能是:")
        print("1. 舵机电源问题")
        print("2. 舵机信号线连接问题")
        print("3. 舵机本身故障")
        print("4. PWM频率问题(当前50Hz)")

    def force_horizontal_test(self):
        """强制水平舵机测试 - 绕过所有逻辑直接控制"""
        print("\n🚨 强制水平舵机测试 - 绕过所有PID逻辑")
        print("="*45)

        if self.horizontal_pwm is None:
            print("❌ 水平舵机PWM未初始化")
            return

        print("强制设置不同速度，观察舵机是否转动...")

        # 强制测试序列 - 使用更小的速度范围
        test_sequence = [
            (10, "小速度顺时针"),
            (0, "停止"),
            (-10, "小速度逆时针"),
            (0, "停止"),
            (20, "中等顺时针"),
            (0, "停止"),
            (-20, "中等逆时针"),
            (0, "停止"),
            (30, "较快顺时针"),
            (0, "停止"),
            (-30, "较快逆时针"),
            (0, "停止")
        ]

        for speed, description in test_sequence:
            print(f"强制设置速度: {speed}% ({description})")
            self.set_horizontal_speed(speed)
            time.sleep(3)  # 更长的观察时间

        print("强制测试完成")
        print("如果舵机在此测试中仍不动，问题可能是:")
        print("1. 硬件连接问题")
        print("2. 电源供应问题")
        print("3. 舵机本身故障")

    def calibrate_horizontal_servo(self):
        """校准水平舵机 - 找到正确的PWM范围"""
        print("\n🔧 水平舵机校准程序")
        print("="*35)

        if self.horizontal_pwm is None:
            print("❌ 水平舵机PWM未初始化")
            return

        print("正在校准MG996R 360度舵机...")
        print("请观察舵机转动情况，记录哪些PWM值能让舵机转动")

        # 测试不同的PWM占空比来找到有效范围
        test_duties = [
            (6.0, "强逆时针"),
            (6.5, "中逆时针"),
            (7.0, "弱逆时针"),
            (7.5, "停止位置"),
            (8.0, "弱顺时针"),
            (8.5, "中顺时针"),
            (9.0, "强顺时针")
        ]

        working_duties = []

        for duty, description in test_duties:
            print(f"\n测试PWM占空比: {duty:.1f}% ({description})")
            try:
                self.horizontal_pwm.duty(duty)
                time.sleep(3)

                # 询问用户舵机是否转动
                print(f"  PWM {duty:.1f}% - 舵机是否转动？")
                if duty == 7.5:
                    print("  (这应该是停止位置)")
                elif duty < 7.5:
                    print("  (这应该是逆时针转动)")
                else:
                    print("  (这应该是顺时针转动)")

                working_duties.append((duty, description))

            except Exception as e:
                print(f"  ❌ 设置PWM {duty:.1f}%失败: {e}")

        # 回到停止位置
        print("\n回到停止位置...")
        try:
            self.horizontal_pwm.duty(7.5)
        except Exception as e:
            print(f"回到停止位置失败: {e}")

        print("\n校准完成！")
        print("根据测试结果，建议的PWM设置：")
        print("- 停止: 7.15% (用户测试确认)")
        print("- 死区: ±0.02% (4微秒)")
        print("- 顺时针范围: 7.17% - 8.5%")
        print("- 逆时针范围: 5.5% - 7.13%")
        print("\n如果舵机只能单向转动，可能需要调整PWM范围或检查舵机类型")

    def test_pid_control(self):
        """测试PID控制是否正常工作"""
        print("\n🔧 PID控制测试")
        print("="*25)

        # 模拟不同的误差值来测试PID响应
        test_errors = [
            (50, 0, "大误差向右"),
            (-50, 0, "大误差向左"),
            (20, 0, "中等误差向右"),
            (-20, 0, "中等误差向左"),
            (10, 0, "小误差向右"),
            (-10, 0, "小误差向左"),
            (0, 0, "无误差")
        ]

        for err_x, err_y, description in test_errors:
            print(f"\n测试: {description} (err_x={err_x}, err_y={err_y})")
            self.pid_control(err_x, err_y)
            time.sleep(2)

        print("\nPID控制测试完成")

    def cleanup(self):
        """清理资源"""
        try:
            if self.vertical_pwm:
                self.vertical_pwm.duty(7.5)  # 垂直舵机回中位
            if self.horizontal_pwm:
                self.horizontal_pwm.duty(7.5)  # 水平舵机停止
            print("舵机控制器资源已清理 - 所有舵机已停止")
        except Exception as e:
            print(f"清理舵机资源时出错: {e}")

################################ config #########################################

# 调试配置 - 统一管理所有调试选项
DEBUG = False                  # 主调试开关
PRINT_TIME = False            # 打印每一步消耗的时间
debug_draw_err_line = True    # 画出圆心和画面中心的误差线，需要消耗1ms左右时间
debug_draw_err_msg = False    # 画出圆心和画面中心的误差值和 FPS 信息，需要消耗7ms左右时间，慎用
debug_draw_circle = True      # 画出圆圈，实际是画点，需要再打开变量, debug 模式都会画，耗费时间比较多，慎用
debug_draw_rect = False       # 画出矩形框
debug_show_hires = True       # 显示结果在高分辨率图上，而不是小分辨率图上， 开启了 hires_mode 才生效

# 如果需要完整调试，取消下面的注释
# DEBUG = True
# PRINT_TIME = True
# debug_draw_err_msg = True
# debug_draw_rect = True


crop_padding = 12            # 裁切图时的外扩距离，调试到保证最近和最远位置整个黑框在检测框里，可以打开 DEBUG 模式看
rect_min_limit = 12          # 找到的大黑边框四个点最小距离必须大于这个值才有效，防止找到错误的值，可以放到最远位置测试
std_from_white_rect = True   # 裁切标准图是裁切自A4纸内部白色部分（更精准），False则是带黑框的外围框（整个A4纸）（更快一点点）
circle_num_points = 50       # 生成的第三个圆圈的点数量，控制圆边的平滑程度，可以用来巡迹
std_res = [int(29.7 / 21 * 80), 80]        # 找中心点和圆圈的分辨率，越大越精确，更慢，A4 29.7 x 21cm
hires_mode = True           # 高分辨模式，适合 find_circle 模式使用，帧率会更低但是找圆圈更精准
                             # 不 find_circle 也可以使用，找4个角点更精准，需要配合设置合理的 std_res
                             # 注意开启了这个模式，输出的误差值也是基于大图的分辨率
high_res = 448               # 高分辨率模式宽高,越高越清晰但是帧率越低，注意 std_res 也要跟着改大点
model_path = "/root/models/model_3356.mud" # 检测黑框模型路径，从 https://maixhub.com/model/zoo/1159 下载并传到开发板的 /root/models 目录


find_circle = False          # 在找到黑框以内白框后是否继续找圆，如果圆圈画得标准，在纸正中心则不用找，如果画点不在纸正中心则需要找。
                             # 建议把A4纸制作正确就不用找了，帧率更高。
                             # 可以用hires_mode 更清晰才能识别到，另外设置合理的 std_res
cam_buff_num = 1             # 摄像头缓冲， 1 延迟更低帧率慢一点点， 2延迟更高帧率高一点点
find_laser = True            # 启用蓝紫色激光点检测，用于激光笔校准

auto_awb = True                            # 自动白平衡或者手动白平衡
awb_gain = [0.134, 0.0625, 0.0625, 0.1139]  # 手动白平衡，auto_awb为False才生效， R GR GB B 的值，调 R 和 B 即可
contrast = 80                               # 对比度，会影响到检测，阴影和圆圈痕迹都会更重

# 舵机控制配置
# 舵机连接说明：
# - 垂直舵机（180°版本MG996R）连接到 A19口（PWM7）- 控制上下俯仰（位置控制）
# - 水平舵机（360°版本MG996R）连接到 A18口（PWM6）- 控制左右旋转（速度控制）
# 注意：混合控制方式 - 垂直位置控制 + 水平速度控制
servo_control_enabled = True               # 是否启用舵机控制
servo_auto_init = False                    # 程序启动时是否自动初始化舵机（False=手动启用）

# 偏移补偿参数（参考111.py，用于修正打靶位置）
offset_x = 3                             # X轴偏移补偿（像素）
offset_y = 33                             # Y轴偏移补偿（像素）

print("🎯 打靶偏移补偿说明:")
print(f"   X轴偏移: {offset_x}像素 (负值=向左补偿, 正值=向右补偿)")
print(f"   Y轴偏移: {offset_y}像素 (负值=向上补偿, 正值=向下补偿)")
print("   如果打靶偏右 → 减小offset_x值")
print("   如果打靶偏左 → 增大offset_x值")
print("   如果打靶偏下 → 减小offset_y值")
print("   如果打靶偏上 → 增大offset_y值")
print("   修改位置: main.py 第1012-1013行")
print("=" * 50)

# 垂直舵机PID参数（180度位置舵机）- 使用内嵌配置参数
servo_vertical_pid_kp = VERTICAL_PID_KP    # 垂直比例系数
servo_vertical_pid_ki = VERTICAL_PID_KI    # 垂直积分系数
servo_vertical_pid_kd = VERTICAL_PID_KD    # 垂直微分系数

# 水平舵机PID参数（360度速度舵机）- 使用内嵌配置参数
servo_horizontal_pid_kp = HORIZONTAL_PID_KP  # 水平比例系数
servo_horizontal_pid_ki = HORIZONTAL_PID_KI  # 水平积分系数
servo_horizontal_pid_kd = HORIZONTAL_PID_KD  # 水平微分系数

servo_error_threshold = 18                 # 误差阈值，小于此值不进行舵机调整（加大死区，提高稳定性）
servo_stop_threshold = 9                  # 停止阈值，小于此值才停止舵机（加大死区，避免频繁启停）
servo_max_no_target_frames = 30           # 最大未检测到目标的帧数，超过后停止舵机
servo_test_mode = False                   # 测试模式：关闭测试模式，使用正常逼近控制

# 目标丢失预测跟踪参数
enable_predictive_tracking = True        # 启用预测跟踪功能
max_prediction_frames = 60               # 最大预测跟踪帧数（2秒@30fps）
prediction_decay_factor = 0.95           # 预测强度衰减因子（每帧衰减5%）
min_prediction_strength = 0.1            # 最小预测强度阈值

# A22触发参数
enable_a22_trigger = True                # 启用A22触发功能
a22_trigger_duration = 1.0               # A22低电平持续时间（秒）
a22_stable_frames_required = 5           # 需要连续稳定的帧数才触发A22

# 激光点校准参数
enable_laser_calibration = True          # 启用激光点校准功能
laser_offset_x = 0                       # 激光点X轴偏差校正值（像素）
laser_offset_y = 0                       # 激光点Y轴偏差校正值（像素）
laser_calibration_mode = False           # 激光校准模式：True=校准模式，False=正常跟踪模式
laser_show_offset = True                 # 显示激光点与目标点的偏差信息

# 舵机速度调节参数 - 使用内嵌配置参数
servo_speed_multiplier = GLOBAL_SPEED_MULTIPLIER      # 全局速度倍数
servo_approach_speed_limit = 10                       # 逼近时的最大速度限制（像素/帧）

###################################################################################

if not os.path.exists(model_path):
    model_path1 = "model/model_3356.mud"
    if not os.path.exists(model_path1):
        print(f"load model failed, please put model in {model_path}, or {os.path.getcwd()}/{model_path1}")
    model_path = model_path1

# 初始化AI检测器
detector = nn.YOLOv5(model=model_path, dual_buff = True)

# 初始化舵机控制器
servo_controller = ServoController()

# 初始化A22继电器控制器
print("\n" + "="*50)
print("🔌 初始化A22继电器控制器")
print("="*50)
a22_relay = A22RelayController("A22")
if a22_relay.relay_gpio:
    print("✅ A22继电器控制器初始化成功")
else:
    print("❌ A22继电器控制器初始化失败")
    a22_relay = None
# 设置分离的PID参数
servo_controller.vertical_pid_kp = servo_vertical_pid_kp
servo_controller.vertical_pid_ki = servo_vertical_pid_ki
servo_controller.vertical_pid_kd = servo_vertical_pid_kd
servo_controller.horizontal_pid_kp = servo_horizontal_pid_kp
servo_controller.horizontal_pid_ki = servo_horizontal_pid_ki
servo_controller.horizontal_pid_kd = servo_horizontal_pid_kd
servo_controller.test_mode = servo_test_mode

# 设置全局速度倍数
servo_controller.set_speed_multiplier(servo_speed_multiplier)

print(f"舵机控制器初始化完成")
print(f"当前速度设置: {servo_controller.get_speed_multiplier():.2f}")
print(f"提示: 如果舵机移动太快，可以调小 servo_speed_multiplier 参数（当前: {servo_speed_multiplier}）")
print(f"提示: 如果舵机移动太慢，可以调大 servo_speed_multiplier 参数（最大: 1.0）")

# 显示高级PID控制器状态
if servo_controller.advanced_controller:
    print("\n🎯 高级PID控制器状态:")
    print("✅ 高级PID控制器已启用（参考222.py优化算法）")
    print("📊 特性: 积分分离、积分限幅、防振荡、稳定性检测")
    print("🔧 可使用测试选项 d/e 开启/关闭调试模式")
else:
    print("\n❌ 高级PID控制器未启用，将使用传统PID控制")

# 初始化摄像头
if hires_mode:
    cam = camera.Camera(high_res, high_res, detector.input_format(), buff_num=cam_buff_num)
else:
    cam = camera.Camera(detector.input_width(), detector.input_height(), detector.input_format(), buff_num=cam_buff_num)
if not auto_awb:
    cam.awb_mode(camera.AwbMode.Manual)
    cam.set_wb_gain(awb_gain)
cam.constrast(contrast)
# cam.set_windowing([448, 448])

# 舵机初始化 - 根据配置决定是否自动启用
if servo_auto_init:
    print("🔓 舵机初始化完成，当前为禁用状态（可手动搬动）")
    print("💡 提示：运行程序时舵机会自动启用，或使用测试选项7手动启用")
else:
    print("🔓 舵机初始化完成，当前为禁用状态（可手动搬动）")
    print("💡 提示：舵机不会自动启用，需要手动使用测试选项7启用或在程序中手动启用")

# 舵机校准和测试选项
print("\n" + "="*50)
print("🔧 LD-3015MG 双轴位置舵机系统")
print("="*50)
print("舵机配置信息：")
print("✓ 垂直舵机: LD-3015MG (向上偏移45°，范围45-225°)")
print("✓ 水平舵机: LD-3015MG (270°位置控制)")
print("✓ 控制方式: 双轴PID位置控制")
print("✓ 运动范围: 垂直45-225°, 水平45-225°")
print("✓ 中心位置: 垂直135°, 水平135°")
print("✓ 响应速度: 高精度位置伺服控制")
print("✓ 初始状态: 舵机禁用（可手动搬动）")
print("✓ 自动启用: 程序运行时自动启用舵机控制")
print("\n是否需要进行舵机测试？")
print("输入选项：")
print("  1 - 跳过测试，直接运行程序")
print("  2 - 测试水平舵机角度控制")
print("  3 - 测试垂直舵机角度控制")
print("  4 - 测试双轴舵机方向")
print("  5 - 完整舵机功能测试")
print("  6 - PID控制测试")
print("  7 - 启用舵机（锁定位置）")
print("  8 - 禁用舵机（可手动搬动）")
print("  9 - 测试A22继电器控制")
print("  a - A22继电器开启")
print("  b - A22继电器关闭")
print("  c - 偏移补偿校准（调节offset_x和offset_y）")
print("  d - 启用高级PID调试模式")
print("  e - 禁用高级PID调试模式")

# 简单的用户输入（在实际MaixCAM上可能需要其他方式）
test_choice = "1"  # 默认跳过测试，如需测试请修改这里

if test_choice == "2":
    print("\n测试水平舵机角度控制...")
    servo_controller.test_horizontal_only()
elif test_choice == "3":
    print("\n测试垂直舵机角度控制...")
    servo_controller.test_servos()  # 重用现有的测试函数
elif test_choice == "4":
    print("\n测试双轴舵机方向...")
    servo_controller.test_servo_direction()
elif test_choice == "5":
    print("\n执行完整舵机功能测试...")
    servo_controller.test_servos()
    servo_controller.test_servo_direction()
elif test_choice == "6":
    print("\n执行PID控制测试...")
    servo_controller.test_pid_control()
elif test_choice == "7":
    print("\n启用舵机...")
    servo_controller.enable_servos()
elif test_choice == "8":
    print("\n禁用舵机...")
    servo_controller.disable_servos()
elif test_choice == "9":
    print("\n测试A22继电器...")
    if a22_relay:
        a22_relay.test_relay(on_duration=2.0, off_duration=1.0, cycles=3)
    else:
        print("❌ A22继电器控制器未初始化")
elif test_choice == "a" or test_choice == "A":
    print("\n开启A22继电器...")
    if a22_relay:
        a22_relay.turn_on_relay()
    else:
        print("❌ A22继电器控制器未初始化")
elif test_choice == "b" or test_choice == "B":
    print("\n关闭A22继电器...")
    if a22_relay:
        a22_relay.turn_off_relay()
    else:
        print("❌ A22继电器控制器未初始化")
elif test_choice == "c" or test_choice == "C":
    print("\n偏移补偿校准...")
    print("❌ 偏移补偿校准功能暂未实现")
elif test_choice == "d" or test_choice == "D":
    print("\n启用高级PID调试模式...")
    servo_controller.set_advanced_pid_debug(True)
elif test_choice == "e" or test_choice == "E":
    print("\n禁用高级PID调试模式...")
    servo_controller.set_advanced_pid_debug(False)
else:
    print("\n跳过测试，直接运行程序...")

# LD-3015MG双轴270度位置舵机系统就绪
print("\n✅ LD-3015MG双轴270度位置舵机系统初始化完成")

# 根据配置决定是否自动启用舵机控制
if servo_auto_init:
    print("\n🔒 自动启用舵机控制，开始目标跟踪...")
    servo_controller.enable_servos()
else:
    print("\n⏸️ 舵机控制未自动启用，程序以检测模式运行")
    print("💡 如需启用舵机，请使用测试选项7或在检测到目标时手动启用")

print("="*50)

def offset_calibration_mode():
    """偏移补偿校准模式"""
    print("🎯 偏移补偿校准模式")
    print("强制设置不同偏移值，观察补偿效果...")
    print("=" * 40)

    global offset_x, offset_y

    # 保存原始值
    original_offset_x = offset_x
    original_offset_y = offset_y

    # 测试序列（参考"强制设置不同速度"的风格）
    test_offsets = [
        (0, 0, "无偏移基准"),
        (-5, -5, "小幅偏移"),
        (-10, -10, "中等偏移"),
        (-15, -15, "较大偏移"),
        (-20, -20, "大幅偏移"),
        (-12, -12, "推荐偏移"),
        (-10, 0, "仅X轴偏移"),
        (0, -10, "仅Y轴偏移"),
        (5, 5, "反向偏移"),
        (original_offset_x, original_offset_y, "恢复原始值")
    ]

    print(f"当前偏移值: offset_x={offset_x}, offset_y={offset_y}")
    print("强制设置不同偏移值，观察误差计算变化...")

    for test_x, test_y, description in test_offsets:
        print(f"\n📍 强制设置偏移: X={test_x:+d}, Y={test_y:+d} ({description})")

        # 临时设置偏移值
        offset_x = test_x
        offset_y = test_y

        # 模拟误差计算（假设目标在(274, 194)，画面中心在(224, 224)）
        target_x, target_y = 274, 194
        center_x, center_y = 224, 224

        # 原始误差
        raw_err_x = target_x - center_x
        raw_err_y = target_y - center_y

        # 补偿后误差
        comp_err_x = target_x - (center_x + offset_x)
        comp_err_y = target_y - (center_y + offset_y)

        # 计算误差大小
        import math
        raw_magnitude = math.sqrt(raw_err_x**2 + raw_err_y**2)
        comp_magnitude = math.sqrt(comp_err_x**2 + comp_err_y**2)

        print(f"   原始误差: ({raw_err_x:+.0f}, {raw_err_y:+.0f}), 大小: {raw_magnitude:.1f}")
        print(f"   补偿误差: ({comp_err_x:+.0f}, {comp_err_y:+.0f}), 大小: {comp_magnitude:.1f}")

        if raw_magnitude > 0:
            improvement = (raw_magnitude - comp_magnitude) / raw_magnitude * 100
            print(f"   改善程度: {improvement:+.1f}%")

        # 观察时间
        print(f"   观察补偿效果中...")
        time.sleep(2)

    print(f"\n✅ 偏移校准演示完成")
    print(f"📊 最终偏移值: offset_x={offset_x}, offset_y={offset_y}")
    print(f"\n💡 调整建议:")
    print(f"   如果打靶偏右 → 减小offset_x值（当前: {offset_x}）")
    print(f"   如果打靶偏左 → 增大offset_x值")
    print(f"   如果打靶偏下 → 减小offset_y值（当前: {offset_y}）")
    print(f"   如果打靶偏上 → 增大offset_y值")
    print(f"\n🔧 修改位置: main.py 第1012-1013行")

def find_laser_point(img, original_img):
    '''
    检测蓝紫色激光点
    针对蓝紫色激光笔优化的检测算法
    '''

    # 蓝紫色激光点的LAB阈值 - 针对蓝紫色激光优化
    # L: 亮度, A: 绿红轴, B: 蓝黄轴
    # 蓝紫色激光特征：高亮度，A值偏负（偏绿），B值明显负值（偏蓝）
    laser_ths = [
        [60, 100, -128, -10, -128, -30],  # 高亮蓝紫色
        [40, 100, -128, 0, -128, -20],    # 中亮蓝紫色
        [30, 100, -128, 10, -128, -15]    # 低亮蓝紫色
    ]

    max_s = 0
    max_b = None

    # 尝试不同的阈值，找到最佳的激光点
    for ths in laser_ths:
        try:
            blobs = img.find_blobs([ths], x_stride=1, y_stride=1,
                                 area_threshold=3, pixels_threshold=3)

            for b in blobs:
                # 激光点特征筛选
                area = b.w() * b.h()
                aspect_ratio = max(b.w(), b.h()) / max(min(b.w(), b.h()), 1)

                # 激光点应该是小而圆的亮点
                if (3 <= area <= 200 and          # 面积范围
                    aspect_ratio <= 3.0 and       # 长宽比不能太大
                    b.density() > 0.3):           # 密度要高

                    if area > max_s:
                        max_s = area
                        max_b = b

        except Exception as e:
            if DEBUG:
                print(f"激光点检测阈值失败: {e}")
            continue

    # 调试信息
    if DEBUG and max_b:
        print(f"🔵 检测到激光点: 位置=({max_b.cx()}, {max_b.cy()}), 大小={max_b.w()}x{max_b.h()}, 面积={max_s}")

        # 在调试模式下显示激光点检测的二值化图像
        try:
            laser_binary = img.binary(laser_ths[0], copy=True)
            if original_img and hasattr(original_img, 'draw_image'):
                original_img.draw_image(original_img.width() - laser_binary.width(),
                                      original_img.height() - laser_binary.height(),
                                      laser_binary)
        except:
            pass

    return max_b

_t = time.ticks_ms()
def debug_time(msg):
    if PRINT_TIME:
        global _t
        print(f"t: {time.ticks_ms() - _t:4d} {msg}")
        _t = time.ticks_ms()

err_center = [0, 0] # 距离中心的误差

# 预计算常量值，避免每帧重复计算
CAMERA_CENTER = [cam.width() // 2, cam.height() // 2] # 画面的中心
DETECTOR_CENTER = [detector.input_width() // 2, detector.input_height() // 2] # 小图中心

# 预计算缩放比例
if hires_mode:
    IMG_AI_SCALE = [cam.width() / detector.input_width(), cam.height() / detector.input_height()]
else:
    IMG_AI_SCALE = [1.0, 1.0]

center_pos = CAMERA_CENTER # 画面的中心
last_center = center_pos # 上一次检测到的圆心距离
last_center_small = DETECTOR_CENTER # 高清模式时，在小图的中心坐标

# 预测跟踪相关变量
last_valid_center = center_pos.copy()    # 最后一次有效的目标位置
last_movement_vector = [0, 0]            # 最后的运动向量
prediction_frames_count = 0              # 预测跟踪的帧数计数
prediction_strength = 1.0                # 当前预测强度
target_lost_frames = 0                   # 目标丢失的连续帧数

# A22触发相关变量
target_stable_frames = 0                 # 目标稳定居中的连续帧数
a22_trigger_active = False               # A22触发是否激活
a22_trigger_start_time = 0               # A22触发开始时间
last_trigger_time = 0                    # 上次触发时间，避免频繁触发

# 舵机控制相关变量
no_target_count = 0          # 未检测到目标的计数
target_lost = False          # 目标丢失标志
target_detected = False      # 当前帧是否检测到目标

while not app.need_exit():
    debug_time("start")
    img = cam.read()
    debug_time("cam read")
    # 每帧开始时重置目标检测标志
    target_detected = False
    # 每帧开始时假设需要停止舵机，只有检测到目标时才会控制舵机
    should_stop_servo = True
    # AI 检测外框
    if hires_mode:
        img_ai = img.resize(detector.input_width(), detector.input_height())
    else:
        img_ai = img # new copy
    debug_time("resize")
    objs = detector.detect(img_ai, conf_th = 0.5, iou_th = 0.45)
    max_idx = -1
    max_s = 0
    for i, obj in enumerate(objs):
        s = obj.w * obj.h
        if s > max_s:
            max_s = s
            max_idx = i
        # img_ai.draw_rect(obj.x, obj.y, obj.w, obj.h, color = image.COLOR_RED, thickness=4)
        # msg = f'{detector.labels[obj.class_id]}: {obj.score:.2f}'
        # img_ai.draw_string(obj.x, obj.y, msg, color = image.COLOR_RED, scale=2)
    debug_time("detect")
    if max_idx >= 0:
        obj = objs[max_idx]
        w = obj.w + crop_padding * 2
        h = obj.h + crop_padding * 2
        w = w + 1 if w % 2 != 0 else w
        h = h + 1 if h % 2 != 0 else h
        x = obj.x - crop_padding
        y = obj.y - crop_padding
        if x < 0:
            w += x
            x = 0
        if y < 0:
            h += y
            y = 0
        if x + w > img_ai.width():
            w = img_ai.width() - x
        if y + h > img_ai.height():
            h = img_ai.height() - y
        crop_ai = img_ai.crop(x, y, w, h)
        crop_ai_rect = [x, y, w, h]
        # 算出裁切范围对应在大图的位置
        # 注意这里只考虑到了拉伸缩放(iamge.Fit.FILL)
        # 使用预计算的缩放比例，避免每帧重复计算
        # crop_rect = image.resize_map_pos_reverse(img.width(), img.height(), img_ai.width(), img_ai.height(), image.Fit.FIT_FILL, obj.x, obj.y, obj.w, obj.h)
        crop_rect = [int(obj.x * IMG_AI_SCALE[0]), int(obj.y * IMG_AI_SCALE[1]), int(obj.w * IMG_AI_SCALE[0]), int(h * IMG_AI_SCALE[0])]
        img_cv = image.image2cv(img, False, False)
        crop_ai_cv = image.image2cv(crop_ai, False, False)
        debug_time("crop")

        gray = crop_ai.to_format(image.Format.FMT_GRAYSCALE)
        gray_cv = image.image2cv(gray, False, False)
        debug_time("gray")

        # 二值化图，找出黑色外轮廓，可以用其它算法
        # 高斯模糊去噪声
        # blurred = cv2.GaussianBlur(gray_cv, (5, 5), 0)
        # 边缘检测，阈值 0，150
        # edged = cv2.Canny(blurred, 50, 150)
        # # 膨胀处理
        # kernel = np.ones((5, 5), np.uint8)
        # dilated = cv2.dilate(edged, kernel, iterations=1)
        # # 腐蚀处理
        # binary = cv2.erode(dilated, kernel, iterations=1)
        # 自适应二值化，最后两个参数可以调整
        binary = cv2.adaptiveThreshold(gray_cv, 255,
                       cv2.ADAPTIVE_THRESH_MEAN_C,
                       cv2.THRESH_BINARY_INV, 27, 31)
        debug_time("binary")


        if std_from_white_rect:
            # 执行洪泛填充找出内白色轮廓
            h, w = binary.shape[:2]
            mask = np.zeros((h + 2, w + 2), np.uint8)
            # 设置种子点（左上角和右下角），如果环境好，可以只点一个角
            seed_point = (2, 2)
            seed_point2 = (w - 2, h - 2)
            # 设置填充值（白色 255）
            fill_value = 255
            # 执行洪泛填充（以左上角像素值为基准）
            cv2.floodFill(binary, mask, seed_point, fill_value, loDiff=5, upDiff=5, flags=4)
            cv2.floodFill(binary, mask, seed_point2, fill_value, loDiff=5, upDiff=5, flags=4)
            binary = cv2.bitwise_not(binary)
            debug_time("fill")

        # 查找轮廓4个角点
        approx = None
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if len(contours) > 0:
            # 筛选出最大的轮廓
            largest_contour = max(contours, key=cv2.contourArea)
            # 近似多边形
            epsilon = 0.02 * cv2.arcLength(largest_contour, True)
            approx = cv2.approxPolyDP(largest_contour, epsilon, True)
            debug_time("find countours")
            # 如果找到的是一个四边形
            if len(approx) == 4:
                # 获取矩形四个角点
                # 对角点进行排序：左上、右上、右下、左下
                corners = approx.reshape((4, 2))
                # 按顺序排列角点（左上、右上、右下、左下）
                rect = np.zeros((4, 2), dtype="float32")
                s = corners.sum(axis=1)
                rect[0] = corners[np.argmin(s)] # 最小和，左上
                rect[2] = corners[np.argmax(s)] # 最大和，右下
                diff = np.diff(corners, axis=1) # y - x
                rect[3] = corners[np.argmax(diff)] # 差最大，左下
                rect[1] = corners[np.argmin(diff)] # 差最小，右上
                minW = min(rect[1][0] - rect[0][0], rect[2][0] - rect[3][0])
                minH = min(rect[3][1] - rect[0][1], rect[2][1] - rect[1][1])
                if minH > rect_min_limit and minW > rect_min_limit:
                    debug_time("find rect")

                    # 计算目标图像宽高（按最大边计算）
                    # (tl, tr, br, bl) = rect
                    # widthA = np.linalg.norm(br - bl)
                    # widthB = np.linalg.norm(tr - tl)
                    # maxWidth = int(max(widthA, widthB) * img_ai_scale[0] * std_scale)

                    # heightA = np.linalg.norm(tr - br)
                    # heightB = np.linalg.norm(tl - bl)
                    # maxHeight = int(max(heightA, heightB) * img_ai_scale[1] * std_scale)
                    # print(maxWidth, maxHeight)


                    maxWidth = std_res[0]
                    maxHeight = std_res[1]

                    # rect 映射到大图, 从大图中得到标准内框图
                    rect[:, 0] += crop_ai_rect[0]
                    rect[:, 1] += crop_ai_rect[1]
                    rect[:, 0] *= IMG_AI_SCALE[0]
                    rect[:, 1] *= IMG_AI_SCALE[1]
                    # 透视变换
                    dst = np.array([
                        [0, 0],
                        [maxWidth - 1, 0],
                        [maxWidth - 1, maxHeight - 1],
                        [0, maxHeight - 1]], dtype="float32")
                    M = cv2.getPerspectiveTransform(rect, dst)
                    M_inv = np.linalg.inv(M)
                    img_std_cv = cv2.warpPerspective(img_cv, M, (maxWidth, maxHeight))
                    img_std = image.cv2image(img_std_cv, False, False)
                    debug_time("get std img")

                    # 如果前面找到得标准图有黑框，用find_blobs 处理一下
                    # ths = [[0, 10, -128, 127, -128, 127]]
                    # blobs = img_std.find_blobs(ths, roi=[0, 0, 10, 10], x_stride=1, y_stride=1)
                    # A4 纸 21cm, 黑框 1.8*2=3.6cm， 白色区域为 17.4cm，圆圈2cm间距
                    # 得出 圆圈间距像素为 2/17.4 * 白色区域高度像素。（0.1149425287356322）
                    # 如果是黑色边框，则 2/21 * 黑框高度像素。(0.09523809523809523)
                    # if len(blobs) > 0: # 有黑框
                        # circle_dist = img_std.height() * 0.09523809523809523
                    # else:
                    if std_from_white_rect:
                        circle_dist = int(img_std.height() * 0.1149425287356322)
                    else:
                        circle_dist = img_std.height() * 0.09523809523809523
                    if circle_dist > 0:
                        center = [img_std.width() // 2, img_std.height() // 2]
                        # 是否找圆和圆心
                        center_new = None
                        if find_circle:
                            img_std_gray_cv = cv2.cvtColor(img_std_cv, cv2.COLOR_RGB2GRAY)
                            w = h = int(circle_dist * 3)
                            roi = [center[0] - w // 2, center[1] - h // 2, w, h]
                            img_small_circle_cv = img_std_gray_cv[roi[1]:roi[1] + roi[3], roi[0]:roi[0]+roi[2]]
                            if DEBUG:
                                img_small_circle = image.cv2image(img_small_circle_cv, False, False)
                                img.draw_image(crop_ai.width(), img_std.height(), img_small_circle)

                            # 用霍夫变换找圆
                            circles = cv2.HoughCircles(img_small_circle_cv, cv2.HOUGH_GRADIENT, dp=1.2,
                                                    minDist=roi[2] // 2,
                                                    param1=100, param2=20,
                                                    minRadius=roi[2] // 4, maxRadius=roi[2] // 2)
                            # 把找圆范围画出来
                            if DEBUG:
                                img_std.draw_rect(roi[0], roi[1], roi[2], roi[3], image.COLOR_ORANGE)
                                cv2.circle(img_std_cv, center, 1, (0, 255, 0), -1)
                            # 若检测到圆，得到中心和半径
                            circle_dist_new = 0
                            if circles is not None:
                                circles = np.uint16(np.around(circles))
                                for c in circles[0, :]:
                                    center_new = (c[0] + roi[0], c[1] + roi[1])  # 圆心坐标偏移回原图
                                    circle_dist_new = c[2]
                                    if DEBUG:
                                        cv2.circle(img_std_cv, center_new, circle_dist_new, (0, 255, 0), 1)
                                        cv2.circle(img_std_cv, center_new, 1, (0, 0, 255), 3)  # 圆心
                                    # 这里认为只能检测到一个圆，如果多个，那画面有问题，或者再优化这里的代码
                                    break
                            # binary = cv2.adaptiveThreshold(img_std_gray_cv, 255,
                            #                cv2.ADAPTIVE_THRESH_MEAN_C,
                            #                cv2.THRESH_BINARY_INV, 11, 3)
                            # # 膨胀加强线条
                            # kernel = np.ones((2, 2), np.uint8)
                            # enhanced = cv2.dilate(binary, kernel, iterations=1)
                            # eroded = cv2.erode(enhanced, kernel, iterations=1)
                            # circles = img_std.find_circles(roi = roi, x_stride=4, y_stride = 4, threshold=2000, r_step = 4)  # 修复：使用正确的变量名
                            if center_new:
                                # 更新圆环中心和圆环间距离
                                center = center_new
                                circle_dist = circle_dist_new
                                # 在标准图中画出新中心和第三个圈
                                if DEBUG:
                                    cv2.circle(img_std_cv, center, 1, (0, 255, 0), -1)
                                    cv2.circle(img_std_cv, center, circle_dist * 3, (0, 255, 0), 1)
                            debug_time("find circle")

                            # 如果不找圆心，或者找到了圆心
                        if (not find_circle) or (center_new):
                            # 原图画圆中心
                            std_center_points = np.array([[center]], dtype=np.float32)
                            original_center_point = cv2.perspectiveTransform(std_center_points, M_inv)[0][0].astype(np.int32).tolist()
                            # 计算误差（加入偏移补偿，参考111.py）
                            err_center = [
                                original_center_point[0] - (center_pos[0] + offset_x),
                                original_center_point[1] - (center_pos[1] + offset_y),
                            ]

                            # 更新预测跟踪相关变量
                            if enable_predictive_tracking:
                                # 计算运动向量（当前位置 - 上次位置）
                                movement_vector = [
                                    original_center_point[0] - last_valid_center[0],
                                    original_center_point[1] - last_valid_center[1]
                                ]

                                # 更新运动向量（使用低通滤波平滑）
                                alpha = 0.3  # 滤波系数
                                last_movement_vector[0] = alpha * movement_vector[0] + (1 - alpha) * last_movement_vector[0]
                                last_movement_vector[1] = alpha * movement_vector[1] + (1 - alpha) * last_movement_vector[1]

                                # 更新最后有效位置
                                last_valid_center = original_center_point.copy()

                                # 重置预测相关计数
                                prediction_frames_count = 0
                                prediction_strength = 1.0
                                target_lost_frames = 0

                                if DEBUG:
                                    print(f"🎯 目标跟踪: 位置=({original_center_point[0]}, {original_center_point[1]}), 运动向量=({last_movement_vector[0]:.1f}, {last_movement_vector[1]:.1f})")

                            last_center = original_center_point
                            last_center_small = [int(last_center[0] / IMG_AI_SCALE[0]), int(last_center[1] / IMG_AI_SCALE[1])]
                            # 标记当前帧检测到目标
                            target_detected = True
                            should_stop_servo = False  # 检测到目标，不需要停止舵机

                            # 舵机控制逻辑 - 只有在当前帧检测到目标时才执行
                            if servo_control_enabled and target_detected:
                                try:
                                    # 如果舵机未启用且检测到目标，自动启用舵机
                                    if not servo_controller.is_servo_enabled():
                                        print("🎯 检测到目标，自动启用舵机控制...")
                                        servo_controller.enable_servos()

                                    # 如果目标重新出现，重置PID状态
                                    if target_lost:
                                        servo_controller.reset_pid()
                                        target_lost = False

                                    # 计算误差大小
                                    error_magnitude = math.sqrt(err_center[0]**2 + err_center[1]**2)

                                    # 添加详细调试信息
                                    if DEBUG:
                                        # 计算原始误差（不含偏移补偿）
                                        raw_err_x = original_center_point[0] - center_pos[0]
                                        raw_err_y = original_center_point[1] - center_pos[1]
                                        print(f"🎯 舵机控制调试:")
                                        print(f"   原始误差: ({raw_err_x:.1f}, {raw_err_y:.1f})")
                                        print(f"   偏移补偿: ({offset_x}, {offset_y})")
                                        print(f"   补偿后误差: ({err_center[0]:.1f}, {err_center[1]:.1f})")
                                        print(f"   误差大小: {error_magnitude:.1f}px, 阈值: {servo_error_threshold}px")
                                        print(f"   目标检测状态: 有效目标已找到")

                                    if error_magnitude > servo_error_threshold:
                                        # 使用高级PID控制舵机进行逼近
                                        if DEBUG:
                                            print(f"✅ 启动高级PID控制: 误差={error_magnitude:.1f}px > 启动阈值={servo_error_threshold}px")

                                        # 尝试使用高级PID控制，如果失败则回退到传统PID
                                        moved = servo_controller.advanced_pid_control(
                                            original_center_point[0], original_center_point[1],
                                            center_pos[0], center_pos[1]
                                        )

                                        if moved:
                                            no_target_count = 0  # 重置未检测到目标的计数
                                            # 重置稳定帧计数
                                            target_stable_frames = 0
                                    elif error_magnitude <= servo_stop_threshold:
                                        # 误差很小，目标已精确居中，停止舵机（使用更小的停止阈值）
                                        if DEBUG:
                                            print(f"⏸ 目标精确居中，停止舵机: 误差={error_magnitude:.1f}px <= 停止阈值={servo_stop_threshold}px")
                                        servo_controller.stop_horizontal()
                                        # 强制清除PID积分项
                                        servo_controller.horizontal_error_sum = 0

                                        # A22触发逻辑：目标稳定居中时触发
                                        if enable_a22_trigger:
                                            target_stable_frames += 1
                                            if target_stable_frames >= a22_stable_frames_required:
                                                current_time = time.time()
                                                # 避免频繁触发，至少间隔3秒
                                                if current_time - last_trigger_time > 3.0:
                                                    if DEBUG:
                                                        print(f"🎯 目标稳定居中{target_stable_frames}帧，触发A22低电平信号")
                                                    # 触发A22低电平
                                                    a22_relay.turn_on_relay()  # 输出低电平
                                                    a22_trigger_active = True
                                                    a22_trigger_start_time = current_time
                                                    last_trigger_time = current_time
                                                    target_stable_frames = 0  # 重置计数
                                    else:
                                        # 误差在启动阈值和停止阈值之间，使用轻微控制（滞后区域）
                                        if DEBUG:
                                            print(f"🔄 滞后区域，轻微控制: 误差={error_magnitude:.1f}px (停止阈值={servo_stop_threshold}px < 误差 <= 启动阈值={servo_error_threshold}px)")
                                        # 使用轻微的PID控制，避免完全停止
                                        servo_controller.pid_control(err_center[0] * 0.3, err_center[1] * 0.3)  # 降低控制强度
                                        # 重置稳定帧计数
                                        target_stable_frames = 0

                                except Exception as e:
                                    print(f"❌ 舵机控制异常: {e}")
                                    # 安全停止舵机
                                    try:
                                        servo_controller.stop_horizontal()
                                    except:
                                        print("❌ 紧急停止舵机失败")
                            # 原图画圆
                            radius = circle_dist * 3 # 第三个圈的半径
                            # 构造圆上的轮廓点
                            debug_time("get points 3")
                            angles = np.linspace(0, 2 * np.pi, circle_num_points, endpoint=False)  # endpoint=False 避免首尾重复
                            cos_vals = np.cos(angles)
                            sin_vals = np.sin(angles)

                            # 向量方式生成所有点
                            x = center[0] + radius * cos_vals
                            y = center[1] + radius * sin_vals
                            circle_pts = np.stack((x, y), axis=1).astype(np.float32)  # shape: (N, 2)
                            circle_pts = circle_pts[np.newaxis, :, :]  # reshape to (1, N, 2)
                            debug_time("get points 1")

                            # 反变换回原图
                            orig_circle_pts = cv2.perspectiveTransform(circle_pts, M_inv)
                            debug_time("get points")

                            # 找激光点
                            original_lasert_point = None
                            laser_offset_info = None
                            if find_laser:
                                laser_point = find_laser_point(img_std, img if DEBUG else img_ai)
                                if laser_point:
                                    # 原图坐标
                                    points = np.array([[[laser_point.x(), laser_point.y()]]], dtype=np.float32)
                                    original_lasert_point = cv2.perspectiveTransform(points, M_inv)[0][0]

                                    # 激光点校准逻辑
                                    if enable_laser_calibration:
                                        # 计算激光点与目标点的偏差
                                        laser_error_x = original_lasert_point[0] - original_center_point[0]
                                        laser_error_y = original_lasert_point[1] - original_center_point[1]

                                        # 应用校准偏移
                                        corrected_laser_x = original_lasert_point[0] - laser_offset_x
                                        corrected_laser_y = original_lasert_point[1] - laser_offset_y

                                        # 计算校准后的偏差
                                        corrected_error_x = corrected_laser_x - original_center_point[0]
                                        corrected_error_y = corrected_laser_y - original_center_point[1]
                                        corrected_error_magnitude = math.sqrt(corrected_error_x**2 + corrected_error_y**2)

                                        laser_offset_info = {
                                            'raw_error': (laser_error_x, laser_error_y),
                                            'corrected_error': (corrected_error_x, corrected_error_y),
                                            'corrected_magnitude': corrected_error_magnitude,
                                            'corrected_position': (corrected_laser_x, corrected_laser_y)
                                        }

                                        if DEBUG or laser_show_offset:
                                            print(f"🔵 激光点校准: 原始偏差=({laser_error_x:.1f}, {laser_error_y:.1f}), "
                                                  f"校准后偏差=({corrected_error_x:.1f}, {corrected_error_y:.1f}), "
                                                  f"总偏差={corrected_error_magnitude:.1f}px")

                                        # 在校准模式下，建议调整偏移值
                                        if laser_calibration_mode and corrected_error_magnitude > 5:
                                            suggested_offset_x = laser_offset_x + laser_error_x * 0.8
                                            suggested_offset_y = laser_offset_y + laser_error_y * 0.8
                                            print(f"💡 建议校准值: laser_offset_x = {suggested_offset_x:.1f}, laser_offset_y = {suggested_offset_y:.1f}")
                                            print(f"   请修改main.py第{1037}行和第{1038}行的参数")
                            # 画在大图上
                            if DEBUG or debug_show_hires:
                                # 画目标中心点（红色）
                                img.draw_circle(original_center_point[0], original_center_point[1], 4, image.COLOR_RED, thickness=-1)
                                pts = np.round(orig_circle_pts[0]).astype(np.int32)
                                cv2.polylines(img_cv, [pts], isClosed=True, color=(0, 0, 255), thickness=1)

                                # 画激光点相关信息
                                if original_lasert_point is not None:
                                    # 画原始激光点（绿色）
                                    img.draw_circle(int(original_lasert_point[0]), int(original_lasert_point[1]), 3, image.COLOR_GREEN, thickness=-1)

                                    # 如果启用校准，画校准后的激光点位置（蓝色）和连接线
                                    if enable_laser_calibration and laser_offset_info:
                                        corrected_pos = laser_offset_info['corrected_position']
                                        # 画校准后的激光点位置（蓝色）
                                        img.draw_circle(int(corrected_pos[0]), int(corrected_pos[1]), 3, image.COLOR_BLUE, thickness=-1)

                                        # 画从原始激光点到校准位置的线（黄色）
                                        img.draw_line(int(original_lasert_point[0]), int(original_lasert_point[1]),
                                                     int(corrected_pos[0]), int(corrected_pos[1]),
                                                     image.COLOR_YELLOW, thickness=2)

                                        # 画从校准后激光点到目标点的线（紫色）
                                        img.draw_line(int(corrected_pos[0]), int(corrected_pos[1]),
                                                     original_center_point[0], original_center_point[1],
                                                     image.COLOR_PURPLE, thickness=2)
                                    else:
                                        # 未启用校准时，画从激光点到目标点的直线（白色）
                                        img.draw_line(int(original_lasert_point[0]), int(original_lasert_point[1]),
                                                     original_center_point[0], original_center_point[1],
                                                     image.COLOR_WHITE, thickness=2)
                            else:
                            # 画在小图上显示
                                # too slow
                                # center_ai = image.resize_map_pos(img.width(), img.height(), img_ai.width(), img_ai.height(), image.Fit.FIT_FILL, original_center_point[0], original_center_point[1])
                                center_ai = [int(original_center_point[0] / IMG_AI_SCALE[0]), int(original_center_point[1] / IMG_AI_SCALE[1])]
                                img_ai.draw_circle(center_ai[0], center_ai[1], 2, image.COLOR_RED, thickness=-1)
                                pts = orig_circle_pts[0]  # shape: (N, 2)

                                scaled_pts = (pts / IMG_AI_SCALE).astype(np.int32)  # shape: (N, 2) - 修复：使用正确的缩放方向
                                points = scaled_pts.reshape(-1).tolist()  # 转为 Python list（与原结果相同）
                                if debug_draw_circle:
                                    img_ai.draw_keypoints(points, image.COLOR_RED, 1, line_thickness=1)

                                # 在小图上显示激光点
                                if original_lasert_point is not None:
                                    laser_ai = [int(original_lasert_point[0] / IMG_AI_SCALE[0]), int(original_lasert_point[1] / IMG_AI_SCALE[1])]
                                    img_ai.draw_circle(laser_ai[0], laser_ai[1], 2, image.COLOR_GREEN, thickness=-1)

                                    # 如果启用校准，显示校准信息
                                    if enable_laser_calibration and laser_offset_info:
                                        corrected_pos = laser_offset_info['corrected_position']
                                        corrected_ai = [int(corrected_pos[0] / IMG_AI_SCALE[0]), int(corrected_pos[1] / IMG_AI_SCALE[1])]
                                        img_ai.draw_circle(corrected_ai[0], corrected_ai[1], 2, image.COLOR_BLUE, thickness=-1)

                                        # 画连接线
                                        img_ai.draw_line(laser_ai[0], laser_ai[1], corrected_ai[0], corrected_ai[1],
                                                        image.COLOR_YELLOW, thickness=1)
                                        img_ai.draw_line(corrected_ai[0], corrected_ai[1], center_ai[0], center_ai[1],
                                                        image.COLOR_PURPLE, thickness=1)
                                    else:
                                        # 画从激光点到目标的直线
                                        img_ai.draw_line(laser_ai[0], laser_ai[1], center_ai[0], center_ai[1],
                                                        image.COLOR_WHITE, thickness=1)
                            debug_time("draw points")
                        if DEBUG:
                            img.draw_image(crop_ai.width(), 0, img_std)
                    else:
                        print("detected circle too small", img_std.width(), img_std.height())
                else:
                    print(minW, minH, "rect not valid")
                    # 未检测到有效矩形，启用预测跟踪
                    target_lost_frames += 1

                    if enable_predictive_tracking and prediction_frames_count < max_prediction_frames:
                        # 预测跟踪模式
                        prediction_frames_count += 1
                        prediction_strength *= prediction_decay_factor  # 预测强度衰减

                        if prediction_strength > min_prediction_strength:
                            # 根据运动向量预测下一个位置
                            predicted_center = [
                                last_valid_center[0] + last_movement_vector[0] * prediction_frames_count * prediction_strength,
                                last_valid_center[1] + last_movement_vector[1] * prediction_frames_count * prediction_strength
                            ]

                            # 限制预测位置在画面范围内并转换为整数
                            predicted_center[0] = int(max(0, min(cam.width() - 1, predicted_center[0])))
                            predicted_center[1] = int(max(0, min(cam.height() - 1, predicted_center[1])))

                            # 计算预测误差（加入偏移补偿）
                            err_center = [
                                predicted_center[0] - (center_pos[0] + offset_x),
                                predicted_center[1] - (center_pos[1] + offset_y)
                            ]
                            last_center = predicted_center

                            if DEBUG:
                                print(f"🔮 预测跟踪: 帧数={prediction_frames_count}, 强度={prediction_strength:.2f}, 预测位置=({predicted_center[0]:.0f}, {predicted_center[1]:.0f})")
                        else:
                            # 预测强度太低，停止预测
                            err_center = [0, 0]
                            # 保留最后一次检测到的位置，不重置到画面中心（红线保留功能）
                            # last_center = center_pos.copy()  # 注释掉重置，保留红线
                            if DEBUG:
                                print("🔮 预测强度过低，停止预测跟踪")
                    else:
                        # 不使用预测跟踪或预测帧数超限
                        err_center = [0, 0]
                        # 保留最后一次检测到的位置，不重置到画面中心（红线保留功能）
                        # last_center = center_pos.copy()  # 注释掉重置，保留红线
                        # last_center_small = [detector.input_width() // 2, detector.input_height() // 2]  # 注释掉重置

                    if servo_control_enabled:
                        no_target_count += 1
                        # 如果不在预测跟踪模式，立即停止舵机
                        if not (enable_predictive_tracking and prediction_frames_count < max_prediction_frames and prediction_strength > min_prediction_strength):
                            servo_controller.stop_horizontal()
                        if no_target_count > servo_max_no_target_frames:
                            servo_controller.center_servos()  # 回到中位
                            target_lost = True  # 设置目标丢失标志
                            if DEBUG:
                                print("长时间未检测到目标，舵机回中位")
            else:
                # 未检测到任何目标，启用预测跟踪
                target_lost_frames += 1

                if enable_predictive_tracking and prediction_frames_count < max_prediction_frames:
                    # 预测跟踪模式
                    prediction_frames_count += 1
                    prediction_strength *= prediction_decay_factor  # 预测强度衰减

                    if prediction_strength > min_prediction_strength:
                        # 根据运动向量预测下一个位置
                        predicted_center = [
                            last_valid_center[0] + last_movement_vector[0] * prediction_frames_count * prediction_strength,
                            last_valid_center[1] + last_movement_vector[1] * prediction_frames_count * prediction_strength
                        ]

                        # 限制预测位置在画面范围内并转换为整数
                        predicted_center[0] = int(max(0, min(cam.width() - 1, predicted_center[0])))
                        predicted_center[1] = int(max(0, min(cam.height() - 1, predicted_center[1])))

                        # 计算预测误差（加入偏移补偿）
                        err_center = [
                            predicted_center[0] - (center_pos[0] + offset_x),
                            predicted_center[1] - (center_pos[1] + offset_y)
                        ]
                        last_center = predicted_center

                        if DEBUG:
                            print(f"🔮 预测跟踪: 帧数={prediction_frames_count}, 强度={prediction_strength:.2f}, 预测位置=({predicted_center[0]:.0f}, {predicted_center[1]:.0f})")
                    else:
                        # 预测强度太低，停止预测
                        err_center = [0, 0]
                        # 保留最后一次检测到的位置，不重置到画面中心（红线保留功能）
                        # last_center = center_pos.copy()  # 注释掉重置，保留红线
                        if DEBUG:
                            print("🔮 预测强度过低，停止预测跟踪")
                else:
                    # 不使用预测跟踪或预测帧数超限
                    err_center = [0, 0]
                    # 保留最后一次检测到的位置，不重置到画面中心（红线保留功能）
                    # last_center = center_pos.copy()  # 注释掉重置，保留红线
                    # last_center_small = [detector.input_width() // 2, detector.input_height() // 2]  # 注释掉重置

                if servo_control_enabled:
                    no_target_count += 1
                    # 如果不在预测跟踪模式，立即停止舵机
                    if not (enable_predictive_tracking and prediction_frames_count < max_prediction_frames and prediction_strength > min_prediction_strength):
                        # 只在第一次检测失败时停止舵机，避免重复调用
                        if no_target_count == 1:
                            servo_controller.stop_horizontal()
                            if DEBUG:
                                print(f"🛑 首次检测失败，停止水平舵机")

                    if no_target_count > servo_max_no_target_frames:
                        servo_controller.center_servos()  # 回到中位
                        target_lost = True  # 设置目标丢失标志
                        if DEBUG:
                            print("长时间未检测到目标，舵机回中位")

        # 绘制路径
        if approx is not None:
            cv2.drawContours(crop_ai_cv, [approx], -1, (255, 255, 255), 1)
        if DEBUG:
            img.draw_image(0, 0, crop_ai)
            img2 = image.cv2image(binary, False, False)
            img.draw_image(0, crop_ai.height(), img2)

        if debug_draw_rect:
            img.draw_rect(crop_rect[0], crop_rect[1], crop_rect[2], crop_rect[3], color = image.COLOR_RED, thickness=2)
            # msg = f'{detector.labels[obj.class_id]}: {obj.score:.2f}'
            # img.draw_string(obj.x, obj.y, msg, color = image.COLOR_RED, scale=2)
        debug_time("draw")
    if DEBUG or debug_show_hires:
        if debug_draw_err_line:
            # 始终画红线，显示最后一次检测到的目标位置（红线保留功能）
            # 确保坐标为整数，避免浮点数导致的TypeError
            img.draw_line(center_pos[0], center_pos[1], int(last_center[0]), int(last_center[1]), image.COLOR_RED, thickness=3)
        if debug_draw_err_msg:
            # 显示误差信息和舵机状态
            status_text = f"err: {err_center[0]:5.1f}, {err_center[1]:5.1f}, fps: {time.fps():2.0f}"
            if servo_control_enabled:
                # 计算当前误差大小
                current_error = math.sqrt(err_center[0]**2 + err_center[1]**2)
                if current_error <= servo_error_threshold:
                    status_text += f", 目标居中({current_error:.0f}px), V:{servo_controller.vertical_current_angle:.0f}°"
                else:
                    status_text += f", 逼近中({current_error:.0f}px), V:{servo_controller.vertical_current_angle:.0f}°"
            else:
                status_text += ", servo: OFF"
            img.draw_string(2, img.height() - 32, status_text, image.COLOR_RED, scale=1.5, thickness=2)
        disp.show(img)
    else:
        if debug_draw_err_line:
            # 始终画红线，显示最后一次检测到的目标位置（红线保留功能）
            # 确保坐标为整数，避免浮点数导致的TypeError
            img_ai.draw_line(center_pos[0], center_pos[1], int(last_center_small[0]), int(last_center_small[1]), image.COLOR_RED, thickness=3)
        if debug_draw_err_msg:
            # 显示误差信息和舵机状态
            status_text = f"err: {err_center[0]:5.1f}, {err_center[1]:5.1f}, fps: {time.fps():2.0f}"
            if servo_control_enabled:
                # 计算当前误差大小
                current_error = math.sqrt(err_center[0]**2 + err_center[1]**2)
                if current_error <= servo_error_threshold:
                    status_text += f", 目标居中({current_error:.0f}px), V:{servo_controller.vertical_current_angle:.0f}°"
                else:
                    status_text += f", 逼近中({current_error:.0f}px), V:{servo_controller.vertical_current_angle:.0f}°"
            else:
                status_text += ", servo: OFF"
            img_ai.draw_string(2, img.height() - 32, status_text, image.COLOR_RED, scale=1.5, thickness=2)
        disp.show(img_ai)
    debug_time("display img")

    # 检查A22触发时间，1秒后恢复高电平
    if a22_trigger_active and enable_a22_trigger:
        current_time = time.time()
        if current_time - a22_trigger_start_time >= a22_trigger_duration:
            # 1秒时间到，恢复高电平
            a22_relay.turn_off_relay()  # 输出高电平
            a22_trigger_active = False
            if DEBUG:
                print(f"⏰ A22触发时间到({a22_trigger_duration}秒)，恢复高电平")

    # 每帧结束时检查是否需要停止舵机（避免重复调用）
    # 优化：只在第一次检测失败时停止，避免重复调用
    if should_stop_servo and servo_control_enabled and no_target_count == 1:
        try:
            servo_controller.stop_horizontal()
            if DEBUG:
                print("🛑 当前帧未检测到目标，停止舵机")
        except Exception as e:
            print(f"❌ 停止舵机失败: {e}")

# 程序退出时清理资源
print("程序退出，清理资源...")
servo_controller.cleanup()
print("程序已退出")