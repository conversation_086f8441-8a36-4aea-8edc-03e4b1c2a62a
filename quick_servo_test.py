#!/usr/bin/env python3
"""
MG996R 360度舵机快速测试脚本
专门解决只能单向转动的问题

@author: AI Assistant  
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def test_servo():
    """快速测试舵机双向转动"""
    print("🔧 MG996R 360度舵机快速测试")
    print("="*35)
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        # 测试序列 - 每次转动后都回到停止位置，便于观察
        test_sequence = [
            (7.5, "初始停止位置", 2),

            # 第一轮：小幅度测试
            (8.0, "小幅顺时针（向左）", 3),
            (7.5, "回到停止位置", 2),
            (7.0, "小幅逆时针（向右）", 3),
            (7.5, "回到停止位置", 2),

            # 第二轮：中等幅度测试
            (8.5, "中等顺时针（向左）", 3),
            (7.5, "回到停止位置", 2),
            (6.5, "中等逆时针（向右）", 3),
            (7.5, "回到停止位置", 2),

            # 第三轮：较大幅度测试
            (9.0, "较快顺时针（向左）", 3),
            (7.5, "回到停止位置", 2),
            (6.0, "较快逆时针（向右）", 3),
            (7.5, "最终停止位置", 3)
        ]
        
        print("\n开始测试序列...")
        print("请仔细观察舵机转动方向：")
        print("- 顺时针：应该向左转")
        print("- 逆时针：应该向右转")
        print("- 停止：应该完全停止")
        print("- 每次转动后都会回到停止位置")

        step_count = 0
        for duty, description, duration in test_sequence:
            step_count += 1
            print(f"\n步骤 {step_count}: {duty:.1f}% ({description})")
            servo_pwm.duty(duty)

            # 显示预期行为和观察要点
            if duty > 7.5:
                expected = "← 预期：顺时针转动（向左）"
                observe = "观察：舵机是否向左转动？"
            elif duty < 7.5:
                expected = "→ 预期：逆时针转动（向右）"
                observe = "观察：舵机是否向右转动？"
            else:
                expected = "⏸ 预期：完全停止"
                observe = "观察：舵机是否完全停止？"

            print(f"  {expected}")
            print(f"  {observe}")

            # 显示倒计时
            for i in range(duration, 0, -1):
                print(f"  等待 {i} 秒...", end='\r')
                time.sleep(1)
            print("  完成！" + " " * 20)  # 清除倒计时显示
        
        print("\n" + "="*35)
        print("测试完成！请根据观察结果判断：")
        print("\n✅ 如果舵机能双向转动：")
        print("   - 问题已解决！")
        print("   - 可以正常使用main.py")
        
        print("\n❌ 如果舵机只能单向转动：")
        print("   - 可能需要调整PWM范围")
        print("   - 尝试以下参数：")
        print("     方案A: 顺时针8.0%, 逆时针7.0%")
        print("     方案B: 顺时针9.0%, 逆时针6.0%")
        print("     方案C: 检查舵机是否为真正的360度版本")
        
        print("\n⚠️ 如果舵机完全不动：")
        print("   - 检查电源供应（需要足够电流）")
        print("   - 检查信号线连接（A18引脚）")
        print("   - 检查舵机本身是否损坏")
        
        print("\n🔄 如果转动方向相反：")
        print("   - 修改main.py第254行")
        print("   - 将 horizontal_error = -err_x")
        print("   - 改为 horizontal_error = err_x")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("请检查：")
        print("1. MaixCAM Pro是否支持PWM6")
        print("2. A18引脚连接是否正确")
        print("3. 舵机电源是否正常")
    
    print("\n程序结束")

if __name__ == "__main__":
    test_servo()
