#!/usr/bin/env python3
"""
单向舵机问题诊断
专门解决只能顺时针转动的问题

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def find_exact_stop_position():
    """精确寻找停止位置"""
    print("🎯 精确寻找停止位置")
    print("="*25)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        print("\n已知7.8%能动，7.6%不动，精确测试7.6%~7.8%之间：")
        
        # 精确测试范围
        test_range = [7.60, 7.61, 7.62, 7.63, 7.64, 7.65, 7.66, 7.67, 7.68, 7.69, 7.70, 7.71, 7.72, 7.73, 7.74, 7.75, 7.76, 7.77, 7.78, 7.79, 7.80]
        
        for duty in test_range:
            print(f"\n测试: {duty:.2f}%")
            servo_pwm.duty(duty)
            print(f"  观察3秒，舵机是否转动...")
            time.sleep(3)
            
            # 回到已知的停止位置
            servo_pwm.duty(7.6)
            time.sleep(1)
        
        print("\n精确停止位置测试完成")
        print("请记录第一个让舵机开始转动的PWM值")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_extreme_reverse():
    """测试极端逆时针PWM值"""
    print("\n⚡ 极端逆时针测试")
    print("="*25)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        print("\n测试极端小的PWM值，看能否逆时针转动：")
        print("⚠️ 使用极端值，请小心观察")
        
        # 极端逆时针测试
        extreme_reverse = [7.0, 6.5, 6.0, 5.5, 5.0, 4.5, 4.0, 3.5, 3.0]
        
        for duty in extreme_reverse:
            print(f"\n测试极端逆时针: {duty:.1f}%")
            try:
                servo_pwm.duty(duty)
                print(f"  观察4秒，舵机是否逆时针转动...")
                time.sleep(4)
                
                # 回到安全位置
                servo_pwm.duty(7.6)
                time.sleep(1)
                
            except Exception as e:
                print(f"  ❌ 设置{duty:.1f}%失败: {e}")
        
        print("\n极端逆时针测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_servo_type():
    """判断舵机类型"""
    print("\n🔍 舵机类型判断")
    print("="*20)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.6, enable=True)  # 使用已知的停止位置
        print("✓ PWM初始化成功")
        
        print("\n判断舵机类型：")
        
        # 测试1：长时间顺时针转动
        print("\n测试1：长时间顺时针转动")
        print("设置7.8%，观察10秒...")
        servo_pwm.duty(7.8)
        time.sleep(10)
        
        print("如果舵机连续转动超过360度，说明是连续旋转舵机")
        print("如果舵机转动一定角度后停止，说明是普通180度舵机")
        
        # 回到停止
        servo_pwm.duty(7.6)
        time.sleep(2)
        
        # 测试2：不同速度测试
        print("\n测试2：不同速度测试")
        speed_tests = [
            (7.7, "慢速"),
            (7.8, "中速"),
            (7.9, "快速"),
            (8.0, "更快"),
            (8.2, "很快")
        ]
        
        for duty, description in speed_tests:
            print(f"\n{description}测试: {duty:.1f}%")
            servo_pwm.duty(duty)
            print(f"  观察3秒，记录转动速度...")
            time.sleep(3)
            
            # 停止
            servo_pwm.duty(7.6)
            time.sleep(1)
        
        print("\n舵机类型测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_alternative_ranges():
    """测试替代PWM范围"""
    print("\n🔄 替代PWM范围测试")
    print("="*25)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.6, enable=True)
        print("✓ PWM初始化成功")
        
        print("\n如果这是单向连续旋转舵机，测试不同的控制方案：")
        
        # 方案1：只使用顺时针，通过脉冲控制方向
        print("\n方案1：脉冲控制测试")
        print("快速切换PWM值模拟双向控制")
        
        # 模拟"逆时针"：快速脉冲
        print("模拟逆时针（快速脉冲）：")
        for i in range(10):
            servo_pwm.duty(7.8)  # 短暂顺时针
            time.sleep(0.1)
            servo_pwm.duty(7.6)  # 停止
            time.sleep(0.1)
        
        time.sleep(2)
        
        # 模拟"顺时针"：持续转动
        print("模拟顺时针（持续转动）：")
        servo_pwm.duty(7.8)
        time.sleep(3)
        servo_pwm.duty(7.6)
        
        print("\n方案2：变速控制测试")
        print("使用不同速度模拟方向控制")
        
        # 慢速"逆时针"
        print("慢速（模拟逆时针）：")
        servo_pwm.duty(7.7)
        time.sleep(3)
        servo_pwm.duty(7.6)
        time.sleep(1)
        
        # 快速"顺时针"
        print("快速（模拟顺时针）：")
        servo_pwm.duty(8.0)
        time.sleep(3)
        servo_pwm.duty(7.6)
        
        print("\n替代方案测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🔧 单向舵机问题诊断")
    print("="*25)
    
    try:
        print("诊断舵机只能顺时针转动的问题...")
        print("已知信息：")
        print("- 7.8% 能动（顺时针，有点快）")
        print("- 7.4%, 7.5%, 7.6% 不动")
        print("- 7.2% 不动（逆时针方向）")
        
        find_exact_stop_position()
        test_extreme_reverse()
        test_servo_type()
        test_alternative_ranges()
        
        print("\n" + "="*25)
        print("🏁 诊断完成！")
        print("\n📋 可能的结论：")
        print("1. 如果极端逆时针PWM值能让舵机转动：")
        print("   → 这是真正的360度舵机，需要更大的PWM范围")
        print("2. 如果所有逆时针PWM都不能转动：")
        print("   → 这可能是单向连续旋转舵机或有缺陷的舵机")
        print("3. 如果舵机只能转动有限角度：")
        print("   → 这是普通的180度舵机，不是360度版本")
        
        print("\n🔧 解决方案：")
        print("- 如果是真正的360度舵机：更新PWM范围")
        print("- 如果是单向舵机：使用变速或脉冲控制")
        print("- 如果是180度舵机：更换为真正的360度舵机")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
