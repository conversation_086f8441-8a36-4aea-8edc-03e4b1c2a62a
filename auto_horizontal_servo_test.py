#!/usr/bin/env python3
"""
自动化水平舵机测试
无需用户交互，自动执行所有测试

解决 "EOF when reading a line" 错误
适用于MaixCAM Pro环境

@author: AI Assistant
@date: 2025.8.2
"""

from maix import pwm, pinmap, time
import sys

class AutoHorizontalServoTest:
    def __init__(self):
        """初始化自动测试"""
        print("🚀 自动化水平舵机测试")
        print("=" * 40)
        
        # 舵机参数
        self.pwm_channel = 6
        self.gpio_pin = "A18"
        self.pwm_freq = 50
        
        # LD-3015MG 270度位置舵机参数
        self.min_duty = 2.5
        self.max_duty = 12.5
        self.center_duty = 7.5
        
        # 角度范围
        self.min_angle = 45
        self.max_angle = 225
        self.center_angle = 135
        
        # PWM对象
        self.pwm = None
        self.enabled = False
        
        # 执行自动测试
        self.run_auto_test()
    
    def init_pwm(self):
        """初始化PWM"""
        try:
            print(f"🔌 配置引脚: {self.gpio_pin} -> PWM{self.pwm_channel}")
            pinmap.set_pin_function(self.gpio_pin, f"PWM{self.pwm_channel}")
            
            print(f"⚡ 初始化PWM{self.pwm_channel}: {self.pwm_freq}Hz")
            self.pwm = pwm.PWM(self.pwm_channel, freq=self.pwm_freq, duty=0, enable=False)
            
            print("✅ PWM初始化成功")
            return True
            
        except Exception as e:
            print(f"❌ PWM初始化失败: {e}")
            return False
    
    def angle_to_duty(self, angle):
        """角度转PWM占空比"""
        angle = max(self.min_angle, min(self.max_angle, angle))
        duty = self.min_duty + ((angle - self.min_angle) / (self.max_angle - self.min_angle)) * (self.max_duty - self.min_duty)
        return duty
    
    def set_angle(self, angle):
        """设置舵机角度"""
        if self.pwm is None:
            return False
        
        try:
            if not self.enabled:
                self.pwm.enable()
                self.enabled = True
                print("🔒 PWM已启用")
            
            duty = self.angle_to_duty(angle)
            self.pwm.duty(duty)
            print(f"✅ 角度: {angle}°, PWM: {duty:.2f}%")
            return True
            
        except Exception as e:
            print(f"❌ 设置角度失败: {e}")
            return False
    
    def disable_pwm(self):
        """禁用PWM"""
        if self.pwm and self.enabled:
            try:
                self.pwm.disable()
                self.enabled = False
                print("🔓 PWM已禁用")
                return True
            except Exception as e:
                print(f"❌ 禁用失败: {e}")
                return False
        return True
    
    def test_basic_movement(self):
        """基础运动测试"""
        print("\n🧪 基础运动测试")
        print("-" * 25)
        
        test_sequence = [
            (135, "中心位置", 2),
            (90, "左转45度", 2),
            (135, "回中心", 1),
            (180, "右转45度", 2),
            (135, "回中心", 1),
            (60, "左转75度", 2),
            (210, "右转75度", 2),
            (135, "最终回中心", 2)
        ]
        
        for angle, description, delay in test_sequence:
            print(f"📍 {description}: {angle}°")
            if self.set_angle(angle):
                time.sleep(delay)
            else:
                print("❌ 测试失败")
                return False
        
        print("✅ 基础运动测试完成")
        return True
    
    def test_pwm_range(self):
        """PWM范围测试"""
        print("\n🧪 PWM范围测试")
        print("-" * 20)
        
        test_duties = [2.5, 4.0, 5.5, 7.0, 7.5, 8.0, 9.5, 11.0, 12.5]
        
        for duty in test_duties:
            print(f"📊 PWM: {duty}%")
            try:
                if not self.enabled:
                    self.pwm.enable()
                    self.enabled = True
                
                self.pwm.duty(duty)
                time.sleep(1.5)
            except Exception as e:
                print(f"❌ PWM设置失败: {e}")
                return False
        
        # 回到中心
        print(f"🎯 回到中心: {self.center_duty}%")
        self.pwm.duty(self.center_duty)
        time.sleep(1)
        
        print("✅ PWM范围测试完成")
        return True
    
    def test_precision_control(self):
        """精密控制测试"""
        print("\n🧪 精密控制测试")
        print("-" * 20)
        
        center = self.center_angle
        test_angles = [
            (center, "中心位置"),
            (center + 5, "右5度"),
            (center - 5, "左5度"),
            (center + 10, "右10度"),
            (center - 10, "左10度"),
            (center + 20, "右20度"),
            (center - 20, "左20度"),
            (center, "回中心")
        ]
        
        for angle, description in test_angles:
            print(f"📐 {description}: {angle}°")
            if self.set_angle(angle):
                time.sleep(1)
            else:
                print("❌ 测试失败")
                return False
        
        print("✅ 精密控制测试完成")
        return True
    
    def run_auto_test(self):
        """运行自动测试"""
        try:
            # 1. 初始化PWM
            if not self.init_pwm():
                print("❌ 初始化失败，测试终止")
                return
            
            print(f"\n📋 舵机信息:")
            print(f"   型号: LD-3015MG 270度位置舵机")
            print(f"   引脚: {self.gpio_pin} -> PWM{self.pwm_channel}")
            print(f"   频率: {self.pwm_freq}Hz")
            print(f"   角度范围: {self.min_angle}° - {self.max_angle}°")
            print(f"   中心位置: {self.center_angle}°")
            
            # 2. 基础运动测试
            if not self.test_basic_movement():
                print("❌ 基础运动测试失败")
                return
            
            # 3. PWM范围测试
            if not self.test_pwm_range():
                print("❌ PWM范围测试失败")
                return
            
            # 4. 精密控制测试
            if not self.test_precision_control():
                print("❌ 精密控制测试失败")
                return
            
            print("\n🎉 所有测试完成！")
            print("=" * 40)
            print("📋 测试结果:")
            print("✅ PWM初始化: 成功")
            print("✅ 基础运动: 成功")
            print("✅ PWM范围: 成功")
            print("✅ 精密控制: 成功")
            
            print("\n💡 使用建议:")
            print("1. 舵机工作正常，可以集成到main.py")
            print("2. 如需调整角度范围，修改min_angle和max_angle")
            print("3. 如需调整PWM范围，修改min_duty和max_duty")
            
        except KeyboardInterrupt:
            print("\n⚠️ 用户中断测试")
        
        except Exception as e:
            print(f"\n❌ 测试过程中出错: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            # 清理资源
            print("\n🧹 清理资源...")
            self.disable_pwm()
            print("👋 测试结束")

def main():
    """主函数"""
    print("🔧 MaixCAM Pro 水平舵机自动化测试")
    print("解决EOF输入错误，无需用户交互")
    print("=" * 50)
    
    # 运行自动测试
    AutoHorizontalServoTest()

if __name__ == "__main__":
    main()
