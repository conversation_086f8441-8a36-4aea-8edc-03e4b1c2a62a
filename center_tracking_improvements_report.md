# 中心跟踪改进报告

## 修改概述

参考111.py的设计，对main.py进行了以下关键改进：

### 1. 偏移补偿功能（打靶精度修正）

#### 新增配置参数
```python
# 偏移补偿参数（参考111.py，用于修正打靶位置）
offset_x = -12                             # X轴偏移补偿（像素）
offset_y = -12                             # Y轴偏移补偿（像素）
```

#### 误差计算修正
**修改前：**
```python
err_center = [
    original_center_point[0] - center_pos[0],
    original_center_point[1] - center_pos[1],
]
```

**修改后：**
```python
# 计算误差（加入偏移补偿，参考111.py）
err_center = [
    original_center_point[0] - (center_pos[0] + offset_x),
    original_center_point[1] - (center_pos[1] + offset_y),
]
```

#### 补偿原理
- `offset_x = -12`: 向左补偿12像素，让舵机提前12像素停止
- `offset_y = -12`: 向上补偿12像素，让舵机提前12像素停止
- 用于补偿激光器与摄像头之间的机械偏差

#### 调整方法
- 如果打靶偏右 → 减小offset_x值（更负）
- 如果打靶偏左 → 增大offset_x值（更正）
- 如果打靶偏下 → 减小offset_y值（更负）
- 如果打靶偏上 → 增大offset_y值（更正）

### 2. 舵机启动控制改进

#### 新增配置参数
```python
servo_auto_init = False                    # 程序启动时是否自动初始化舵机（False=手动启用）
```

#### 启动行为修改
**修改前：**
- 程序启动时自动启用舵机
- 舵机立即锁定到中心位置

**修改后：**
- 程序启动时舵机保持禁用状态（可手动搬动）
- 检测到目标时自动启用舵机控制
- 或使用测试选项7手动启用

#### 自动启用逻辑
```python
# 如果舵机未启用且检测到目标，自动启用舵机
if not servo_controller.is_servo_enabled():
    print("🎯 检测到目标，自动启用舵机控制...")
    servo_controller.enable_servos()
```

### 3. 调试信息增强

#### 详细的偏移补偿调试
```python
if DEBUG:
    # 计算原始误差（不含偏移补偿）
    raw_err_x = original_center_point[0] - center_pos[0]
    raw_err_y = original_center_point[1] - center_pos[1]
    print(f"🎯 舵机控制调试:")
    print(f"   原始误差: ({raw_err_x:.1f}, {raw_err_y:.1f})")
    print(f"   偏移补偿: ({offset_x}, {offset_y})")
    print(f"   补偿后误差: ({err_center[0]:.1f}, {err_center[1]:.1f})")
```

### 4. 用户指导信息

#### 启动时显示配置说明
```python
print("🎯 打靶偏移补偿说明:")
print(f"   X轴偏移: {offset_x}像素 (负值=向左补偿, 正值=向右补偿)")
print(f"   Y轴偏移: {offset_y}像素 (负值=向上补偿, 正值=向下补偿)")
print("   如果打靶偏右 → 减小offset_x值")
print("   如果打靶偏左 → 增大offset_x值")
print("   如果打靶偏下 → 减小offset_y值")
print("   如果打靶偏上 → 增大offset_y值")
```

## 修改的文件位置

### main.py 主要修改点：
1. **第1012-1013行**: 偏移补偿参数定义
2. **第1515行**: 主要误差计算修正
3. **第1767行**: 预测跟踪误差计算修正
4. **第1820行**: 第二个预测跟踪误差计算修正
5. **第1009行**: 舵机自动初始化配置
6. **第1561行**: 自动启用舵机逻辑
7. **第1582-1592行**: 增强的调试信息

## 使用建议

### 1. 初次使用
1. 保持 `servo_auto_init = False`
2. 运行程序，舵机不会自动启用
3. 检测到目标时舵机会自动启用并开始跟踪

### 2. 打靶精度调整
1. 观察实际打靶位置与目标的偏差
2. 根据偏差方向调整 `offset_x` 和 `offset_y`
3. 重新测试直到打靶精度满意

### 3. 调试模式
1. 设置 `DEBUG = True`
2. 观察控制台输出的偏移补偿信息
3. 验证补偿效果是否符合预期

## 预期效果

1. **更精确的打靶**: 通过偏移补偿修正机械偏差
2. **更友好的启动**: 程序启动时舵机不会立即锁定
3. **智能启用**: 检测到目标时自动启用舵机控制
4. **更好的调试**: 详细的偏移补偿调试信息

## 测试验证

已创建 `test_offset_compensation.py` 测试脚本，验证：
- 偏移补偿计算的正确性
- 不同目标位置下的补偿效果
- 舵机自动初始化功能

测试结果显示所有功能工作正常。
