# 水平舵机初始化代码使用指南

## 概述

我为你创建了三个不同层次的水平舵机初始化代码，适用于不同的使用场景：

## 1. 完整功能版本 - `horizontal_servo_init.py`

### 特点
- 完整的舵机控制功能
- 多种测试模式
- 交互式控制
- 详细的错误处理

### 使用方法
```bash
python horizontal_servo_init.py
```

### 功能菜单
- **基础功能测试**: 测试角度控制
- **PWM范围测试**: 测试不同占空比
- **精密控制测试**: 测试精确角度控制
- **交互式测试**: 手动输入命令控制舵机
- **启用/禁用舵机**: 控制舵机状态

### 适用场景
- 首次调试舵机
- 全面测试舵机功能
- 学习舵机控制原理

## 2. 快速初始化版本 - `quick_horizontal_servo_init.py`

### 特点
- 快速初始化
- 简单测试
- 可作为模块导入
- 轻量级设计

### 直接运行
```bash
python quick_horizontal_servo_init.py
```

### 作为模块使用
```python
from quick_horizontal_servo_init import init_horizontal_servo, set_servo_angle

# 初始化舵机
servo_pwm, success = init_horizontal_servo()

if success:
    # 设置角度
    set_servo_angle(servo_pwm, 135)  # 中心位置
    set_servo_angle(servo_pwm, 90)   # 左转
    set_servo_angle(servo_pwm, 180)  # 右转
```

### 适用场景
- 快速验证舵机连接
- 集成到其他项目
- 简单的角度控制需求

## 3. 模块化版本 - `horizontal_servo_module.py`

### 特点
- 面向对象设计
- 完整的类接口
- 易于集成
- 调试信息可控

### 基本使用
```python
from horizontal_servo_module import HorizontalServo

# 创建舵机对象
servo = HorizontalServo(debug=True)

if servo.is_initialized():
    # 启用舵机
    servo.enable()
    
    # 设置角度
    servo.set_angle(135)  # 中心位置
    servo.set_angle(90)   # 左转45度
    servo.set_angle(180)  # 右转45度
    
    # 回中心
    servo.center()
    
    # 禁用舵机
    servo.disable()
```

### 便捷函数
```python
from horizontal_servo_module import create_horizontal_servo

# 快速创建
servo = create_horizontal_servo(debug=True)
if servo:
    servo.test_movement()  # 运行测试
```

### 适用场景
- 集成到main.py
- 需要完整控制接口
- 面向对象编程

## 硬件连接

### 引脚连接
- **信号线**: A18引脚 (PWM6通道)
- **电源线**: 5V电源正极
- **地线**: GND

### 舵机规格
- **型号**: LD-3015MG
- **类型**: 270度位置舵机
- **控制**: PWM信号 (50Hz)
- **角度范围**: 45° - 225° (安全范围)
- **中心位置**: 135°

## PWM参数

### 占空比映射
- **45度**: 2.5% 占空比
- **135度**: 7.5% 占空比 (中心)
- **225度**: 12.5% 占空比

### 计算公式
```python
duty = 2.5 + ((angle - 45) / 180.0) * (12.5 - 2.5)
```

## 集成到main.py

### 方法1: 替换现有舵机类
```python
# 在main.py中导入
from horizontal_servo_module import HorizontalServo

# 替换现有的水平舵机初始化
class ServoController:
    def __init__(self):
        # ... 其他初始化代码 ...
        
        # 使用新的水平舵机模块
        self.horizontal_servo = HorizontalServo(debug=DEBUG)
        
        if not self.horizontal_servo.is_initialized():
            print("❌ 水平舵机初始化失败")
            self.horizontal_servo = None
```

### 方法2: 独立使用
```python
# 在main.py开头添加
from horizontal_servo_module import create_horizontal_servo

# 创建独立的水平舵机对象
horizontal_servo = create_horizontal_servo(debug=DEBUG)

# 在需要控制舵机的地方使用
if horizontal_servo:
    horizontal_servo.set_angle(target_angle)
```

## 常见问题

### 1. 舵机不动
- 检查A18引脚连接
- 确认舵机电源供电
- 验证PWM信号输出

### 2. 角度不准确
- 校准PWM占空比范围
- 检查机械安装
- 调整角度映射参数

### 3. 初始化失败
- 确认MaixCAM Pro支持PWM6
- 检查引脚配置
- 查看错误信息

## 调试技巧

### 启用调试输出
```python
servo = HorizontalServo(debug=True)
```

### 测试PWM输出
```python
# 直接设置PWM占空比
servo.set_duty(7.5)  # 中心位置
```

### 检查状态
```python
print(f"初始化状态: {servo.is_initialized()}")
print(f"启用状态: {servo.is_enabled()}")
print(f"当前角度: {servo.get_current_angle()}")
```

## 推荐使用流程

1. **首次使用**: 运行 `horizontal_servo_init.py` 进行全面测试
2. **快速验证**: 使用 `quick_horizontal_servo_init.py` 快速检查
3. **项目集成**: 使用 `horizontal_servo_module.py` 集成到项目中

## 安全注意事项

- 确保角度范围在45°-225°内，避免机械碰撞
- 程序退出时调用 `cleanup()` 方法
- 不使用时及时禁用PWM，避免舵机过热
- 检查电源供应是否稳定

## 性能优化

- 避免频繁启用/禁用PWM
- 合理设置角度变化间隔
- 使用适当的调试级别
- 及时清理资源
