#!/usr/bin/env python3
"""
测试偏移补偿功能
验证main.py中的偏移补偿是否正确工作

@author: AI Assistant
@date: 2025.8.2
"""

def test_offset_compensation():
    """测试偏移补偿计算"""
    print("🧪 测试偏移补偿功能")
    print("=" * 40)
    
    # 模拟参数
    center_pos = [224, 224]  # 画面中心 (448x448的一半)
    offset_x = -12
    offset_y = -12
    
    # 测试用例
    test_cases = [
        {
            "name": "目标在画面中心",
            "target_pos": [224, 224],
            "expected_behavior": "应该有轻微的负偏移"
        },
        {
            "name": "目标在右上角",
            "target_pos": [300, 150],
            "expected_behavior": "X正误差增大，Y负误差增大"
        },
        {
            "name": "目标在左下角", 
            "target_pos": [150, 300],
            "expected_behavior": "X负误差增大，Y正误差增大"
        }
    ]
    
    for case in test_cases:
        print(f"\n📍 {case['name']}:")
        target_pos = case['target_pos']
        
        # 原始误差计算（不含偏移补偿）
        raw_err_x = target_pos[0] - center_pos[0]
        raw_err_y = target_pos[1] - center_pos[1]
        
        # 偏移补偿后的误差计算
        compensated_err_x = target_pos[0] - (center_pos[0] + offset_x)
        compensated_err_y = target_pos[1] - (center_pos[1] + offset_y)
        
        print(f"   目标位置: ({target_pos[0]}, {target_pos[1]})")
        print(f"   画面中心: ({center_pos[0]}, {center_pos[1]})")
        print(f"   原始误差: ({raw_err_x:+.0f}, {raw_err_y:+.0f})")
        print(f"   偏移补偿: ({offset_x:+.0f}, {offset_y:+.0f})")
        print(f"   补偿后误差: ({compensated_err_x:+.0f}, {compensated_err_y:+.0f})")
        print(f"   预期行为: {case['expected_behavior']}")
        
        # 分析补偿效果
        x_change = compensated_err_x - raw_err_x
        y_change = compensated_err_y - raw_err_y
        print(f"   补偿效果: X轴{x_change:+.0f}, Y轴{y_change:+.0f}")
    
    print("\n" + "=" * 40)
    print("📋 偏移补偿说明:")
    print("• offset_x < 0: 向左补偿，增大X轴误差")
    print("• offset_y < 0: 向上补偿，增大Y轴误差")
    print("• 这样可以让舵机提前一点到达目标位置")
    print("• 用于补偿激光器与摄像头的机械偏差")
    
    print("\n🔧 调整建议:")
    print("• 如果打靶偏右 → 减小offset_x (更负)")
    print("• 如果打靶偏左 → 增大offset_x (更正)")
    print("• 如果打靶偏下 → 减小offset_y (更负)")
    print("• 如果打靶偏上 → 增大offset_y (更正)")

def test_servo_auto_init():
    """测试舵机自动初始化功能"""
    print("\n🤖 测试舵机自动初始化功能")
    print("=" * 40)
    
    # 模拟配置
    configs = [
        {"servo_auto_init": True, "description": "自动初始化模式"},
        {"servo_auto_init": False, "description": "手动初始化模式"}
    ]
    
    for config in configs:
        print(f"\n📋 {config['description']}:")
        servo_auto_init = config["servo_auto_init"]
        
        if servo_auto_init:
            print("   ✓ 程序启动时自动启用舵机")
            print("   ✓ 立即开始目标跟踪")
            print("   ⚠️ 舵机会立即锁定到中心位置")
        else:
            print("   ⏸️ 程序启动时舵机保持禁用状态")
            print("   ✓ 可以手动搬动舵机")
            print("   ✓ 检测到目标时自动启用")
            print("   ✓ 或使用测试选项7手动启用")
    
    print("\n💡 推荐设置:")
    print("• servo_auto_init = False (当前设置)")
    print("• 这样可以在程序启动时保持舵机自由状态")
    print("• 检测到目标时会自动启用舵机控制")

if __name__ == "__main__":
    test_offset_compensation()
    test_servo_auto_init()
    print("\n✅ 测试完成！")
