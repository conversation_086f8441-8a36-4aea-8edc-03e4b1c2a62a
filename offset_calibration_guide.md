# 偏移补偿校准工具使用指南

## 概述

我为你创建了多个偏移补偿校准工具，参考你选中的"强制设置不同速度，观察舵机是否转动"的风格，让你可以在屏幕上调节offset_x和offset_y。

## 🎯 工具列表

### 1. 集成到main.py的校准功能

#### 使用方法
1. 运行main.py
2. 在测试选项中选择 `c` - 偏移补偿校准
3. 观察控制台输出的偏移效果

#### 特点
- 直接集成在main.py中
- 参考"强制设置不同速度"的风格
- 自动演示不同偏移值的效果
- 显示误差改善程度

### 2. 独立的可视化校准工具

#### `offset_calibration_tool.py` - 完整版
```bash
python offset_calibration_tool.py
```

**功能特点：**
- 实时摄像头画面
- 可视化误差线显示
- 自动演示模式
- 交互式调节（如果支持输入）

#### `quick_offset_tuner.py` - 快速版
```bash
python quick_offset_tuner.py
```

**功能特点：**
- 简化的界面
- 快速测试序列
- 自动生成代码输出
- 参考main.py风格

## 🔧 使用流程

### 方法1: 使用main.py集成功能

1. **修改测试选项**
   ```python
   test_choice = "c"  # 改为"c"启用偏移校准
   ```

2. **运行程序**
   ```bash
   python main.py
   ```

3. **观察输出**
   程序会自动演示不同偏移值的效果：
   ```
   📍 强制设置偏移: X=+0, Y=+0 (无偏移基准)
      原始误差: (+50, -30), 大小: 58.3
      补偿误差: (+50, -30), 大小: 58.3
      改善程度: +0.0%
   
   📍 强制设置偏移: X=-12, Y=-12 (推荐偏移)
      原始误差: (+50, -30), 大小: 58.3
      补偿误差: (+62, -18), 大小: 64.6
      改善程度: -10.8%
   ```

### 方法2: 使用独立工具

1. **运行快速调节工具**
   ```bash
   python quick_offset_tuner.py
   ```

2. **观察屏幕显示**
   - 蓝色十字：原始画面中心
   - 绿色十字：补偿后中心
   - 红色圆圈：模拟目标位置
   - 误差线：显示补偿效果

3. **获取推荐值**
   程序会输出推荐的偏移值和代码

## 📊 偏移值含义

### offset_x（X轴偏移）
- **负值**：向左补偿，让舵机提前停止
- **正值**：向右补偿，让舵机延后停止
- **调整原则**：
  - 打靶偏右 → 减小offset_x（更负）
  - 打靶偏左 → 增大offset_x（更正）

### offset_y（Y轴偏移）
- **负值**：向上补偿，让舵机提前停止
- **正值**：向下补偿，让舵机延后停止
- **调整原则**：
  - 打靶偏下 → 减小offset_y（更负）
  - 打靶偏上 → 增大offset_y（更正）

## 🎯 校准步骤

### 1. 初始测试
```python
# 在main.py中设置
test_choice = "c"  # 启用偏移校准
```

### 2. 观察效果
运行程序，观察不同偏移值的误差变化：
- 无偏移基准
- 小幅偏移 (-5, -5)
- 中等偏移 (-10, -10)
- 推荐偏移 (-12, -12)

### 3. 实际测试
使用推荐值进行实际打靶测试，根据结果微调：

```python
# 如果打靶偏右，减小offset_x
offset_x = -15  # 从-12改为-15

# 如果打靶偏下，减小offset_y  
offset_y = -15  # 从-12改为-15
```

### 4. 精细调节
每次调节1-2像素，重复测试直到满意：

```python
offset_x = -13  # 微调
offset_y = -11  # 微调
```

## 🔍 调试信息

### 在main.py中启用详细调试
```python
DEBUG = True  # 启用调试模式
```

调试输出示例：
```
🎯 舵机控制调试:
   原始误差: (+50.0, -30.0)
   偏移补偿: (-12, -12)
   补偿后误差: (+62.0, -18.0)
   误差大小: 64.6px, 阈值: 18px
```

## 📋 推荐配置

### 基于111.py的推荐值
```python
offset_x = -12  # X轴偏移补偿（像素）
offset_y = -12  # Y轴偏移补偿（像素）
```

### 常见调整范围
- **X轴**：-20 到 +10 像素
- **Y轴**：-20 到 +10 像素
- **步长**：1-2 像素

## 🚀 快速开始

1. **立即测试**：
   ```python
   # 修改main.py第1152行
   test_choice = "c"  # 改为"c"
   ```

2. **运行程序**：
   ```bash
   python main.py
   ```

3. **观察效果**：
   程序会自动演示各种偏移值的效果

4. **应用结果**：
   根据演示结果调整第1012-1013行的偏移值

## 💡 使用技巧

1. **先用演示模式**了解偏移效果
2. **记录实际打靶结果**进行对比
3. **小步调节**，每次1-2像素
4. **多次测试**确保稳定性
5. **记录最佳值**备用

## ⚠️ 注意事项

1. 偏移值过大可能导致系统不稳定
2. 建议在-20到+20像素范围内调节
3. 每次修改后要重新测试
4. 保存好工作的配置值

这套工具让你可以像调节舵机速度一样，直观地观察和调节偏移补偿效果，大大简化了校准过程。
