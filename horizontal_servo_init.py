#!/usr/bin/env python3
"""
水平舵机初始化和测试工具
专门用于初始化和测试水平舵机（LD-3015MG 270度位置舵机）

功能：
1. 初始化水平舵机PWM
2. 基础功能测试
3. 角度控制测试
4. PWM占空比测试
5. 舵机校准

连接说明：
- 水平舵机连接到 A18口（PWM6通道）
- 舵机型号：LD-3015MG 270度位置舵机
- PWM频率：50Hz
- 控制范围：45-225度（避免机械碰撞）

@author: AI Assistant
@date: 2025.8.2
"""

from maix import pwm, pinmap, time
import sys

class HorizontalServoController:
    def __init__(self):
        """初始化水平舵机控制器"""
        print("🔧 初始化水平舵机控制器")
        print("=" * 50)
        
        # 舵机参数
        self.pwm_channel = 6                    # PWM通道6
        self.gpio_pin = "A18"                   # A18引脚
        self.pwm_freq = 50                      # PWM频率50Hz
        
        # LD-3015MG 270度位置舵机参数
        self.min_duty = 2.5                     # 0度对应的占空比
        self.max_duty = 12.5                    # 270度对应的占空比
        self.center_duty = 7.5                  # 135度中位占空比
        
        # 安全运动范围（避免机械碰撞）
        self.min_angle = 45                     # 最小角度（左极限）
        self.max_angle = 225                    # 最大角度（右极限）
        self.center_angle = 135                 # 中心角度
        self.current_angle = 135                # 当前角度
        
        # PWM对象和状态
        self.horizontal_pwm = None
        self.pwm_enabled = False
        
        # 初始化PWM
        self.init_pwm()
    
    def init_pwm(self):
        """初始化PWM"""
        try:
            print(f"🔌 配置引脚: {self.gpio_pin} -> PWM{self.pwm_channel}")
            pinmap.set_pin_function(self.gpio_pin, f"PWM{self.pwm_channel}")
            
            print(f"⚡ 初始化PWM{self.pwm_channel}: 频率={self.pwm_freq}Hz, 初始禁用")
            self.horizontal_pwm = pwm.PWM(self.pwm_channel, freq=self.pwm_freq, duty=0, enable=False)
            
            print("✅ 水平舵机PWM初始化成功")
            print(f"📋 舵机参数:")
            print(f"   型号: LD-3015MG 270度位置舵机")
            print(f"   控制范围: {self.min_angle}° - {self.max_angle}°")
            print(f"   中心位置: {self.center_angle}°")
            print(f"   PWM范围: {self.min_duty}% - {self.max_duty}%")
            print(f"   当前状态: 禁用（可手动搬动）")
            
            return True
            
        except Exception as e:
            print(f"❌ PWM初始化失败: {e}")
            print("请检查：")
            print("1. MaixCAM Pro是否支持PWM6")
            print("2. A18引脚是否正确连接")
            print("3. 舵机电源是否正常供电")
            self.horizontal_pwm = None
            return False
    
    def angle_to_duty(self, angle):
        """将角度转换为PWM占空比"""
        # 限制角度范围
        angle = max(self.min_angle, min(self.max_angle, angle))
        
        # 线性映射：45度 -> 2.5%, 135度 -> 7.5%, 225度 -> 12.5%
        # 使用180度范围(45-225)映射到270度舵机的部分PWM范围
        duty = self.min_duty + ((angle - self.min_angle) / (self.max_angle - self.min_angle)) * (self.max_duty - self.min_duty)
        return duty
    
    def set_angle(self, angle):
        """设置舵机角度"""
        if self.horizontal_pwm is None:
            print("❌ PWM未初始化")
            return False
        
        # 限制角度范围
        angle = max(self.min_angle, min(self.max_angle, angle))
        
        try:
            # 如果PWM未启用，先启用
            if not self.pwm_enabled:
                self.horizontal_pwm.enable()
                self.pwm_enabled = True
                print("🔒 PWM已启用，舵机锁定")
            
            # 计算并设置占空比
            duty = self.angle_to_duty(angle)
            self.horizontal_pwm.duty(duty)
            self.current_angle = angle
            
            print(f"✅ 角度设置: {angle}°, 占空比: {duty:.2f}%")
            return True
            
        except Exception as e:
            print(f"❌ 设置角度失败: {e}")
            return False
    
    def set_duty(self, duty):
        """直接设置PWM占空比"""
        if self.horizontal_pwm is None:
            print("❌ PWM未初始化")
            return False
        
        try:
            # 如果PWM未启用，先启用
            if not self.pwm_enabled:
                self.horizontal_pwm.enable()
                self.pwm_enabled = True
                print("🔒 PWM已启用，舵机锁定")
            
            self.horizontal_pwm.duty(duty)
            print(f"✅ PWM占空比设置: {duty:.2f}%")
            return True
            
        except Exception as e:
            print(f"❌ 设置PWM失败: {e}")
            return False
    
    def enable_servo(self):
        """启用舵机（锁定到中心位置）"""
        print("🔒 启用舵机，锁定到中心位置...")
        return self.set_angle(self.center_angle)
    
    def disable_servo(self):
        """禁用舵机（可手动搬动）"""
        if self.horizontal_pwm is None:
            print("❌ PWM未初始化")
            return False
        
        try:
            self.horizontal_pwm.disable()
            self.pwm_enabled = False
            print("🔓 舵机已禁用，可以手动搬动")
            return True
            
        except Exception as e:
            print(f"❌ 禁用舵机失败: {e}")
            return False
    
    def center_servo(self):
        """舵机回中心位置"""
        print("🎯 舵机回中心位置...")
        return self.set_angle(self.center_angle)
    
    def test_basic_function(self):
        """基础功能测试"""
        print("\n🧪 基础功能测试")
        print("=" * 30)
        
        if self.horizontal_pwm is None:
            print("❌ PWM未初始化，无法测试")
            return False
        
        # 测试序列
        test_sequence = [
            (self.center_angle, "中心位置", 2),
            (90, "向左45度", 2),
            (self.center_angle, "回中心", 1),
            (180, "向右45度", 2),
            (self.center_angle, "回中心", 1),
            (60, "向左75度", 2),
            (210, "向右75度", 2),
            (self.center_angle, "最终回中心", 2)
        ]
        
        print("开始角度控制测试...")
        for angle, description, duration in test_sequence:
            print(f"\n📍 {description}: {angle}°")
            if self.set_angle(angle):
                time.sleep(duration)
            else:
                print("❌ 测试失败")
                return False
        
        print("\n✅ 基础功能测试完成")
        return True
    
    def test_pwm_range(self):
        """PWM占空比范围测试"""
        print("\n🧪 PWM占空比范围测试")
        print("=" * 30)
        
        if self.horizontal_pwm is None:
            print("❌ PWM未初始化，无法测试")
            return False
        
        # 测试不同的PWM占空比
        test_duties = [2.5, 4.0, 5.5, 7.0, 7.5, 8.0, 9.5, 11.0, 12.5]
        
        print("测试PWM占空比范围...")
        for duty in test_duties:
            print(f"\n📊 测试PWM: {duty}%")
            if self.set_duty(duty):
                time.sleep(2)
            else:
                print("❌ 测试失败")
                return False
        
        # 回到中心位置
        print(f"\n🎯 回到中心位置: {self.center_duty}%")
        self.set_duty(self.center_duty)
        time.sleep(1)
        
        print("\n✅ PWM范围测试完成")
        return True
    
    def test_precision_control(self):
        """精密控制测试"""
        print("\n🧪 精密控制测试")
        print("=" * 30)
        
        if self.horizontal_pwm is None:
            print("❌ PWM未初始化，无法测试")
            return False
        
        # 精密角度测试
        center = self.center_angle
        test_angles = [
            center,
            center + 5,   # 右5度
            center - 5,   # 左5度
            center + 10,  # 右10度
            center - 10,  # 左10度
            center + 20,  # 右20度
            center - 20,  # 左20度
            center        # 回中心
        ]
        
        print("测试精密角度控制...")
        for angle in test_angles:
            offset = angle - center
            direction = "右" if offset > 0 else "左" if offset < 0 else "中心"
            print(f"\n📐 {direction}{abs(offset)}度: {angle}°")
            if self.set_angle(angle):
                time.sleep(1.5)
            else:
                print("❌ 测试失败")
                return False
        
        print("\n✅ 精密控制测试完成")
        return True
    
    def interactive_test(self):
        """交互式测试"""
        print("\n🎮 交互式测试模式")
        print("=" * 30)
        print("输入命令控制舵机:")
        print("  角度值 (45-225): 设置指定角度")
        print("  'c' 或 'center': 回中心位置")
        print("  'e' 或 'enable': 启用舵机")
        print("  'd' 或 'disable': 禁用舵机")
        print("  'q' 或 'quit': 退出测试")
        print("  'h' 或 'help': 显示帮助")
        
        while True:
            try:
                cmd = input("\n🎯 请输入命令: ").strip().lower()
                
                if cmd in ['q', 'quit']:
                    break
                elif cmd in ['h', 'help']:
                    print("命令列表:")
                    print("  45-225: 设置角度")
                    print("  c/center: 回中心")
                    print("  e/enable: 启用舵机")
                    print("  d/disable: 禁用舵机")
                    print("  q/quit: 退出")
                elif cmd in ['c', 'center']:
                    self.center_servo()
                elif cmd in ['e', 'enable']:
                    self.enable_servo()
                elif cmd in ['d', 'disable']:
                    self.disable_servo()
                else:
                    # 尝试解析为角度值
                    try:
                        angle = float(cmd)
                        if self.min_angle <= angle <= self.max_angle:
                            self.set_angle(angle)
                        else:
                            print(f"❌ 角度超出范围 ({self.min_angle}-{self.max_angle})")
                    except ValueError:
                        print("❌ 无效命令，输入 'h' 查看帮助")
                        
            except KeyboardInterrupt:
                print("\n\n⚠️ 用户中断")
                break
            except Exception as e:
                print(f"❌ 命令执行错误: {e}")
        
        print("🔚 交互式测试结束")
    
    def cleanup(self):
        """清理资源"""
        print("\n🧹 清理资源...")
        try:
            if self.horizontal_pwm and self.pwm_enabled:
                # 回到中心位置
                self.set_angle(self.center_angle)
                time.sleep(0.5)
                # 禁用PWM
                self.disable_servo()
            print("✅ 资源清理完成")
        except Exception as e:
            print(f"⚠️ 清理资源时出错: {e}")

def main():
    """主函数"""
    print("🚀 水平舵机初始化和测试工具")
    print("=" * 50)
    
    # 创建舵机控制器
    servo = HorizontalServoController()
    
    if servo.horizontal_pwm is None:
        print("❌ 舵机初始化失败，程序退出")
        return
    
    try:
        # 显示测试选项
        print("\n📋 测试选项:")
        print("  1 - 基础功能测试")
        print("  2 - PWM范围测试")
        print("  3 - 精密控制测试")
        print("  4 - 交互式测试")
        print("  5 - 启用舵机")
        print("  6 - 禁用舵机")
        print("  0 - 退出程序")
        
        while True:
            try:
                choice = input("\n🎯 请选择测试项目 (0-6): ").strip()
                
                if choice == "0":
                    break
                elif choice == "1":
                    servo.test_basic_function()
                elif choice == "2":
                    servo.test_pwm_range()
                elif choice == "3":
                    servo.test_precision_control()
                elif choice == "4":
                    servo.interactive_test()
                elif choice == "5":
                    servo.enable_servo()
                elif choice == "6":
                    servo.disable_servo()
                else:
                    print("❌ 无效选择，请输入 0-6")
                    
            except KeyboardInterrupt:
                print("\n\n⚠️ 用户中断")
                break
            except Exception as e:
                print(f"❌ 执行错误: {e}")
    
    finally:
        # 清理资源
        servo.cleanup()
        print("\n👋 程序结束")

if __name__ == "__main__":
    main()
