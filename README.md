2025 电赛 E 题 MaixPy 实现
======

## 题目

这是对 《2025 电赛 E 题 简易自行瞄准装置》的一个实现，官方题目在[这里](https://res.nuedc-training.com.cn/topic/2025/topic_from_30.html)。

## 效果

演示视频看[Bilibili](https://www.bilibili.com/video/BV18m8CzNEqT/)

* 使用了 AI 模型，可以避免很多干扰识别到矩形框。
* 使用MaixCAM 默认 GC4653 摄像头，最远距离可 4 米还能识别到。
* 多种算法选择。
* 帧率 > 25fps，根据你配置的分辨率和算法决定，里面有各种选择配置，而且还有优化空间。


## 硬件

[MaixCAM/MaixCAM-Pro](https://wiki.sipeed.com/maixcam-pro)

## 软件

基于 [MaixPy](https://github.com/sipeed/maixpy)，以及用到了一点点 OpenCV。

注意，本代码在系统镜像 v4.11.8 上测试过。之前的版本可以在`main.py`配置中打开自动白平衡即可运行，或者直接升级系统也可以。


## 使用

* 先下载 [检测轮廓模型](https://maixhub.com/model/zoo/1159)。
* 传送到开发板，解压后在有`mud`文件的目录路径下，终端用`scp ./* root@板子ip:~/models` 发送到开发板。
* 用 MaixVision 打开本目录（工程），点击运行工程即可。
* 代码开头有很多配置项，可以根据你的情况修改配置。
* 输出有一个误差值，就是圆心相对于屏幕中心的误差像素，用这个值，最简单的方法就是将摄像头和激光笔固定到一个点，就不用识别激光笔了，
  直接用这个误差值PID控制云台，让云台始终指向中心（也就等同激光指向了圆心）即可。

## 注意

* 代码和文档可能会持续更新，还是在这里看。
* 代码只是一个参考，不代表是最好效果，多思考，也许你有更好的改进（欢迎 [Issues](https://github.com/sipeed/maixpy/issues) 提出来）。

## 思路

有几种方法：
* 方法一：
  * 用模型检测到黑框，可以排除很多干扰。
  * 用传统算法找到内框四角坐标。
  * 仿射变换成标准A4纸的比例（代码中的标准图）。
  * 根据四个角点直接算出中心点和圆圈间的距离。前题是你的A4卡纸必须画得标准，中心点在4个角交叉中心！！！
  * 然后再将坐标变换回原始图像坐标即可。
* 方法二：
  * 前面步骤相同。
  * 得到标准图后不直接从角算出，而是从标准图再找圆圈和中心，再识别一次。
    好处就是圆心歪了也能识别，但是问题就是分辨率太低和太远或者灯光不好时可能看不清，代码中可以打开这个选项，注意调节分辨率。
  * 然后再将坐标变换回原始图像坐标即可。






