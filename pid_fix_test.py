#!/usr/bin/env python3
"""
PID修复验证测试
验证增大比例系数后PID控制是否能正常工作

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time
import math

def test_pid_fix():
    """测试PID修复效果"""
    print("🔧 PID修复验证测试")
    print("="*25)
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        # 修复后的参数
        global_speed_multiplier = 1.5
        horizontal_stop_duty = 7.5
        horizontal_cw_duty = 9.0
        horizontal_ccw_duty = 6.0
        
        print(f"参数: 全局倍数={global_speed_multiplier}, PWM范围={horizontal_ccw_duty}%~{horizontal_cw_duty}%")
        
        def set_speed_and_test(speed, description):
            """设置速度并测试"""
            # 应用缩放
            final_speed = speed * global_speed_multiplier
            
            # PWM计算
            if final_speed > 0:
                duty_change = (abs(final_speed) / 100.0) * (horizontal_cw_duty - horizontal_stop_duty)
                duty = horizontal_stop_duty + duty_change
            elif final_speed < 0:
                duty_change = (abs(final_speed) / 100.0) * (horizontal_stop_duty - horizontal_ccw_duty)
                duty = horizontal_stop_duty - duty_change
            else:
                duty = horizontal_stop_duty
            
            pwm_change = duty - horizontal_stop_duty
            
            print(f"\n{description}:")
            print(f"  原始速度: {speed:.1f}% → 最终速度: {final_speed:.1f}%")
            print(f"  PWM占空比: {duty:.3f}% (变化: {pwm_change:+.3f}%)")
            
            # 设置PWM并观察
            servo_pwm.duty(duty)
            time.sleep(3)
            
            # 判断是否足够驱动舵机
            if abs(pwm_change) >= 0.2:
                print(f"  ✅ PWM变化足够大，应该能驱动舵机")
            else:
                print(f"  ❌ PWM变化太小，可能无法驱动舵机")
            
            return abs(pwm_change) >= 0.2
        
        # 测试修复后的PID控制速度
        print("\n🔍 测试修复后的PID控制速度:")
        
        test_cases = [
            # (误差, 预期速度, 描述)
            (10, -10 * 1.2, "精细区域 (10px误差)"),  # 新的比例系数1.2
            (20, -20 * 0.8, "减速区域 (20px误差)"),  # 新的比例系数0.8
            (30, -30 * 1.0, "正常区域 (30px误差)"),  # 新的比例系数1.0
            (5, -5 * 1.2, "精细区域 (5px误差)"),    # 新的比例系数1.2
        ]
        
        working_count = 0
        total_count = len(test_cases)
        
        for err_x, expected_speed, description in test_cases:
            # 模拟PID控制逻辑
            horizontal_error = -err_x  # 与main.py一致
            
            # 根据误差大小确定控制区域和速度
            if abs(horizontal_error) <= 3:
                horizontal_speed = 0
                zone = "逼近区域"
            elif abs(horizontal_error) <= 12:
                horizontal_speed = horizontal_error * 1.2  # 新的比例系数
                horizontal_speed = max(-6, min(6, horizontal_speed))
                zone = "精细区域"
            elif abs(horizontal_error) <= 25:
                horizontal_speed = horizontal_error * 0.8  # 新的比例系数
                horizontal_speed = max(-10, min(10, horizontal_speed))
                zone = "减速区域"
            elif abs(horizontal_error) <= 50:
                horizontal_speed = horizontal_error * 1.0  # 新的比例系数
                horizontal_speed = max(-15, min(15, horizontal_speed))
                zone = "正常区域"
            else:
                horizontal_speed = horizontal_error * 1.2  # 简化的PID
                horizontal_speed = max(-20, min(20, horizontal_speed))
                zone = "PID区域"
            
            print(f"\n{description} ({zone}):")
            print(f"  误差: {err_x}px → horizontal_error: {horizontal_error}")
            print(f"  计算速度: {horizontal_speed:.1f}%")
            
            # 测试这个速度是否能驱动舵机
            if set_speed_and_test(horizontal_speed, f"  测试速度 {horizontal_speed:.1f}%"):
                working_count += 1
            
            # 停止舵机
            servo_pwm.duty(7.5)
            time.sleep(1)
        
        print(f"\n" + "="*25)
        print(f"🏁 测试完成！")
        print(f"✅ 能驱动舵机的测试: {working_count}/{total_count}")
        print(f"📊 成功率: {working_count/total_count*100:.0f}%")
        
        if working_count == total_count:
            print("🎉 所有PID控制速度都能驱动舵机！")
            print("现在可以正常使用main.py了")
        elif working_count > 0:
            print("⚠️ 部分PID控制速度能驱动舵机")
            print("可能需要进一步调整比例系数")
        else:
            print("❌ 所有PID控制速度都无法驱动舵机")
            print("需要进一步增大比例系数或全局倍数")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def compare_before_after():
    """对比修复前后的差异"""
    print("\n📊 修复前后对比")
    print("="*20)
    
    test_errors = [5, 10, 20, 30]
    
    print("误差(px) | 修复前速度 | 修复后速度 | PWM变化(修复前) | PWM变化(修复后)")
    print("-" * 70)
    
    for err in test_errors:
        # 修复前的比例系数
        if err <= 12:
            old_speed = err * 0.3
            zone = "精细"
        elif err <= 25:
            old_speed = err * 0.4
            zone = "减速"
        else:
            old_speed = err * 0.5
            zone = "正常"
        
        # 修复后的比例系数
        if err <= 12:
            new_speed = err * 1.2
        elif err <= 25:
            new_speed = err * 0.8
        else:
            new_speed = err * 1.0
        
        # 计算PWM变化 (使用1.5倍数和1.5%范围)
        old_final = old_speed * 1.5
        new_final = new_speed * 1.5
        
        old_pwm_change = (old_final / 100.0) * 1.5  # 1.5%范围
        new_pwm_change = (new_final / 100.0) * 1.5  # 1.5%范围
        
        print(f"{err:7d} | {old_speed:8.1f}% | {new_speed:8.1f}% | {old_pwm_change:11.3f}% | {new_pwm_change:11.3f}%")

def main():
    """主函数"""
    print("🔧 PID控制修复验证")
    print("="*25)
    
    try:
        compare_before_after()
        test_pid_fix()
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
