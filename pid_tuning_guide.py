#!/usr/bin/env python3
"""
PID参数调节指南和测试工具
专门用于调节VERTICAL_PID_KP等PID参数

@author: AI Assistant
@date: 2025.8.2
"""

import time
import math

class PIDTuningGuide:
    def __init__(self):
        """初始化PID调节指南"""
        print("🎛️ PID参数调节指南")
        print("=" * 40)
        
        # 当前PID参数（从main.py读取）
        self.current_vertical_kp = 0.2
        self.current_vertical_ki = 0.02
        self.current_vertical_kd = 0.04
        
        self.current_horizontal_kp = 0.06
        self.current_horizontal_ki = 0.001
        self.current_horizontal_kd = 0.015
        
        # PID参数范围和建议
        self.pid_ranges = {
            'VERTICAL_PID_KP': {
                'current': self.current_vertical_kp,
                'min_safe': 0.05,
                'max_safe': 0.5,
                'max_extreme': 1.0,
                'recommended_range': (0.1, 0.3),
                'step_size': 0.02,
                'description': '垂直舵机比例系数'
            },
            'VERTICAL_PID_KI': {
                'current': self.current_vertical_ki,
                'min_safe': 0.001,
                'max_safe': 0.05,
                'max_extreme': 0.1,
                'recommended_range': (0.01, 0.03),
                'step_size': 0.002,
                'description': '垂直舵机积分系数'
            },
            'VERTICAL_PID_KD': {
                'current': self.current_vertical_kd,
                'min_safe': 0.01,
                'max_safe': 0.1,
                'max_extreme': 0.2,
                'recommended_range': (0.02, 0.06),
                'step_size': 0.005,
                'description': '垂直舵机微分系数'
            },
            'HORIZONTAL_PID_KP': {
                'current': self.current_horizontal_kp,
                'min_safe': 0.03,
                'max_safe': 0.2,
                'max_extreme': 0.5,
                'recommended_range': (0.05, 0.12),
                'step_size': 0.01,
                'description': '水平舵机比例系数'
            }
        }
    
    def analyze_current_parameters(self):
        """分析当前PID参数"""
        print("\n📊 当前PID参数分析")
        print("-" * 30)
        
        for param_name, param_info in self.pid_ranges.items():
            current = param_info['current']
            min_safe = param_info['min_safe']
            max_safe = param_info['max_safe']
            recommended = param_info['recommended_range']
            
            print(f"\n🎛️ {param_name}: {current}")
            print(f"   描述: {param_info['description']}")
            print(f"   安全范围: {min_safe} - {max_safe}")
            print(f"   推荐范围: {recommended[0]} - {recommended[1]}")
            
            # 分析当前值
            if current < min_safe:
                status = "⚠️ 过低，响应可能太慢"
            elif current > max_safe:
                status = "⚠️ 过高，可能震荡"
            elif recommended[0] <= current <= recommended[1]:
                status = "✅ 在推荐范围内"
            elif current < recommended[0]:
                status = "📈 可以适当增大"
            else:
                status = "📉 可以适当减小"
            
            print(f"   状态: {status}")
    
    def suggest_vertical_kp_increase(self):
        """建议VERTICAL_PID_KP的增大方案"""
        print(f"\n🚀 VERTICAL_PID_KP 增大建议")
        print("-" * 35)
        
        current_kp = self.current_vertical_kp
        param_info = self.pid_ranges['VERTICAL_PID_KP']
        
        print(f"当前值: {current_kp}")
        print(f"最高安全值: {param_info['max_safe']}")
        print(f"极限值: {param_info['max_extreme']} (不推荐)")
        
        # 建议的增大步骤
        suggested_values = []
        step = param_info['step_size']
        
        # 从当前值开始，逐步增大
        test_value = current_kp + step
        while test_value <= param_info['max_safe']:
            suggested_values.append(test_value)
            test_value += step
        
        print(f"\n📈 建议的测试序列:")
        for i, value in enumerate(suggested_values, 1):
            risk_level = self.assess_risk_level(value, param_info)
            print(f"   {i}. {value:.3f} - {risk_level}")
        
        # 极限值警告
        if param_info['max_safe'] < param_info['max_extreme']:
            print(f"\n⚠️ 极限测试值 (谨慎使用):")
            extreme_values = [0.6, 0.7, 0.8, 0.9, 1.0]
            for value in extreme_values:
                if value > param_info['max_safe']:
                    print(f"   {value:.1f} - ⚠️ 高风险，可能震荡")
        
        return suggested_values
    
    def assess_risk_level(self, value, param_info):
        """评估参数值的风险等级"""
        recommended = param_info['recommended_range']
        max_safe = param_info['max_safe']
        
        if value <= recommended[1]:
            return "✅ 安全"
        elif value <= max_safe * 0.8:
            return "🟡 中等风险"
        elif value <= max_safe:
            return "🟠 较高风险"
        else:
            return "🔴 高风险"
    
    def generate_test_configurations(self):
        """生成测试配置"""
        print(f"\n🧪 PID测试配置生成")
        print("-" * 25)
        
        # 针对VERTICAL_PID_KP的测试配置
        kp_values = [0.25, 0.3, 0.35, 0.4, 0.45, 0.5]
        
        print(f"📋 VERTICAL_PID_KP 测试配置:")
        for i, kp in enumerate(kp_values, 1):
            # 根据KP调整其他参数
            ki = self.current_vertical_ki * (kp / self.current_vertical_kp) * 0.8  # 稍微降低KI
            kd = self.current_vertical_kd * (kp / self.current_vertical_kp) * 1.2  # 稍微增加KD
            
            # 限制范围
            ki = min(ki, 0.05)
            kd = min(kd, 0.1)
            
            risk = self.assess_risk_level(kp, self.pid_ranges['VERTICAL_PID_KP'])
            
            print(f"\n配置 {i}: {risk}")
            print(f"VERTICAL_PID_KP = {kp:.3f}")
            print(f"VERTICAL_PID_KI = {ki:.3f}")
            print(f"VERTICAL_PID_KD = {kd:.3f}")
            
            # 预期效果
            if kp <= 0.3:
                effect = "响应加快，稳定性好"
            elif kp <= 0.4:
                effect = "响应很快，可能轻微震荡"
            else:
                effect = "响应极快，震荡风险高"
            
            print(f"预期效果: {effect}")
    
    def create_test_code(self):
        """生成测试代码"""
        print(f"\n💻 测试代码生成")
        print("-" * 20)
        
        print(f"在main.py中修改以下参数进行测试:")
        print(f"")
        print(f"# 原始值 (当前)")
        print(f"VERTICAL_PID_KP = {self.current_vertical_kp}  # 当前值")
        print(f"")
        print(f"# 测试值 (逐步尝试)")
        
        test_values = [0.25, 0.3, 0.35, 0.4, 0.45, 0.5]
        for value in test_values:
            risk = self.assess_risk_level(value, self.pid_ranges['VERTICAL_PID_KP'])
            print(f"VERTICAL_PID_KP = {value:.3f}  # {risk}")
        
        print(f"\n🔧 修改位置: main.py 第40行")
        print(f"修改后重新运行程序测试效果")
    
    def performance_analysis(self):
        """性能分析和建议"""
        print(f"\n📈 性能分析和调节建议")
        print("-" * 30)
        
        print(f"🎯 VERTICAL_PID_KP 调节指南:")
        print(f"")
        print(f"当前值: {self.current_vertical_kp}")
        print(f"")
        print(f"📊 效果预测:")
        
        effects = [
            (0.25, "响应速度提升25%，稳定性优秀"),
            (0.3, "响应速度提升50%，稳定性良好"),
            (0.35, "响应速度提升75%，稳定性一般"),
            (0.4, "响应速度提升100%，可能轻微震荡"),
            (0.45, "响应速度提升125%，震荡风险中等"),
            (0.5, "响应速度提升150%，震荡风险较高")
        ]
        
        for kp, effect in effects:
            risk = self.assess_risk_level(kp, self.pid_ranges['VERTICAL_PID_KP'])
            print(f"   KP={kp:.2f}: {effect} ({risk})")
        
        print(f"\n💡 调节建议:")
        print(f"1. 从 0.25 开始测试，观察响应速度")
        print(f"2. 如果响应满意，保持该值")
        print(f"3. 如果需要更快响应，逐步增加到 0.3")
        print(f"4. 超过 0.35 需要仔细观察是否震荡")
        print(f"5. 最高建议值: 0.5 (需要配合调整KI和KD)")
        
        print(f"\n⚠️ 注意事项:")
        print(f"- 每次只调整一个参数")
        print(f"- 增大KP时，适当减小KI")
        print(f"- 增大KP时，适当增大KD")
        print(f"- 观察是否出现震荡或超调")
        print(f"- 测试不同目标距离的响应")

def main():
    """主函数"""
    print("🎛️ PID参数调节指南")
    print("专门分析VERTICAL_PID_KP的调节范围")
    print("=" * 50)
    
    guide = PIDTuningGuide()
    
    # 分析当前参数
    guide.analyze_current_parameters()
    
    # VERTICAL_PID_KP增大建议
    guide.suggest_vertical_kp_increase()
    
    # 生成测试配置
    guide.generate_test_configurations()
    
    # 生成测试代码
    guide.create_test_code()
    
    # 性能分析
    guide.performance_analysis()
    
    print(f"\n🎯 总结:")
    print(f"VERTICAL_PID_KP 可以从当前的 0.2 增大到:")
    print(f"- 安全范围: 0.25 - 0.5")
    print(f"- 推荐最大值: 0.35 (平衡响应速度和稳定性)")
    print(f"- 极限最大值: 0.5 (需要谨慎测试)")
    print(f"- 理论最大值: 1.0 (不推荐，震荡风险极高)")

if __name__ == "__main__":
    main()
