#!/usr/bin/env python3
"""
无输入水平舵机初始化
专门解决 "EOF when reading a line" 错误
适用于MaixCAM Pro环境，无需任何用户输入

@author: AI Assistant
@date: 2025.8.2
"""

from maix import pwm, pinmap, time
import sys

class NoInputServoInit:
    """无输入舵机初始化类"""
    
    def __init__(self):
        self.pwm = None
        self.enabled = False
        
    def init_and_test(self):
        """初始化并测试舵机"""
        print("🚀 无输入水平舵机初始化")
        print("解决EOF错误，自动执行所有步骤")
        print("=" * 40)
        
        # 步骤1: 初始化
        if not self._init_pwm():
            return False
        
        # 步骤2: 基础测试
        if not self._basic_test():
            return False
        
        # 步骤3: 完成
        self._finish()
        return True
    
    def _init_pwm(self):
        """初始化PWM"""
        print("\n📌 步骤1: 初始化PWM")
        print("-" * 20)
        
        try:
            print("🔌 配置A18引脚为PWM6...")
            pinmap.set_pin_function("A18", "PWM6")
            
            print("⚡ 创建PWM对象 (50Hz)...")
            self.pwm = pwm.PWM(6, freq=50, duty=0, enable=False)
            
            print("✅ PWM初始化成功")
            print("📋 配置: A18->PWM6, 50Hz, LD-3015MG舵机")
            return True
            
        except Exception as e:
            print(f"❌ PWM初始化失败: {e}")
            print("可能原因:")
            print("  1. A18引脚连接问题")
            print("  2. PWM6不可用")
            print("  3. 硬件故障")
            return False
    
    def _basic_test(self):
        """基础测试"""
        print("\n🧪 步骤2: 基础功能测试")
        print("-" * 25)
        
        if self.pwm is None:
            print("❌ PWM未初始化")
            return False
        
        try:
            # 测试序列: 中心->左->中心->右->中心
            test_steps = [
                (7.5, 135, "中心位置"),
                (5.0, 90, "左转45度"),
                (7.5, 135, "回中心"),
                (10.0, 180, "右转45度"),
                (7.5, 135, "最终回中心")
            ]
            
            for duty, angle, description in test_steps:
                print(f"📍 {description}: {angle}°, PWM: {duty}%")
                
                # 启用PWM
                if not self.enabled:
                    self.pwm.enable()
                    self.enabled = True
                    print("  🔒 PWM已启用")
                
                # 设置占空比
                self.pwm.duty(duty)
                print(f"  ✅ PWM设置完成")
                
                # 等待舵机运动
                print(f"  ⏰ 等待2秒...")
                time.sleep(2)
            
            print("✅ 基础测试完成")
            return True
            
        except Exception as e:
            print(f"❌ 基础测试失败: {e}")
            return False
    
    def _finish(self):
        """完成并清理"""
        print("\n🏁 步骤3: 完成测试")
        print("-" * 20)
        
        try:
            if self.pwm and self.enabled:
                print("🔓 禁用PWM...")
                self.pwm.disable()
                self.enabled = False
                print("✅ PWM已禁用，舵机可手动搬动")
            
            print("\n🎉 测试成功完成！")
            print("📊 测试结果:")
            print("  ✅ PWM初始化: 成功")
            print("  ✅ 角度控制: 成功")
            print("  ✅ 运动测试: 成功")
            
            print("\n💡 下一步:")
            print("  1. 观察舵机是否正常转动")
            print("  2. 检查运动是否平滑")
            print("  3. 如果正常，可以集成到main.py")
            
            print("\n🔧 集成代码示例:")
            print("```python")
            print("from maix import pwm, pinmap")
            print("pinmap.set_pin_function('A18', 'PWM6')")
            print("servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)")
            print("# 设置角度: servo_pwm.duty(占空比)")
            print("```")
            
        except Exception as e:
            print(f"⚠️ 清理时出错: {e}")

def main():
    """主函数 - 无需任何输入"""
    try:
        # 创建并运行测试
        servo_init = NoInputServoInit()
        success = servo_init.init_and_test()
        
        if success:
            print("\n🌟 水平舵机初始化成功！")
        else:
            print("\n❌ 水平舵机初始化失败！")
            print("请检查硬件连接和配置")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n👋 程序结束")

# 额外的快速验证函数
def quick_verify():
    """快速验证函数 - 最简单的测试"""
    print("\n🚀 快速验证模式")
    print("=" * 20)
    
    try:
        # 一步到位的验证
        pinmap.set_pin_function("A18", "PWM6")
        servo = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        
        print("✅ 舵机已启用到中心位置")
        print("⏰ 保持3秒...")
        time.sleep(3)
        
        servo.disable()
        print("🔓 舵机已禁用")
        print("✅ 快速验证完成")
        
    except Exception as e:
        print(f"❌ 快速验证失败: {e}")

if __name__ == "__main__":
    # 运行主测试
    main()
    
    # 运行快速验证
    quick_verify()
