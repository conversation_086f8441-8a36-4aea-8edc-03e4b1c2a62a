"""
舵机速度配置文件
可以通过修改这个文件来调整舵机的移动速度，无需修改主程序
"""

# ==================== 舵机速度控制参数 ====================

# 全局速度倍数 (0.1-1.0)
# 0.1 = 非常慢, 0.3 = 慢, 0.6 = 中等, 0.8 = 快, 1.0 = 最快
GLOBAL_SPEED_MULTIPLIER = 0.3  # 推荐从0.3开始调试（更慢更稳定）

# 水平舵机基础速度参数
HORIZONTAL_MAX_SPEED = 12      # 最大速度百分比 (5-30) - 降低最大速度
HORIZONTAL_MIN_SPEED = 2       # 最小有效速度百分比 (1-10) - 降低最小速度

# 垂直舵机速度参数
VERTICAL_MAX_STEP = 2.0        # 垂直舵机每次最大角度变化 (0.5-5.0度)

# 垂直舵机PID参数
VERTICAL_PID_KP = 0.08         # 比例系数 (0.05-0.2)
VERTICAL_PID_KI = 0.005        # 积分系数 (0.001-0.02)
VERTICAL_PID_KD = 0.02         # 微分系数 (0.01-0.1)

# 水平舵机PID参数
HORIZONTAL_PID_KP = 0.06       # 比例系数 (0.03-0.15)
HORIZONTAL_PID_KI = 0.001      # 积分系数 (0.001-0.01)
HORIZONTAL_PID_KD = 0.008      # 微分系数 (0.005-0.05)

# 逼近控制阈值
APPROACH_THRESHOLD = 5         # 停止阈值 (3-10像素)
FINE_ZONE = 12                 # 精细控制区 (8-20像素)
SLOW_ZONE = 25                 # 减速区 (15-40像素)
NORMAL_ZONE = 50               # 正常控制区 (30-80像素)

# 速度限制 (在不同区域的最大速度) - 降低所有区域的速度
FINE_ZONE_MAX_SPEED = 4        # 精细区最大速度 (3-10) - 更慢更精确
SLOW_ZONE_MAX_SPEED = 8        # 减速区最大速度 (8-15) - 降低速度
NORMAL_ZONE_MAX_SPEED = 12     # 正常区最大速度 (12-25) - 降低速度
PID_ZONE_MAX_SPEED = 16        # PID区最大速度 (15-30) - 降低速度

# ==================== 预设配置 ====================

# 超慢速配置 - 适合精密调试
ULTRA_SLOW_CONFIG = {
    'global_speed': 0.2,
    'max_speed': 8,
    'vertical_step': 1.0,
    'fine_max': 3,
    'slow_max': 5,
    'normal_max': 8,
    'pid_max': 12
}

# 慢速配置 - 适合一般使用
SLOW_CONFIG = {
    'global_speed': 0.4,
    'max_speed': 15,
    'vertical_step': 2.0,
    'fine_max': 6,
    'slow_max': 10,
    'normal_max': 15,
    'pid_max': 20
}

# 中速配置 - 平衡速度和精度
MEDIUM_CONFIG = {
    'global_speed': 0.6,
    'max_speed': 20,
    'vertical_step': 3.0,
    'fine_max': 8,
    'slow_max': 12,
    'normal_max': 18,
    'pid_max': 25
}

# 快速配置 - 适合快速响应
FAST_CONFIG = {
    'global_speed': 0.8,
    'max_speed': 25,
    'vertical_step': 4.0,
    'fine_max': 10,
    'slow_max': 15,
    'normal_max': 22,
    'pid_max': 30
}

# ==================== 使用说明 ====================
"""
使用方法:
1. 直接修改上面的参数值
2. 或者选择一个预设配置，在主程序中调用 apply_config()

参数说明:
- GLOBAL_SPEED_MULTIPLIER: 影响所有速度的全局倍数，最直接的调速参数
- HORIZONTAL_MAX_SPEED: 水平舵机的最大速度百分比
- PID参数: 影响舵机的响应特性和稳定性
- 各区域阈值: 定义不同控制区域的边界
- 速度限制: 限制各区域的最大速度

调试建议:
1. 先调整 GLOBAL_SPEED_MULTIPLIER，这是最简单的方法
2. 如果需要更精细控制，再调整各区域的速度限制
3. 最后调整PID参数来优化响应特性
"""

def apply_config(config_name="slow"):
    """应用预设配置"""
    configs = {
        'ultra_slow': ULTRA_SLOW_CONFIG,
        'slow': SLOW_CONFIG,
        'medium': MEDIUM_CONFIG,
        'fast': FAST_CONFIG
    }
    
    if config_name in configs:
        config = configs[config_name]
        global GLOBAL_SPEED_MULTIPLIER, HORIZONTAL_MAX_SPEED, VERTICAL_MAX_STEP
        global FINE_ZONE_MAX_SPEED, SLOW_ZONE_MAX_SPEED
        global NORMAL_ZONE_MAX_SPEED, PID_ZONE_MAX_SPEED

        GLOBAL_SPEED_MULTIPLIER = config['global_speed']
        HORIZONTAL_MAX_SPEED = config['max_speed']
        VERTICAL_MAX_STEP = config['vertical_step']
        FINE_ZONE_MAX_SPEED = config['fine_max']
        SLOW_ZONE_MAX_SPEED = config['slow_max']
        NORMAL_ZONE_MAX_SPEED = config['normal_max']
        PID_ZONE_MAX_SPEED = config['pid_max']
        
        print(f"已应用 {config_name} 配置")
        print(f"全局速度: {GLOBAL_SPEED_MULTIPLIER}")
    else:
        print(f"未找到配置: {config_name}")
        print(f"可用配置: {list(configs.keys())}")

def print_current_config():
    """打印当前配置"""
    print("=== 当前舵机速度配置 ===")
    print(f"全局速度倍数: {GLOBAL_SPEED_MULTIPLIER}")
    print(f"水平最大速度: {HORIZONTAL_MAX_SPEED}")
    print(f"垂直最大步长: {VERTICAL_MAX_STEP}°")
    print(f"精细区最大速度: {FINE_ZONE_MAX_SPEED}")
    print(f"减速区最大速度: {SLOW_ZONE_MAX_SPEED}")
    print(f"正常区最大速度: {NORMAL_ZONE_MAX_SPEED}")
    print(f"PID区最大速度: {PID_ZONE_MAX_SPEED}")
    print("========================")

# 自动应用慢速配置（取消注释下面这行来使用预设配置）
# apply_config("slow")  # 取消注释来应用慢速预设配置

print("舵机配置已加载 - 当前为慢速安全配置")
print(f"全局速度倍数: {GLOBAL_SPEED_MULTIPLIER}")
print("如需调整速度，请修改 servo_config.py 中的 GLOBAL_SPEED_MULTIPLIER 参数")
