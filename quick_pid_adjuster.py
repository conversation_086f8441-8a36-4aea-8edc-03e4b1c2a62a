#!/usr/bin/env python3
"""
快速PID参数调节工具
直接修改main.py中的PID参数

@author: AI Assistant
@date: 2025.8.2
"""

import re

class QuickPIDadjuster:
    def __init__(self):
        """初始化PID调节工具"""
        print("⚡ 快速PID参数调节工具")
        print("=" * 30)
        
        self.main_py_path = "main.py"
        
        # 当前参数
        self.current_params = self.read_current_params()
        
    def read_current_params(self):
        """读取当前的PID参数"""
        try:
            with open(self.main_py_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用正则表达式提取PID参数
            params = {}
            
            # 垂直PID参数
            kp_match = re.search(r'VERTICAL_PID_KP\s*=\s*([\d.]+)', content)
            ki_match = re.search(r'VERTICAL_PID_KI\s*=\s*([\d.]+)', content)
            kd_match = re.search(r'VERTICAL_PID_KD\s*=\s*([\d.]+)', content)
            
            if kp_match:
                params['VERTICAL_PID_KP'] = float(kp_match.group(1))
            if ki_match:
                params['VERTICAL_PID_KI'] = float(ki_match.group(1))
            if kd_match:
                params['VERTICAL_PID_KD'] = float(kd_match.group(1))
            
            # 水平PID参数
            h_kp_match = re.search(r'HORIZONTAL_PID_KP\s*=\s*([\d.]+)', content)
            h_ki_match = re.search(r'HORIZONTAL_PID_KI\s*=\s*([\d.]+)', content)
            h_kd_match = re.search(r'HORIZONTAL_PID_KD\s*=\s*([\d.]+)', content)
            
            if h_kp_match:
                params['HORIZONTAL_PID_KP'] = float(h_kp_match.group(1))
            if h_ki_match:
                params['HORIZONTAL_PID_KI'] = float(h_ki_match.group(1))
            if h_kd_match:
                params['HORIZONTAL_PID_KD'] = float(h_kd_match.group(1))
            
            return params
            
        except Exception as e:
            print(f"❌ 读取参数失败: {e}")
            return {}
    
    def display_current_params(self):
        """显示当前参数"""
        print(f"\n📊 当前PID参数:")
        print("-" * 20)
        
        if 'VERTICAL_PID_KP' in self.current_params:
            print(f"🔺 垂直舵机:")
            print(f"   KP = {self.current_params['VERTICAL_PID_KP']}")
            print(f"   KI = {self.current_params.get('VERTICAL_PID_KI', 'N/A')}")
            print(f"   KD = {self.current_params.get('VERTICAL_PID_KD', 'N/A')}")
        
        if 'HORIZONTAL_PID_KP' in self.current_params:
            print(f"🔄 水平舵机:")
            print(f"   KP = {self.current_params['HORIZONTAL_PID_KP']}")
            print(f"   KI = {self.current_params.get('HORIZONTAL_PID_KI', 'N/A')}")
            print(f"   KD = {self.current_params.get('HORIZONTAL_PID_KD', 'N/A')}")
    
    def suggest_vertical_kp_values(self):
        """建议VERTICAL_PID_KP的值"""
        current_kp = self.current_params.get('VERTICAL_PID_KP', 0.2)
        
        print(f"\n🚀 VERTICAL_PID_KP 调节建议")
        print(f"当前值: {current_kp}")
        print("-" * 30)
        
        # 建议的测试值
        suggested_values = [
            (0.25, "✅ 安全提升", "响应速度提升25%"),
            (0.3, "✅ 推荐值", "响应速度提升50%，稳定性好"),
            (0.35, "🟡 积极值", "响应速度提升75%，需观察稳定性"),
            (0.4, "🟠 激进值", "响应速度提升100%，可能轻微震荡"),
            (0.45, "⚠️ 高风险", "响应速度提升125%，震荡风险中等"),
            (0.5, "⚠️ 极限值", "响应速度提升150%，震荡风险较高")
        ]
        
        print(f"📈 建议测试值:")
        for value, risk, effect in suggested_values:
            print(f"   {value:.2f} - {risk} - {effect}")
        
        print(f"\n💡 最高推荐值: 0.5")
        print(f"⚠️ 理论最大值: 1.0 (不推荐，震荡风险极高)")
        
        return [v[0] for v in suggested_values]
    
    def modify_vertical_kp(self, new_value):
        """修改VERTICAL_PID_KP的值"""
        try:
            with open(self.main_py_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换VERTICAL_PID_KP的值
            pattern = r'(VERTICAL_PID_KP\s*=\s*)([\d.]+)'
            replacement = f'\\g<1>{new_value}'
            new_content = re.sub(pattern, replacement, content)
            
            # 检查是否成功替换
            if new_content != content:
                # 备份原文件
                backup_path = f"{self.main_py_path}.backup"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"📁 原文件已备份到: {backup_path}")
                
                # 写入新内容
                with open(self.main_py_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"✅ VERTICAL_PID_KP 已修改为: {new_value}")
                print(f"📍 修改位置: main.py 第40行")
                
                # 更新当前参数
                self.current_params['VERTICAL_PID_KP'] = new_value
                
                return True
            else:
                print(f"❌ 未找到VERTICAL_PID_KP参数")
                return False
                
        except Exception as e:
            print(f"❌ 修改失败: {e}")
            return False
    
    def batch_modify_vertical_params(self, kp, ki=None, kd=None):
        """批量修改垂直舵机PID参数"""
        try:
            with open(self.main_py_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 修改KP
            if kp is not None:
                pattern = r'(VERTICAL_PID_KP\s*=\s*)([\d.]+)'
                content = re.sub(pattern, f'\\g<1>{kp}', content)
            
            # 修改KI
            if ki is not None:
                pattern = r'(VERTICAL_PID_KI\s*=\s*)([\d.]+)'
                content = re.sub(pattern, f'\\g<1>{ki}', content)
            
            # 修改KD
            if kd is not None:
                pattern = r'(VERTICAL_PID_KD\s*=\s*)([\d.]+)'
                content = re.sub(pattern, f'\\g<1>{kd}', content)
            
            if content != original_content:
                # 备份
                backup_path = f"{self.main_py_path}.backup"
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                
                # 写入
                with open(self.main_py_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ 垂直舵机PID参数已批量修改:")
                if kp is not None:
                    print(f"   VERTICAL_PID_KP = {kp}")
                if ki is not None:
                    print(f"   VERTICAL_PID_KI = {ki}")
                if kd is not None:
                    print(f"   VERTICAL_PID_KD = {kd}")
                
                return True
            else:
                print(f"❌ 未找到要修改的参数")
                return False
                
        except Exception as e:
            print(f"❌ 批量修改失败: {e}")
            return False
    
    def create_test_configurations(self):
        """创建测试配置"""
        print(f"\n🧪 创建测试配置")
        print("-" * 20)
        
        current_kp = self.current_params.get('VERTICAL_PID_KP', 0.2)
        current_ki = self.current_params.get('VERTICAL_PID_KI', 0.02)
        current_kd = self.current_params.get('VERTICAL_PID_KD', 0.04)
        
        # 测试配置
        test_configs = [
            {
                'name': '保守提升',
                'kp': 0.25,
                'ki': current_ki * 0.9,
                'kd': current_kd * 1.1,
                'description': '轻微提升响应，保持稳定'
            },
            {
                'name': '平衡配置',
                'kp': 0.3,
                'ki': current_ki * 0.8,
                'kd': current_kd * 1.2,
                'description': '响应与稳定的平衡'
            },
            {
                'name': '快速响应',
                'kp': 0.4,
                'ki': current_ki * 0.7,
                'kd': current_kd * 1.4,
                'description': '快速响应，可能轻微震荡'
            },
            {
                'name': '极限配置',
                'kp': 0.5,
                'ki': current_ki * 0.6,
                'kd': current_kd * 1.5,
                'description': '最快响应，需仔细测试'
            }
        ]
        
        print(f"📋 推荐测试配置:")
        for i, config in enumerate(test_configs, 1):
            print(f"\n{i}. {config['name']}:")
            print(f"   KP = {config['kp']:.3f}")
            print(f"   KI = {config['ki']:.3f}")
            print(f"   KD = {config['kd']:.3f}")
            print(f"   说明: {config['description']}")
        
        return test_configs
    
    def interactive_adjustment(self):
        """交互式调节"""
        print(f"\n🎮 交互式PID调节")
        print("-" * 20)
        
        suggested_values = self.suggest_vertical_kp_values()
        
        print(f"\n选择要设置的VERTICAL_PID_KP值:")
        for i, value in enumerate(suggested_values, 1):
            print(f"   {i}. {value}")
        print(f"   0. 自定义值")
        
        try:
            choice = input("\n请选择 (1-6, 0=自定义): ").strip()
            
            if choice == '0':
                custom_value = float(input("请输入自定义值 (0.05-1.0): "))
                if 0.05 <= custom_value <= 1.0:
                    return self.modify_vertical_kp(custom_value)
                else:
                    print("❌ 值超出范围")
                    return False
            
            elif choice.isdigit() and 1 <= int(choice) <= len(suggested_values):
                selected_value = suggested_values[int(choice) - 1]
                return self.modify_vertical_kp(selected_value)
            
            else:
                print("❌ 无效选择")
                return False
                
        except ValueError:
            print("❌ 输入格式错误")
            return False
        except KeyboardInterrupt:
            print("\n⚠️ 用户取消")
            return False

def main():
    """主函数"""
    print("⚡ 快速PID参数调节工具")
    print("专门用于调节VERTICAL_PID_KP")
    print("=" * 40)
    
    adjuster = QuickPIDadjuster()
    
    # 显示当前参数
    adjuster.display_current_params()
    
    # 显示建议值
    adjuster.suggest_vertical_kp_values()
    
    # 创建测试配置
    adjuster.create_test_configurations()
    
    print(f"\n🎯 快速设置建议:")
    print(f"1. 保守用户: 设置为 0.25 (安全提升)")
    print(f"2. 平衡用户: 设置为 0.3 (推荐值)")
    print(f"3. 激进用户: 设置为 0.4 (快速响应)")
    print(f"4. 极限用户: 设置为 0.5 (最高建议值)")
    
    print(f"\n💻 手动修改方法:")
    print(f"编辑 main.py 第40行:")
    print(f"VERTICAL_PID_KP = 0.3  # 改为你想要的值")
    
    # 可选的交互式调节
    try:
        use_interactive = input(f"\n是否使用交互式调节? (y/n): ").strip().lower()
        if use_interactive == 'y':
            adjuster.interactive_adjustment()
    except KeyboardInterrupt:
        print(f"\n👋 程序结束")

if __name__ == "__main__":
    main()
