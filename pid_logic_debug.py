#!/usr/bin/env python3
"""
PID逻辑深度调试
找出为什么PID控制逻辑下舵机不动的真正原因

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time
import math

def test_direct_vs_pid():
    """对比直接控制和PID控制的差异"""
    print("🔍 直接控制 vs PID控制对比测试")
    print("="*40)
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        # 参数设置
        global_speed_multiplier = 1.5
        horizontal_stop_duty = 7.5
        horizontal_cw_duty = 9.0
        horizontal_ccw_duty = 6.0
        
        def direct_speed_control(speed, description):
            """直接速度控制（已知能工作）"""
            print(f"\n🎯 {description} - 直接控制")
            
            # 应用缩放
            final_speed = speed * global_speed_multiplier
            
            # PWM计算
            if final_speed > 0:
                duty_change = (abs(final_speed) / 100.0) * (horizontal_cw_duty - horizontal_stop_duty)
                duty = horizontal_stop_duty + duty_change
            elif final_speed < 0:
                duty_change = (abs(final_speed) / 100.0) * (horizontal_stop_duty - horizontal_ccw_duty)
                duty = horizontal_stop_duty - duty_change
            else:
                duty = horizontal_stop_duty
            
            pwm_change = duty - horizontal_stop_duty
            
            print(f"  输入速度: {speed:.1f}%")
            print(f"  最终速度: {final_speed:.1f}%")
            print(f"  PWM占空比: {duty:.3f}% (变化: {pwm_change:+.3f}%)")
            
            # 设置PWM
            servo_pwm.duty(duty)
            time.sleep(3)
            
            return duty
        
        def pid_speed_control(err_x, description):
            """PID速度控制（模拟main.py逻辑）"""
            print(f"\n🧮 {description} - PID控制")
            
            horizontal_error = -err_x
            print(f"  误差转换: err_x={err_x} → horizontal_error={horizontal_error}")
            
            # PID控制区域判断（与main.py完全一致）
            approach_threshold = 3
            fine_zone = 12
            slow_zone = 25
            normal_zone = 50
            
            if abs(horizontal_error) <= approach_threshold:
                horizontal_speed = 0
                control_zone = "逼近区域"
            elif abs(horizontal_error) <= fine_zone:
                horizontal_speed = horizontal_error * 1.2
                horizontal_speed = max(-6, min(6, horizontal_speed))
                control_zone = "精细区域"
            elif abs(horizontal_error) <= slow_zone:
                horizontal_speed = horizontal_error * 0.8
                horizontal_speed = max(-10, min(10, horizontal_speed))
                control_zone = "减速区域"
            elif abs(horizontal_error) <= normal_zone:
                horizontal_speed = horizontal_error * 1.0
                horizontal_speed = max(-15, min(15, horizontal_speed))
                control_zone = "正常区域"
            else:
                horizontal_speed = horizontal_error * 1.2
                horizontal_speed = max(-20, min(20, horizontal_speed))
                control_zone = "PID区域"
            
            print(f"  控制区域: {control_zone}")
            print(f"  计算速度: {horizontal_speed:.1f}%")
            
            # 应用与直接控制相同的缩放和PWM计算
            final_speed = horizontal_speed * global_speed_multiplier
            
            # 速度过滤检查
            if abs(final_speed) < 0.1:
                print(f"  ⚠️ 速度被过滤: {final_speed:.3f}% < 0.1%")
                final_speed = 0
            
            # PWM计算
            if final_speed > 0:
                duty_change = (abs(final_speed) / 100.0) * (horizontal_cw_duty - horizontal_stop_duty)
                duty = horizontal_stop_duty + duty_change
            elif final_speed < 0:
                duty_change = (abs(final_speed) / 100.0) * (horizontal_stop_duty - horizontal_ccw_duty)
                duty = horizontal_stop_duty - duty_change
            else:
                duty = horizontal_stop_duty
            
            pwm_change = duty - horizontal_stop_duty
            
            print(f"  最终速度: {final_speed:.1f}%")
            print(f"  PWM占空比: {duty:.3f}% (变化: {pwm_change:+.3f}%)")
            
            # 设置PWM
            servo_pwm.duty(duty)
            time.sleep(3)
            
            return duty
        
        # 对比测试
        test_cases = [
            (10, "10%速度测试"),
            (-10, "-10%速度测试"),
            (15, "15%速度测试"),
            (-15, "-15%速度测试")
        ]
        
        print("\n📊 对比测试开始...")
        
        for speed, description in test_cases:
            print(f"\n{'='*50}")
            print(f"测试: {description}")
            
            # 直接控制测试
            direct_duty = direct_speed_control(speed, description)
            
            # 停止
            servo_pwm.duty(7.5)
            time.sleep(1)
            
            # 找一个能产生相同速度的误差值进行PID测试
            # 反推误差值
            target_speed = speed
            
            # 根据速度大小选择合适的误差
            if abs(target_speed) <= 6:
                # 精细区域: speed = error * 1.2
                test_error = target_speed / 1.2
                expected_zone = "精细区域"
            elif abs(target_speed) <= 10:
                # 减速区域: speed = error * 0.8  
                test_error = target_speed / 0.8
                expected_zone = "减速区域"
            else:
                # 正常区域: speed = error * 1.0
                test_error = target_speed / 1.0
                expected_zone = "正常区域"
            
            print(f"  预期PID误差: {test_error:.1f}px ({expected_zone})")
            
            # PID控制测试
            pid_duty = pid_speed_control(test_error, description)
            
            # 停止
            servo_pwm.duty(7.5)
            time.sleep(1)
            
            # 对比结果
            duty_diff = abs(direct_duty - pid_duty)
            print(f"  PWM差异: {duty_diff:.3f}%")
            
            if duty_diff < 0.01:
                print(f"  ✅ PWM设置一致，如果PID不动可能是其他问题")
            else:
                print(f"  ⚠️ PWM设置不一致，可能是计算逻辑问题")
        
        print(f"\n{'='*50}")
        print("🏁 对比测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_speed_filtering():
    """测试速度过滤逻辑"""
    print("\n🔍 速度过滤逻辑测试")
    print("="*25)
    
    global_speed_multiplier = 1.5
    
    test_speeds = [1, 2, 3, 4, 5, 6, 8, 10, -1, -2, -3, -4, -5, -6, -8, -10]
    
    print("原始速度 | 缩放后速度 | 是否被过滤 | 最终速度")
    print("-" * 45)
    
    for speed in test_speeds:
        scaled_speed = speed * global_speed_multiplier
        
        if abs(scaled_speed) < 0.1:
            filtered = "是"
            final_speed = 0
        else:
            filtered = "否"
            final_speed = scaled_speed
        
        print(f"{speed:7.1f}% | {scaled_speed:8.1f}% | {filtered:6s} | {final_speed:7.1f}%")

def test_error_thresholds():
    """测试误差阈值逻辑"""
    print("\n🔍 误差阈值逻辑测试")
    print("="*25)
    
    servo_error_threshold = 1  # main.py中的设置
    
    test_errors = [0.5, 1.0, 1.5, 2.0, 3.0, 5.0, 10.0, 15.0, 20.0]
    
    print("误差(px) | 误差大小 | 是否启动PID | 控制区域")
    print("-" * 45)
    
    for err_x in test_errors:
        error_magnitude = math.sqrt(err_x**2 + 0**2)  # err_y = 0
        
        if error_magnitude > servo_error_threshold:
            pid_enabled = "是"
            
            horizontal_error = -err_x
            if abs(horizontal_error) <= 3:
                zone = "逼近区域"
            elif abs(horizontal_error) <= 12:
                zone = "精细区域"
            elif abs(horizontal_error) <= 25:
                zone = "减速区域"
            elif abs(horizontal_error) <= 50:
                zone = "正常区域"
            else:
                zone = "PID区域"
        else:
            pid_enabled = "否"
            zone = "停止"
        
        print(f"{err_x:6.1f} | {error_magnitude:7.1f} | {pid_enabled:8s} | {zone}")

def main():
    """主函数"""
    print("🔧 PID逻辑深度调试")
    print("="*25)
    
    try:
        test_speed_filtering()
        test_error_thresholds()
        test_direct_vs_pid()
        
        print("\n" + "="*50)
        print("🏁 所有测试完成！")
        print("\n📋 可能的问题：")
        print("1. 速度过滤逻辑过于严格")
        print("2. 误差阈值设置不当")
        print("3. PID控制区域判断有误")
        print("4. PWM计算逻辑不一致")
        print("5. 其他未知的逻辑问题")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
