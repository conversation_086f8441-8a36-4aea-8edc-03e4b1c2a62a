# 舵机速度快速调节指南

## 🚀 快速调节方法

### 1. 主要参数调节
打开 `main.py` 文件，找到第30行：
```python
GLOBAL_SPEED_MULTIPLIER = 0.3  # 推荐从0.3开始调试（更慢更稳定）
```

**调节建议：**
- **舵机太快** → 改为 `0.2` 或 `0.1`
- **舵机太慢** → 改为 `0.4` 或 `0.5`
- **需要快速响应** → 改为 `0.6` 或 `0.7`

### 2. 推荐设置值

| 使用场景 | GLOBAL_SPEED_MULTIPLIER | 说明 |
|----------|-------------------------|------|
| 精密调试 | 0.1 - 0.2 | 非常慢，适合观察细节 |
| 一般使用 | 0.3 - 0.4 | 慢速稳定，推荐设置 |
| 平衡性能 | 0.5 - 0.6 | 中等速度，平衡精度和速度 |
| 快速响应 | 0.7 - 0.8 | 较快速度，适合快速目标 |
| 最大速度 | 0.9 - 1.0 | 最快速度，可能不稳定 |

## 🔧 详细参数说明

### 主要速度控制参数（在main.py中）

```python
# 第30行 - 最重要的参数
GLOBAL_SPEED_MULTIPLIER = 0.3  # 全局速度倍数

# 第33-34行 - 水平舵机参数
HORIZONTAL_MAX_SPEED = 12      # 水平舵机最大速度百分比
HORIZONTAL_MIN_SPEED = 2       # 水平舵机最小速度百分比

# 第37行 - 垂直舵机参数
VERTICAL_MAX_STEP = 2.0        # 垂直舵机每次最大角度变化

# 第50-53行 - 控制区域阈值
APPROACH_THRESHOLD = 5         # 停止阈值（像素）
FINE_ZONE = 12                 # 精细控制区
SLOW_ZONE = 25                 # 减速区
NORMAL_ZONE = 50               # 正常控制区

# 第56-59行 - 各区域最大速度
FINE_ZONE_MAX_SPEED = 4        # 精细区最大速度
SLOW_ZONE_MAX_SPEED = 8        # 减速区最大速度
NORMAL_ZONE_MAX_SPEED = 12     # 正常区最大速度
PID_ZONE_MAX_SPEED = 16        # PID区最大速度
```

## 🎯 常见问题解决

### 问题1: 舵机移动太快，难以精确控制
**解决方案：**
1. 减小 `GLOBAL_SPEED_MULTIPLIER` 到 0.2
2. 减小各区域最大速度：
   ```python
   FINE_ZONE_MAX_SPEED = 3
   SLOW_ZONE_MAX_SPEED = 6
   NORMAL_ZONE_MAX_SPEED = 10
   PID_ZONE_MAX_SPEED = 12
   ```

### 问题2: 舵机移动太慢，响应迟钝
**解决方案：**
1. 增大 `GLOBAL_SPEED_MULTIPLIER` 到 0.5-0.7
2. 增大各区域最大速度：
   ```python
   FINE_ZONE_MAX_SPEED = 6
   SLOW_ZONE_MAX_SPEED = 12
   NORMAL_ZONE_MAX_SPEED = 18
   PID_ZONE_MAX_SPEED = 24
   ```

### 问题3: 舵机在目标附近震荡
**解决方案：**
1. 增大停止阈值：`APPROACH_THRESHOLD = 8`
2. 增大精细控制区：`FINE_ZONE = 20`
3. 减小精细区速度：`FINE_ZONE_MAX_SPEED = 2`

### 问题4: 舵机响应不够灵敏
**解决方案：**
1. 减小停止阈值：`APPROACH_THRESHOLD = 3`
2. 增大PID系数：
   ```python
   HORIZONTAL_PID_KP = 0.1
   VERTICAL_PID_KP = 0.1
   ```

## 📝 修改步骤

### 步骤1: 备份原文件
```bash
cp main.py main.py.backup
```

### 步骤2: 修改参数
1. 用文本编辑器打开 `main.py`
2. 找到第30行，修改 `GLOBAL_SPEED_MULTIPLIER` 的值
3. 保存文件

### 步骤3: 测试效果
1. 运行程序：`python main.py`
2. 观察舵机响应速度
3. 如需调整，重复步骤2

### 步骤4: 精细调整（可选）
如果基本速度调整还不够，可以进一步调整：
- 各区域速度限制（第56-59行）
- 控制区域阈值（第50-53行）
- PID参数（第39-47行）

## 🔍 调试技巧

### 启用调试模式
在 `main.py` 中找到：
```python
DEBUG = False  # 改为 True
```

启用后会显示详细的控制信息，帮助你了解舵机的响应情况。

### 观察控制台输出
调试模式下会显示：
- 当前误差值
- 控制区域（精细区、减速区等）
- 舵机速度设置
- PID输出值

### 分步调试
1. **先调整全局速度**：只修改 `GLOBAL_SPEED_MULTIPLIER`
2. **再调整区域速度**：修改各区域最大速度
3. **最后调整PID**：修改PID参数

## ⚠️ 注意事项

1. **安全第一**：从慢速开始调试，避免舵机损坏
2. **逐步调整**：每次只修改一个参数，观察效果
3. **备份设置**：记录有效的参数组合
4. **硬件限制**：不同舵机的响应特性可能不同
5. **电源要求**：确保舵机电源充足，电压稳定

## 📊 参数对照表

| 参数名 | 默认值 | 调快 | 调慢 | 影响 |
|--------|--------|------|------|------|
| GLOBAL_SPEED_MULTIPLIER | 0.3 | 增大 | 减小 | 整体速度 |
| HORIZONTAL_MAX_SPEED | 12 | 增大 | 减小 | 水平最大速度 |
| VERTICAL_MAX_STEP | 2.0 | 增大 | 减小 | 垂直移动步长 |
| APPROACH_THRESHOLD | 5 | 增大 | 减小 | 停止精度 |
| FINE_ZONE_MAX_SPEED | 4 | 增大 | 减小 | 精细区速度 |

记住：**GLOBAL_SPEED_MULTIPLIER 是最重要的参数，90%的速度问题都可以通过调整它来解决！**
