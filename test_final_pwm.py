#!/usr/bin/env python3
"""
测试最终正确的PWM范围
验证顺时针7.66%→9.0%，逆时针7.15%→5.0%

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def test_final_pwm_ranges():
    """测试最终的PWM范围"""
    print("🎯 测试最终PWM范围")
    print("="*25)
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.65, enable=True)
        print("✓ PWM初始化成功")
        
        # 最终正确的PWM参数
        horizontal_stop_duty = 7.65      # 停止位置
        horizontal_cw_start = 7.66       # 顺时针开始
        horizontal_cw_duty = 9.0         # 顺时针最大
        horizontal_ccw_start = 7.15      # 逆时针开始
        horizontal_ccw_duty = 5.0        # 逆时针最小
        global_speed_multiplier = 1.5
        
        print(f"PWM参数:")
        print(f"  停止位置: {horizontal_stop_duty}%")
        print(f"  顺时针范围: {horizontal_cw_start}% → {horizontal_cw_duty}% (范围: {horizontal_cw_duty - horizontal_cw_start:.2f}%)")
        print(f"  逆时针范围: {horizontal_ccw_start}% → {horizontal_ccw_duty}% (范围: {horizontal_ccw_start - horizontal_ccw_duty:.2f}%)")
        
        def test_speed_final(speed, description):
            """使用最终正确逻辑测试速度"""
            print(f"\n{description}: {speed}%")
            
            # 应用缩放
            final_speed = speed * global_speed_multiplier
            
            # 最终正确的PWM计算逻辑
            if final_speed == 0:
                duty = horizontal_stop_duty
                direction = "停止"
                range_info = f"固定在{horizontal_stop_duty}%"
            elif final_speed > 0:
                # 顺时针：从7.66%开始到9.0%
                duty_range = horizontal_cw_duty - horizontal_cw_start  # 1.34%
                duty_change = (abs(final_speed) / 100.0) * duty_range
                duty = horizontal_cw_start + duty_change  # 从7.66%开始
                duty = min(duty, horizontal_cw_duty)  # 不超过9.0%
                direction = "顺时针"
                range_info = f"{horizontal_cw_start}% + {duty_change:.3f}% = {duty:.3f}%"
            else:
                # 逆时针：从7.15%开始到5.0%
                duty_range = horizontal_ccw_start - horizontal_ccw_duty  # 2.15%
                duty_change = (abs(final_speed) / 100.0) * duty_range
                duty = horizontal_ccw_start - duty_change  # 从7.15%开始向下
                duty = max(duty, horizontal_ccw_duty)  # 不小于5.0%
                direction = "逆时针"
                range_info = f"{horizontal_ccw_start}% - {duty_change:.3f}% = {duty:.3f}%"
            
            print(f"  最终速度: {final_speed:.1f}%")
            print(f"  PWM计算: {range_info}")
            print(f"  预期方向: {direction}")
            
            # 设置PWM并观察
            servo_pwm.duty(duty)
            time.sleep(3)
            
            return duty
        
        # 测试不同速度
        test_cases = [
            (0, "停止测试"),
            (5, "小速度顺时针"),
            (-5, "小速度逆时针"),
            (10, "中速度顺时针"),
            (-10, "中速度逆时针"),
            (20, "高速度顺时针"),
            (-20, "高速度逆时针"),
            (50, "最大速度顺时针"),
            (-50, "最大速度逆时针"),
            (0, "最终停止")
        ]
        
        print("\n开始最终PWM范围测试...")
        
        for speed, description in test_cases:
            duty = test_speed_final(speed, description)
            
            # 回到停止位置
            if speed != 0:
                servo_pwm.duty(horizontal_stop_duty)
                time.sleep(1)
        
        print("\n" + "="*25)
        print("最终PWM范围测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def show_final_calculation_table():
    """显示最终PWM计算表"""
    print("\n📊 最终PWM计算表")
    print("="*50)
    
    # 参数
    stop = 7.65
    cw_start = 7.66
    cw_max = 9.0
    ccw_start = 7.15
    ccw_min = 5.0
    multiplier = 1.5
    
    print(f"参数设置:")
    print(f"  停止: {stop}%")
    print(f"  顺时针: {cw_start}% → {cw_max}% (范围: {cw_max - cw_start:.2f}%)")
    print(f"  逆时针: {ccw_start}% → {ccw_min}% (范围: {ccw_start - ccw_min:.2f}%)")
    print(f"  全局倍数: {multiplier}")
    
    print(f"\n{'原始速度':<8} {'最终速度':<10} {'PWM占空比':<12} {'方向':<8} {'计算过程'}")
    print("-" * 70)
    
    test_speeds = [0, 5, -5, 10, -10, 20, -20, 30, -30, 50, -50]
    
    for speed in test_speeds:
        final_speed = speed * multiplier
        
        if final_speed == 0:
            duty = stop
            direction = "停止"
            calculation = f"固定 {stop}%"
        elif final_speed > 0:
            duty_range = cw_max - cw_start  # 1.34
            duty_change = (abs(final_speed) / 100.0) * duty_range
            duty = cw_start + duty_change
            duty = min(duty, cw_max)
            direction = "顺时针"
            calculation = f"{cw_start} + {duty_change:.3f}"
        else:
            duty_range = ccw_start - ccw_min  # 2.15
            duty_change = (abs(final_speed) / 100.0) * duty_range
            duty = ccw_start - duty_change
            duty = max(duty, ccw_min)
            direction = "逆时针"
            calculation = f"{ccw_start} - {duty_change:.3f}"
        
        print(f"{speed:<8} {final_speed:<10.1f} {duty:<12.3f} {direction:<8} {calculation}")

def main():
    """主函数"""
    print("🎯 最终PWM范围测试")
    print("="*25)
    
    try:
        print("最终正确的PWM范围：")
        print("- 停止位置: 7.65%")
        print("- 顺时针: 7.66% → 9.0% (实际工作范围)")
        print("- 逆时针: 7.15% → 5.0% (实际工作范围)")
        
        # 显示计算表
        show_final_calculation_table()
        
        # 实际测试
        test_final_pwm_ranges()
        
        print("\n" + "="*25)
        print("🏁 最终测试完成！")
        print("\n📋 如果测试成功：")
        print("✅ 顺时针应该从7.66%开始工作")
        print("✅ 逆时针应该从7.15%开始工作")
        print("✅ 停止应该精确在7.65%")
        
        print("\n🚀 现在main.py应该完全正常工作了！")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
