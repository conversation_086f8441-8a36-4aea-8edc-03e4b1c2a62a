#!/usr/bin/env python3
"""
最简单的水平舵机测试
一步到位，无需任何输入

@author: AI Assistant
@date: 2025.8.2
"""

from maix import pwm, pinmap, time

def simple_servo_test():
    """最简单的舵机测试"""
    print("🔧 最简单的水平舵机测试")
    print("=" * 30)
    
    try:
        # 1. 配置引脚
        print("📌 配置A18引脚为PWM6...")
        pinmap.set_pin_function("A18", "PWM6")
        
        # 2. 初始化PWM
        print("⚡ 初始化PWM6 (50Hz)...")
        servo_pwm = pwm.PWM(6, freq=50, duty=0, enable=False)
        
        print("✅ 初始化成功！")
        print("📋 舵机信息: LD-3015MG, A18->PWM6, 50Hz")
        
        # 3. 简单测试序列
        print("\n🧪 开始简单测试...")
        
        # 测试角度和对应的PWM占空比
        test_sequence = [
            (135, 7.5, "中心位置"),
            (90, 5.0, "左转"),
            (135, 7.5, "回中心"),
            (180, 10.0, "右转"),
            (135, 7.5, "最终回中心")
        ]
        
        for angle, duty, description in test_sequence:
            print(f"📍 {description}: {angle}°, PWM: {duty}%")
            
            # 启用PWM并设置占空比
            servo_pwm.enable()
            servo_pwm.duty(duty)
            
            # 等待2秒观察舵机运动
            time.sleep(2)
        
        print("\n✅ 测试完成！")
        print("🔍 观察结果:")
        print("  - 舵机是否转动到了正确位置？")
        print("  - 运动是否平滑？")
        print("  - 是否有异常声音？")
        
        # 4. 禁用PWM
        print("\n🔓 禁用PWM，舵机可手动搬动...")
        servo_pwm.disable()
        
        print("\n🎉 测试成功完成！")
        print("💡 如果舵机工作正常，可以集成到main.py中")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("请检查:")
        print("1. A18引脚连接是否正确")
        print("2. 舵机电源是否正常")
        print("3. MaixCAM Pro是否支持PWM6")
        
        # 尝试清理
        try:
            if 'servo_pwm' in locals():
                servo_pwm.disable()
        except:
            pass

def quick_angle_test():
    """快速角度测试"""
    print("\n🎯 快速角度验证")
    print("-" * 20)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        
        print("✅ PWM已启用，舵机应该在中心位置")
        print("⏰ 保持5秒...")
        time.sleep(5)
        
        servo_pwm.disable()
        print("🔓 PWM已禁用")
        
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")

if __name__ == "__main__":
    # 运行简单测试
    simple_servo_test()
    
    # 运行快速角度测试
    quick_angle_test()
    
    print("\n👋 所有测试结束")
