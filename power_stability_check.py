#!/usr/bin/env python3
"""
电源稳定性检查工具
检查电源供电是否稳定，是否会导致自动重启

@author: AI Assistant
@date: 2025.8.2
"""

import time
import os
from maix import pwm, pinmap

class PowerStabilityChecker:
    def __init__(self):
        """初始化电源稳定性检查器"""
        print("🔋 电源稳定性检查工具")
        print("=" * 40)
        
        self.test_results = []
        self.voltage_readings = []
        
    def check_system_voltage(self):
        """检查系统电压（如果可用）"""
        try:
            # 尝试读取系统电压信息
            voltage_files = [
                '/sys/class/power_supply/battery/voltage_now',
                '/sys/class/power_supply/usb/voltage_now',
                '/proc/cpuinfo'  # 备用信息源
            ]
            
            voltage_info = {}
            
            for voltage_file in voltage_files:
                if os.path.exists(voltage_file):
                    try:
                        with open(voltage_file, 'r') as f:
                            content = f.read().strip()
                            if 'voltage_now' in voltage_file:
                                # 电压通常以微伏为单位
                                voltage = int(content) / 1000000.0  # 转换为伏特
                                voltage_info[voltage_file] = voltage
                            else:
                                voltage_info[voltage_file] = content[:100]  # 限制长度
                    except:
                        continue
            
            return voltage_info
            
        except Exception as e:
            print(f"⚠️ 无法读取系统电压: {e}")
            return {}
    
    def test_servo_power_consumption(self):
        """测试舵机功耗对系统稳定性的影响"""
        print("\n🔧 测试舵机功耗影响")
        print("-" * 30)
        
        try:
            # 初始化舵机PWM
            print("📌 初始化舵机PWM...")
            pinmap.set_pin_function("A18", "PWM6")
            pinmap.set_pin_function("A19", "PWM7")
            
            horizontal_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=False)
            vertical_pwm = pwm.PWM(7, freq=50, duty=7.5, enable=False)
            
            print("✅ PWM初始化成功")
            
            # 测试序列：逐步增加负载
            test_sequence = [
                ("空载测试", False, False, "系统基准状态"),
                ("单舵机测试", True, False, "启用水平舵机"),
                ("双舵机测试", True, True, "启用双舵机"),
                ("高频运动测试", True, True, "双舵机快速运动"),
                ("停止测试", False, False, "停止所有舵机")
            ]
            
            for test_name, h_enable, v_enable, description in test_sequence:
                print(f"\n📍 {test_name}: {description}")
                
                # 记录测试开始时的电压
                voltage_before = self.check_system_voltage()
                
                try:
                    # 设置舵机状态
                    if h_enable:
                        horizontal_pwm.enable()
                        print("  🔒 水平舵机已启用")
                    else:
                        horizontal_pwm.disable()
                        print("  🔓 水平舵机已禁用")
                    
                    if v_enable:
                        vertical_pwm.enable()
                        print("  🔒 垂直舵机已启用")
                    else:
                        vertical_pwm.disable()
                        print("  🔓 垂直舵机已禁用")
                    
                    # 如果是运动测试，进行舵机运动
                    if test_name == "高频运动测试":
                        print("  🏃 开始快速运动测试...")
                        for i in range(10):
                            # 快速改变PWM占空比
                            horizontal_pwm.duty(6.0 + i * 0.3)
                            vertical_pwm.duty(6.0 + i * 0.3)
                            time.sleep(0.2)
                            
                            horizontal_pwm.duty(9.0 - i * 0.3)
                            vertical_pwm.duty(9.0 - i * 0.3)
                            time.sleep(0.2)
                        
                        # 回到中心位置
                        horizontal_pwm.duty(7.5)
                        vertical_pwm.duty(7.5)
                    
                    # 等待稳定
                    time.sleep(2)
                    
                    # 记录测试后的电压
                    voltage_after = self.check_system_voltage()
                    
                    # 分析结果
                    test_result = {
                        'test_name': test_name,
                        'description': description,
                        'voltage_before': voltage_before,
                        'voltage_after': voltage_after,
                        'success': True,
                        'notes': []
                    }
                    
                    # 检查电压变化
                    for key in voltage_before:
                        if key in voltage_after:
                            if isinstance(voltage_before[key], (int, float)) and isinstance(voltage_after[key], (int, float)):
                                voltage_change = voltage_after[key] - voltage_before[key]
                                if abs(voltage_change) > 0.1:  # 电压变化超过0.1V
                                    test_result['notes'].append(f"电压变化: {voltage_change:+.2f}V")
                    
                    self.test_results.append(test_result)
                    print(f"  ✅ {test_name}完成")
                    
                except Exception as e:
                    print(f"  ❌ {test_name}失败: {e}")
                    test_result = {
                        'test_name': test_name,
                        'description': description,
                        'success': False,
                        'error': str(e)
                    }
                    self.test_results.append(test_result)
            
            # 清理
            horizontal_pwm.disable()
            vertical_pwm.disable()
            print("\n🧹 舵机PWM已清理")
            
        except Exception as e:
            print(f"❌ 舵机功耗测试失败: {e}")
    
    def test_system_stability(self):
        """测试系统稳定性"""
        print("\n🧪 系统稳定性测试")
        print("-" * 25)
        
        try:
            # 检查系统负载
            if os.path.exists('/proc/loadavg'):
                with open('/proc/loadavg', 'r') as f:
                    load_avg = f.read().strip()
                    print(f"📊 系统负载: {load_avg}")
            
            # 检查内存使用
            if os.path.exists('/proc/meminfo'):
                with open('/proc/meminfo', 'r') as f:
                    meminfo = f.read()
                    for line in meminfo.split('\n')[:5]:  # 前5行
                        if line.strip():
                            print(f"💾 {line}")
            
            # 检查温度
            temp_files = [
                '/sys/class/thermal/thermal_zone0/temp',
                '/sys/class/thermal/thermal_zone1/temp'
            ]
            
            for i, temp_file in enumerate(temp_files):
                if os.path.exists(temp_file):
                    try:
                        with open(temp_file, 'r') as f:
                            temp = int(f.read().strip()) / 1000.0
                            print(f"🌡️ 温度传感器{i}: {temp:.1f}°C")
                            
                            if temp > 70:
                                print(f"  ⚠️ 温度过高，可能导致重启")
                            elif temp > 60:
                                print(f"  ⚠️ 温度较高，需要注意")
                    except:
                        continue
            
        except Exception as e:
            print(f"❌ 系统稳定性检查失败: {e}")
    
    def generate_power_report(self):
        """生成电源稳定性报告"""
        print(f"\n📋 电源稳定性报告")
        print("=" * 40)
        
        # 测试结果统计
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result.get('success', False))
        
        print(f"测试总数: {total_tests}")
        print(f"成功测试: {successful_tests}")
        print(f"失败测试: {total_tests - successful_tests}")
        
        # 详细结果
        print(f"\n📊 详细测试结果:")
        for i, result in enumerate(self.test_results, 1):
            status = "✅" if result.get('success', False) else "❌"
            print(f"{i}. {status} {result['test_name']}: {result['description']}")
            
            if 'notes' in result and result['notes']:
                for note in result['notes']:
                    print(f"   📝 {note}")
            
            if 'error' in result:
                print(f"   ❌ 错误: {result['error']}")
        
        # 建议
        print(f"\n💡 建议:")
        
        failed_tests = [r for r in self.test_results if not r.get('success', False)]
        if failed_tests:
            print("⚠️ 发现问题:")
            for result in failed_tests:
                print(f"  - {result['test_name']}: {result.get('error', '未知错误')}")
            
            print("\n🔧 可能的解决方案:")
            print("  1. 检查电源适配器功率是否足够")
            print("  2. 检查电源线连接是否牢固")
            print("  3. 检查舵机电源是否独立供电")
            print("  4. 降低舵机运动速度和频率")
            print("  5. 检查系统散热是否良好")
        else:
            print("✅ 所有测试通过，电源稳定性良好")
        
        # 电压变化分析
        voltage_changes = []
        for result in self.test_results:
            if 'notes' in result:
                for note in result['notes']:
                    if '电压变化' in note:
                        voltage_changes.append(note)
        
        if voltage_changes:
            print(f"\n⚡ 电压变化记录:")
            for change in voltage_changes:
                print(f"  - {change}")
            print("  建议: 如果电压变化过大，考虑使用更稳定的电源")

def main():
    """主函数"""
    print("🔋 MaixCAM Pro 电源稳定性检查")
    print("检查电源是否会导致自动重启问题")
    print("=" * 50)
    
    checker = PowerStabilityChecker()
    
    try:
        # 检查系统状态
        checker.test_system_stability()
        
        # 测试舵机功耗影响
        checker.test_servo_power_consumption()
        
        # 生成报告
        checker.generate_power_report()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    
    except Exception as e:
        print(f"\n❌ 测试过程异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n👋 电源稳定性检查完成")

if __name__ == "__main__":
    main()
