# 水平舵机不动问题诊断指南

## 🚨 问题描述
垂直舵机方向正确，但水平舵机不动。

## 🔍 问题原因分析

### 已修复的问题：
1. **速度过度缩放** - 多层缩放导致最终速度太小
2. **死区阈值过高** - 小速度被误判为0
3. **全局速度倍数过小** - 0.3倍数可能太保守

### 修复措施：
1. 降低死区阈值：`2% → 0.5%`
2. 增大基础缩放因子：`0.5 → 1.0`
3. 临时增大全局速度：`0.3 → 0.6`
4. 添加详细调试信息

## 🛠️ 诊断步骤

### 步骤1: 启用调试模式
```python
DEBUG = True  # 在main.py中取消注释
```

### 步骤2: 观察调试输出
运行程序后，观察控制台输出：
```
速度缩放: 10.00 → 6.00 (缩放因子: 1.0 × 0.6 = 0.600)
逼近控制: 原始err_x=50.0, H_err=-50.0, H_speed=-20.0
  → 目标在画面右侧，舵机速度=-20.0% (逆时针)
```

### 步骤3: 检查速度计算
确认以下信息：
- 原始误差值 (`err_x`) 是否合理
- 处理后误差 (`H_err`) 是否正确
- 计算出的速度 (`H_speed`) 是否足够大
- 缩放后的最终速度是否被死区过滤

### 步骤4: 硬件测试
运行专项测试：
```
🔧 水平舵机专项测试
测试速度: 5%
测试速度: 10%
测试速度: 15%
...
```

## 🔧 可能的解决方案

### 方案1: 调整速度参数（已实施）
```python
# 在main.py中已修改：
GLOBAL_SPEED_MULTIPLIER = 0.6  # 从0.3增加到0.6
speed_deadzone = 0.5           # 从2.0降低到0.5
self.speed_scale_factor = 1.0  # 从0.5增加到1.0
```

### 方案2: 进一步增大速度（如果仍不动）
```python
# 第30行
GLOBAL_SPEED_MULTIPLIER = 0.8  # 进一步增大

# 第33行
HORIZONTAL_MAX_SPEED = 20      # 增大最大速度

# 第56-59行 - 增大各区域速度限制
FINE_ZONE_MAX_SPEED = 8        # 从4增加到8
SLOW_ZONE_MAX_SPEED = 15       # 从8增加到15
NORMAL_ZONE_MAX_SPEED = 20     # 从12增加到20
PID_ZONE_MAX_SPEED = 25        # 从16增加到25
```

### 方案3: 检查PWM设置（如果软件调整无效）
```python
# 检查水平舵机PWM参数（第98-100行）
self.horizontal_stop_duty = 7.5    # 停止占空比
self.horizontal_cw_duty = 8.0      # 顺时针占空比
self.horizontal_ccw_duty = 7.0     # 逆时针占空比
```

可能需要调整为：
```python
self.horizontal_cw_duty = 8.5      # 增大占空比差异
self.horizontal_ccw_duty = 6.5     # 增大占空比差异
```

## 📊 速度计算分析

### 当前计算流程：
1. **PID输出**：比如 `horizontal_speed = 20`
2. **第一层缩放**：`20 × 1.0 = 20`
3. **第二层缩放**：`20 × 0.6 = 12`
4. **死区检查**：`12 > 0.5` ✓ 通过
5. **最终PWM**：根据12%计算占空比

### 之前的问题（已修复）：
1. **PID输出**：`horizontal_speed = 20`
2. **第一层缩放**：`20 × 0.5 = 10`
3. **第二层缩放**：`10 × 0.3 = 3`
4. **死区检查**：`3 > 2` ✓ 通过，但很接近
5. **最终检查**：`3 > 2` ✓ 但可能在PWM转换时丢失

## 🧪 测试验证

### 测试1: 手动速度测试
```python
# 在调试模式下，程序会自动运行：
servo_controller.test_horizontal_only()
```

### 测试2: 观察PWM输出
查看调试信息中的占空比值：
```
水平速度设置为: 12.0%, 占空比: 7.560%
```

### 测试3: 检查舵机响应
- 占空比应该在 6.5% - 8.5% 范围内变化
- 7.5% 应该是停止位置
- 大于7.5% 应该是一个方向
- 小于7.5% 应该是另一个方向

## ⚠️ 硬件检查

如果软件调整后仍不动，检查：

### 1. 电源供应
- 舵机是否有独立5V电源
- 电流是否足够（360°舵机通常需要更大电流）
- 电源线是否接触良好

### 2. 信号线连接
- PWM信号线是否连接到正确引脚（A18）
- 信号线是否有断路或接触不良
- 地线是否共地

### 3. 舵机本身
- 手动转动舵机轴，是否有阻力
- 更换一个已知正常的舵机测试
- 检查舵机型号是否支持连续旋转

### 4. PWM频率
- 确认PWM频率为50Hz
- 某些舵机可能需要不同频率

## 🎯 预期结果

修复后应该看到：
```
逼近控制: 原始err_x=50.0, H_err=-50.0, H_speed=-12.0
    速度缩放: -20.00 → -12.00 (缩放因子: 1.0 × 0.6 = 0.600)
水平速度设置为: -12.0%, 占空比: 7.200%
  → 目标在画面右侧，舵机速度=-12.0% (逆时针)
```

并且舵机应该开始转动！

## 📝 调试检查清单

- [ ] 启用DEBUG模式
- [ ] 观察速度计算过程
- [ ] 确认速度不为0
- [ ] 确认占空比在合理范围
- [ ] 检查电源供应
- [ ] 检查信号线连接
- [ ] 运行专项测试
- [ ] 必要时调整PWM参数

记住：**耐心调试，从软件到硬件，逐步排查！**
