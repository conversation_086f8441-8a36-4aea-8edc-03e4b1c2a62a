# 垂直舵机向上偏移45度修正报告

## 🎯 修改目标
将垂直舵机的整体角度向上偏移45度，以获得更好的向上视角覆盖，适应实际应用需求。

## ✅ 已完成的修正

### 1. 角度范围重新定义
```python
# 修正前 (0-180度范围)
self.vertical_min_angle = 0       # 最小角度（上极限）
self.vertical_max_angle = 180     # 最大角度（下极限）
self.vertical_center_angle = 90   # 中心角度
self.vertical_current_angle = 90  # 当前角度

# 修正后 (向上偏移45度)
self.vertical_min_angle = 45      # 最小角度（向上45度）
self.vertical_max_angle = 225     # 最大角度（向下225度）
self.vertical_center_angle = 135  # 中心角度（偏移后的中心）
self.vertical_current_angle = 135 # 当前角度（135度中心位置）
```

### 2. PWM映射函数更新
```python
def vertical_angle_to_duty(self, angle):
    """将角度转换为PWM占空比 (垂直舵机45-225度范围，向上偏移45度)"""
    # 限制角度范围在设定的安全范围内
    angle = max(self.vertical_min_angle, min(self.vertical_max_angle, angle))

    # 线性映射到270度舵机的PWM范围：
    # 45度 -> 2.5% (500μs)   - 向上极限
    # 135度 -> 7.5% (1500μs) - 中心位置（向上偏移45度）
    # 225度 -> 12.5% (2500μs) - 向下极限
    duty = 2.5 + ((angle - 45) / 180.0) * (12.5 - 2.5)
    return duty
```

### 3. 角度映射验证
```python
# 关键角度点验证
45度:  duty = 2.5 + ((45-45)/180) * 10 = 2.5%    # 500μs  (向上极限)
135度: duty = 2.5 + ((135-45)/180) * 10 = 7.5%   # 1500μs (水平中心)
225度: duty = 2.5 + ((225-45)/180) * 10 = 12.5%  # 2500μs (向下极限)
```

### 4. 中心位置调整
```python
# 修正前
self.set_vertical_angle(90)   # 90度中心

# 修正后  
self.set_vertical_angle(135)  # 135度中心（向上偏移45度后的水平位置）
```

### 5. 测试函数更新
```python
# 垂直舵机测试序列
test_sequence = [
    (90, "向上45度"),      # 相对水平向上45度
    (180, "向下45度"),     # 相对水平向下45度
    (135, "回中心(水平)")  # 水平中心位置
]
```

## 📊 角度对应关系

### 物理角度 vs 控制角度
| 物理方向 | 控制角度 | PWM占空比 | 脉宽 | 说明 |
|----------|----------|-----------|------|------|
| 向上45° | 45° | 2.5% | 500μs | 向上极限 |
| 水平 | 135° | 7.5% | 1500μs | 中心位置 |
| 向下45° | 225° | 12.5% | 2500μs | 向下极限 |

### 相对角度理解
```
物理视角 (相对水平面):
        ↑ +45° (控制角度45°)
        |
水平 ←──┼──→ 0° (控制角度135°)
        |
        ↓ -45° (控制角度225°)
```

## 🎯 优势分析

### 1. 视角覆盖优化
- ✅ **更好的向上视角**: 可以向上看45度
- ✅ **保持向下视角**: 可以向下看45度  
- ✅ **对称覆盖**: ±45度的对称视角范围
- ✅ **实用性强**: 适合大多数监控和跟踪应用

### 2. 控制逻辑优化
- ✅ **中心位置直观**: 135度对应水平方向
- ✅ **范围合理**: 90度总范围，避免过度转动
- ✅ **精度保持**: 充分利用270度舵机的精度
- ✅ **安全可靠**: 在舵机安全工作范围内

### 3. 应用场景适配
- ✅ **目标跟踪**: 更好地跟踪空中目标
- ✅ **监控应用**: 适合监控摄像头的视角需求
- ✅ **机器人视觉**: 符合机器人头部运动习惯
- ✅ **云台控制**: 标准云台的角度配置

## 🔧 PWM映射详解

### 偏移前后对比
```
偏移前 (0°-180°):
0°   → 2.5%  (500μs)   - 向上极限
90°  → 7.5%  (1500μs)  - 水平中心
180° → 12.5% (2500μs)  - 向下极限

偏移后 (45°-225°):
45°  → 2.5%  (500μs)   - 向上45度
135° → 7.5%  (1500μs)  - 水平中心
225° → 12.5% (2500μs)  - 向下45度
```

### 数学公式
```python
# 偏移前公式
duty = 2.5 + (angle / 180.0) * 10.0

# 偏移后公式  
duty = 2.5 + ((angle - 45) / 180.0) * 10.0
```

## 🧪 测试验证

### 1. 角度映射测试
```python
# 测试关键角度点
def test_vertical_angle_mapping():
    # 向上极限
    assert vertical_angle_to_duty(45) == 2.5
    
    # 水平中心
    assert vertical_angle_to_duty(135) == 7.5
    
    # 向下极限
    assert vertical_angle_to_duty(225) == 12.5
```

### 2. 物理方向测试
```python
test_choice = "4"  # 测试双轴舵机方向
# 验证：
# - 90度应该向上看
# - 135度应该水平看
# - 180度应该向下看
```

### 3. 中心位置测试
```python
servo_controller.center_vertical()  # 应该设置到135度(水平)
```

## 📋 双轴舵机最终配置

| 舵机 | 角度范围 | 中心位置 | 物理含义 | PWM范围 |
|------|----------|----------|----------|---------|
| **垂直舵机** | 45°-225° | 135° | 向上45°↔向下45° | 2.5%-12.5% |
| **水平舵机** | 45°-225° | 135° | 左90°↔右90° | 约3.2%-11.8% |

## 🎉 修正完成

垂直舵机现在已经正确配置为向上偏移45度：

- ✅ **角度范围**: 45°-225° (物理上为向上45°到向下45°)
- ✅ **中心位置**: 135° (对应水平方向)
- ✅ **PWM映射**: 充分利用270度舵机的完整PWM范围
- ✅ **视角优化**: 获得更好的向上视角覆盖
- ✅ **控制精度**: 保持高精度位置控制

这样的配置既满足了向上视角的需求，又保持了系统的稳定性和精确性！

## 🔍 实际应用效果

### 视角覆盖对比
```
偏移前: 向上90° ↔ 水平 ↔ 向下90°
偏移后: 向上45° ↔ 水平 ↔ 向下45°
```

偏移后的配置更适合实际应用，因为：
1. **向上45度足够**: 覆盖大部分空中目标
2. **向下45度合理**: 避免看到地面过多无用信息
3. **范围适中**: 90度总范围，响应更快
4. **中心直观**: 135度对应水平，便于理解和调试
