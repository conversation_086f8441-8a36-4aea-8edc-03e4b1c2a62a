"""
K210风格PID控制配置文件
基于K210 CODE.py的PID控制逻辑，适配MaixCAM Pro的舵机控制
"""

# ==================== K210风格PID参数 ====================

# 全局速度倍数 (0.1-1.0)
# 影响舵机响应速度，越小越慢越稳定
GLOBAL_SPEED_MULTIPLIER = 0.4  # 推荐从0.4开始

# K210风格PID系数 - 参考原始CODE.py
# K210原始代码: P=(ZX-xin)*0, I=(ZX-xin)*1+I, PI=P+I
# 这表示只使用积分控制，比例和微分系数都为0

# 水平舵机PID参数（360°连续旋转舵机）
HORIZONTAL_KP = 0.0    # 比例系数（K210原始为0）
HORIZONTAL_KI = 1.0    # 积分系数（K210原始为1）
HORIZONTAL_KD = 0.0    # 微分系数（K210原始未使用）

# 垂直舵机PID参数（180°位置舵机）
VERTICAL_KP = 0.0      # 比例系数（K210原始为0）
VERTICAL_KI = 1.0      # 积分系数（K210原始为1）
VERTICAL_KD = 0.0      # 微分系数（K210原始未使用）

# PID限制参数
INTEGRAL_LIMIT = 500   # 积分限制，防止积分饱和
OUTPUT_LIMIT = 300     # 输出限制

# 舵机位置参数 - 参考K210的位置值
# K210原始: intiposit=2096, posit=2025, positx=2048
VERTICAL_CENTER_POSITION = 2048    # 垂直舵机中心位置
VERTICAL_MIN_POSITION = 1000       # 垂直舵机最小位置
VERTICAL_MAX_POSITION = 3000       # 垂直舵机最大位置

HORIZONTAL_CENTER_POSITION = 2048  # 水平舵机中心位置

# ==================== 改进版PID参数 ====================
# 如果纯K210风格控制不够稳定，可以尝试这些改进参数

# 改进版1：添加比例控制，减少超调
IMPROVED_V1 = {
    'h_kp': 0.1,    # 添加小量比例控制
    'h_ki': 0.8,    # 减小积分系数
    'h_kd': 0.0,    # 仍不使用微分
    'v_kp': 0.1,
    'v_ki': 0.8,
    'v_kd': 0.0,
    'integral_limit': 300,
    'output_limit': 200
}

# 改进版2：添加微分控制，减少震荡
IMPROVED_V2 = {
    'h_kp': 0.05,   # 小量比例控制
    'h_ki': 0.6,    # 进一步减小积分
    'h_kd': 0.02,   # 添加小量微分控制
    'v_kp': 0.05,
    'v_ki': 0.6,
    'v_kd': 0.02,
    'integral_limit': 200,
    'output_limit': 150
}

# 改进版3：平衡控制，适合大多数情况
IMPROVED_V3 = {
    'h_kp': 0.08,   # 适中的比例控制
    'h_ki': 0.5,    # 适中的积分控制
    'h_kd': 0.01,   # 小量微分控制
    'v_kp': 0.08,
    'v_ki': 0.5,
    'v_kd': 0.01,
    'integral_limit': 250,
    'output_limit': 180
}

# ==================== 预设配置选择 ====================

# 当前使用的配置（修改这里来切换不同的PID参数）
CURRENT_CONFIG = "k210_original"  # 可选: "k210_original", "improved_v1", "improved_v2", "improved_v3"

def get_current_pid_config():
    """获取当前PID配置"""
    if CURRENT_CONFIG == "k210_original":
        return {
            'h_kp': HORIZONTAL_KP,
            'h_ki': HORIZONTAL_KI,
            'h_kd': HORIZONTAL_KD,
            'v_kp': VERTICAL_KP,
            'v_ki': VERTICAL_KI,
            'v_kd': VERTICAL_KD,
            'integral_limit': INTEGRAL_LIMIT,
            'output_limit': OUTPUT_LIMIT
        }
    elif CURRENT_CONFIG == "improved_v1":
        return IMPROVED_V1
    elif CURRENT_CONFIG == "improved_v2":
        return IMPROVED_V2
    elif CURRENT_CONFIG == "improved_v3":
        return IMPROVED_V3
    else:
        print(f"未知配置: {CURRENT_CONFIG}，使用K210原始配置")
        return get_current_pid_config.__defaults__[0] if hasattr(get_current_pid_config, '__defaults__') else {
            'h_kp': 0.0, 'h_ki': 1.0, 'h_kd': 0.0,
            'v_kp': 0.0, 'v_ki': 1.0, 'v_kd': 0.0,
            'integral_limit': 500, 'output_limit': 300
        }

def print_current_config():
    """打印当前PID配置"""
    config = get_current_pid_config()
    print("=== 当前K210风格PID配置 ===")
    print(f"配置名称: {CURRENT_CONFIG}")
    print(f"全局速度倍数: {GLOBAL_SPEED_MULTIPLIER}")
    print(f"水平PID: Kp={config['h_kp']}, Ki={config['h_ki']}, Kd={config['h_kd']}")
    print(f"垂直PID: Kp={config['v_kp']}, Ki={config['v_ki']}, Kd={config['v_kd']}")
    print(f"积分限制: {config['integral_limit']}")
    print(f"输出限制: {config['output_limit']}")
    print(f"垂直中心位置: {VERTICAL_CENTER_POSITION}")
    print(f"水平中心位置: {HORIZONTAL_CENTER_POSITION}")
    print("============================")

def apply_config(config_name):
    """应用指定的PID配置"""
    global CURRENT_CONFIG
    valid_configs = ["k210_original", "improved_v1", "improved_v2", "improved_v3"]
    
    if config_name in valid_configs:
        CURRENT_CONFIG = config_name
        print(f"已切换到配置: {config_name}")
        print_current_config()
    else:
        print(f"无效配置名称: {config_name}")
        print(f"可用配置: {valid_configs}")

# ==================== 使用说明 ====================
"""
使用方法:

1. 基本使用（K210原始风格）:
   - 保持 CURRENT_CONFIG = "k210_original"
   - 只调整 GLOBAL_SPEED_MULTIPLIER 来控制响应速度

2. 如果K210原始风格不够稳定:
   - 尝试 CURRENT_CONFIG = "improved_v1"  # 添加比例控制
   - 或者 CURRENT_CONFIG = "improved_v2"  # 添加微分控制
   - 或者 CURRENT_CONFIG = "improved_v3"  # 平衡控制

3. 自定义参数:
   - 直接修改对应配置字典中的参数值
   - 或者创建新的配置字典

4. 在主程序中使用:
   ```python
   from k210_pid_config import *
   config = get_current_pid_config()
   servo_controller.set_pid_parameters(
       h_kp=config['h_kp'], h_ki=config['h_ki'], h_kd=config['h_kd'],
       v_kp=config['v_kp'], v_ki=config['v_ki'], v_kd=config['v_kd']
   )
   ```

参数调试建议:
1. 先使用K210原始配置测试基本功能
2. 如果有超调，尝试improved_v1（添加比例控制）
3. 如果有震荡，尝试improved_v2（添加微分控制）
4. 如果需要平衡性能，使用improved_v3
5. 最后微调GLOBAL_SPEED_MULTIPLIER来控制整体响应速度
"""

# 启动时打印当前配置
if __name__ == "__main__":
    print_current_config()
else:
    print(f"K210风格PID配置已加载: {CURRENT_CONFIG}")
    print(f"全局速度倍数: {GLOBAL_SPEED_MULTIPLIER}")
