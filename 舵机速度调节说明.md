# 舵机速度调节说明

## 问题描述
识别到目标后，舵机移动太快，需要让它移动得更慢一些。
- **水平舵机**（360°连续旋转）：速度控制，容易过快
- **垂直舵机**（180°位置控制）：角度控制，也可能移动过快

## 解决方案

### 方法1：修改配置文件（推荐）
1. 打开 `servo_config.py` 文件
2. 修改 `GLOBAL_SPEED_MULTIPLIER` 参数：
   ```python
   GLOBAL_SPEED_MULTIPLIER = 0.3  # 0.1=很慢, 0.5=中等, 1.0=最快
   ```
3. 保存文件并重新运行程序

### 方法2：使用预设配置
在 `servo_config.py` 中调用预设配置：
```python
# 在文件末尾添加
apply_config("ultra_slow")  # 超慢速
# apply_config("slow")      # 慢速
# apply_config("medium")    # 中速
# apply_config("fast")      # 快速
```

### 方法3：直接修改主程序
在 `main.py` 中找到这行：
```python
servo_speed_multiplier = 0.6
```
改为更小的值，比如：
```python
servo_speed_multiplier = 0.3  # 更慢的速度
```

## 参数说明

### 主要速度控制参数
- `GLOBAL_SPEED_MULTIPLIER`: 全局速度倍数（最重要，影响两个舵机）
  - 0.1 = 非常慢（精密调试用）
  - 0.3 = 慢速（推荐起始值）
  - 0.6 = 中等速度
  - 1.0 = 最快速度

### 水平舵机速度控制（360°连续旋转）

- `FINE_ZONE_MAX_SPEED`: 精细控制区最大速度（目标附近）
- `SLOW_ZONE_MAX_SPEED`: 减速区最大速度
- `NORMAL_ZONE_MAX_SPEED`: 正常区最大速度
- `PID_ZONE_MAX_SPEED`: PID控制区最大速度（远离目标时）

### 垂直舵机速度控制（180°位置控制）
- `VERTICAL_MAX_STEP`: 每次最大角度变化（度）
  - 1.0 = 非常慢（精密控制）
  - 2.0 = 慢速（推荐）
  - 5.0 = 快速响应

### 控制区域阈值
- `APPROACH_THRESHOLD`: 停止阈值（像素）
- `FINE_ZONE`: 精细控制区范围
- `SLOW_ZONE`: 减速区范围
- `NORMAL_ZONE`: 正常控制区范围

## 调试建议

### 第一步：调整全局速度
1. 先将 `GLOBAL_SPEED_MULTIPLIER` 设为 0.2（很慢）
2. 运行程序测试舵机速度
3. 如果太慢，逐步增加到 0.3, 0.4, 0.5...
4. 找到合适的速度后停止

### 第二步：精细调整（可选）
如果需要更精细的控制：
1. 调整各区域的最大速度限制
2. 调整PID参数来改善响应特性
3. 调整控制区域的阈值

### 第三步：测试和优化
1. 在不同距离测试舵机响应
2. 观察是否有震荡或过冲现象
3. 根据需要微调参数

## 常见问题

### Q: 舵机移动太快怎么办？
A: 减小 `GLOBAL_SPEED_MULTIPLIER` 的值

### Q: 舵机移动太慢怎么办？
A: 增大 `GLOBAL_SPEED_MULTIPLIER` 的值

### Q: 舵机在目标附近震荡怎么办？
A: 
1. 减小 `FINE_ZONE_MAX_SPEED`
2. 增大 `APPROACH_THRESHOLD`
3. 减小PID参数中的 `KP` 值

### Q: 舵机响应太慢怎么办？
A:
1. 适当增大 `GLOBAL_SPEED_MULTIPLIER`
2. 增大各区域的最大速度限制
3. 适当增大PID参数中的 `KP` 值

### Q: 如何恢复默认设置？
A: 删除 `servo_config.py` 文件，程序会使用内置的默认配置

## 推荐配置

### 精密控制（推荐）
```python
GLOBAL_SPEED_MULTIPLIER = 0.3
FINE_ZONE_MAX_SPEED = 4
SLOW_ZONE_MAX_SPEED = 8
NORMAL_ZONE_MAX_SPEED = 12
PID_ZONE_MAX_SPEED = 16
```

### 平衡配置
```python
GLOBAL_SPEED_MULTIPLIER = 0.5
FINE_ZONE_MAX_SPEED = 6
SLOW_ZONE_MAX_SPEED = 10
NORMAL_ZONE_MAX_SPEED = 15
PID_ZONE_MAX_SPEED = 20
```

### 快速响应
```python
GLOBAL_SPEED_MULTIPLIER = 0.7
FINE_ZONE_MAX_SPEED = 8
SLOW_ZONE_MAX_SPEED = 12
NORMAL_ZONE_MAX_SPEED = 18
PID_ZONE_MAX_SPEED = 25
```

## 注意事项
1. 修改参数后需要重新运行程序才能生效
2. 速度太慢可能导致跟踪延迟
3. 速度太快可能导致震荡和不稳定
4. 建议从慢速开始调试，逐步增加速度
5. 不同的舵机型号可能需要不同的参数设置
