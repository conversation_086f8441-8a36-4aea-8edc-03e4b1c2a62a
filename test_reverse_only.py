#!/usr/bin/env python3
"""
专门测试逆时针转动
只测试逆时针方向，不测试顺时针

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def test_reverse_from_765():
    """从7.65%开始测试逆时针"""
    print("🔄 专门测试逆时针转动")
    print("="*25)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.65, enable=True)
        print("✓ PWM初始化成功，停止位置7.65%")
        
        print("\n逐步测试逆时针PWM值：")
        
        # 从7.65%开始，逐步减小PWM值
        reverse_tests = [
            7.60, 7.55, 7.50, 7.45, 7.40, 7.35, 7.30, 7.25, 7.20, 7.15, 7.10,
            7.00, 6.90, 6.80, 6.70, 6.60, 6.50, 6.40, 6.30, 6.20, 6.10, 6.00,
            5.90, 5.80, 5.70, 5.60, 5.50, 5.40, 5.30, 5.20, 5.10, 5.00,
            4.50, 4.00, 3.50, 3.00
        ]
        
        working_reverse = []
        
        for duty in reverse_tests:
            change = duty - 7.65
            print(f"\n测试逆时针: {duty:.2f}% (变化: {change:+.2f}%)")
            
            try:
                servo_pwm.duty(duty)
                print(f"  观察4秒，舵机是否逆时针转动...")
                time.sleep(4)
                
                # 记录能设置的PWM值
                working_reverse.append(duty)
                print(f"  ✅ {duty:.2f}% 设置成功")
                
                # 回到停止位置
                servo_pwm.duty(7.65)
                time.sleep(1)
                
            except Exception as e:
                print(f"  ❌ {duty:.2f}% 设置失败: {e}")
                break  # 如果设置失败，停止测试更极端的值
        
        print(f"\n" + "="*25)
        print("逆时针测试完成！")
        
        if working_reverse:
            print(f"✅ 能设置的逆时针PWM值: {len(working_reverse)}个")
            print(f"   范围: {min(working_reverse):.2f}% ~ {max(working_reverse):.2f}%")
            print(f"   所有值: {working_reverse}")
            
            # 找出第一个让舵机转动的逆时针PWM值
            print(f"\n请观察并记录：")
            print(f"- 哪个PWM值第一次让舵机逆时针转动？")
            print(f"- 逆时针转动的速度如何？")
            
        else:
            print("❌ 没有找到能工作的逆时针PWM值")
            print("   这个舵机可能只能单向转动")
        
        return working_reverse
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return []

def test_specific_reverse_values():
    """测试特定的逆时针PWM值"""
    print("\n🎯 测试特定逆时针PWM值")
    print("="*25)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.65, enable=True)
        print("✓ PWM初始化成功")
        
        # 测试一些关键的逆时针PWM值
        key_values = [
            (7.50, "中等逆时针"),
            (7.00, "大幅逆时针"),
            (6.50, "很大逆时针"),
            (6.00, "极大逆时针"),
            (5.50, "超大逆时针"),
            (5.00, "极限逆时针"),
            (4.00, "超极限逆时针")
        ]
        
        print("\n测试关键逆时针PWM值，每个观察6秒：")
        
        for duty, description in key_values:
            change = duty - 7.65
            print(f"\n{description}: {duty:.2f}% (变化: {change:+.2f}%)")
            
            try:
                servo_pwm.duty(duty)
                print(f"  观察6秒，舵机反应...")
                time.sleep(6)
                
                print(f"  ✅ {duty:.2f}% 设置成功")
                
                # 回到停止位置
                servo_pwm.duty(7.65)
                time.sleep(2)
                
            except Exception as e:
                print(f"  ❌ {duty:.2f}% 设置失败: {e}")
        
        print("\n特定值测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def analyze_servo_behavior():
    """分析舵机行为"""
    print("\n📊 舵机行为分析")
    print("="*20)
    
    print("基于测试结果分析：")
    print("\n已知信息：")
    print("- 停止位置: 7.65%")
    print("- 顺时针工作: 7.66%+ (已确认)")
    print("- 逆时针工作: 待测试")
    
    print("\n可能的情况：")
    print("1. 真正的360度舵机:")
    print("   - 逆时针PWM值应该在 < 7.65% 范围内")
    print("   - 可能需要很小的PWM值 (如 5.0% 或更小)")
    
    print("\n2. 单向连续旋转舵机:")
    print("   - 只能顺时针连续转动")
    print("   - 逆时针方向完全不工作")
    print("   - 需要使用变速控制模拟双向")
    
    print("\n3. 改装的180度舵机:")
    print("   - 机械限位被移除，但电路只支持单向")
    print("   - 表现为单向连续旋转")

def main():
    """主函数"""
    print("🔄 专门测试逆时针转动")
    print("="*25)
    
    try:
        print("目标：找出逆时针转动的PWM范围")
        print("已知：停止位置 7.65%，顺时针 7.66%+")
        
        # 逐步测试逆时针
        working_reverse = test_reverse_from_765()
        
        # 测试特定值
        test_specific_reverse_values()
        
        # 分析结果
        analyze_servo_behavior()
        
        print("\n" + "="*25)
        print("🏁 逆时针测试完成！")
        
        if working_reverse:
            min_reverse = min(working_reverse)
            print(f"\n✅ 找到逆时针PWM范围!")
            print(f"   建议逆时针PWM: {min_reverse:.2f}%")
            print(f"   建议参数更新:")
            print(f"   self.horizontal_stop_duty = 7.65")
            print(f"   self.horizontal_ccw_duty = {min_reverse:.2f}")
        else:
            print(f"\n❌ 未找到逆时针PWM值")
            print(f"   这个舵机可能只能单向转动")
            print(f"   建议:")
            print(f"   1. 更换为真正的双向360度舵机")
            print(f"   2. 或使用单向变速控制方案")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
