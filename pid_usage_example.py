#!/usr/bin/env python3
"""
高级PID控制器使用示例
展示如何在main.py中使用新的PID系统

@author: AI Assistant
@date: 2025.8.2
"""

from advanced_pid_controller import AdvancedPIDController, ServoStabilityController, AdvancedServoController

def example_usage():
    """展示高级PID控制器的使用方法"""
    
    print("🎯 高级PID控制器使用示例")
    print("=" * 50)
    
    # 1. 创建高级舵机控制器（与main.py中的方式相同）
    print("\n1. 创建高级舵机控制器...")
    
    # PID参数（参考222.py的优化参数）
    pid_params = {
        "Kp": 0.2,           # 比例系数（进一步降低）
        "Ki": 0.008,         # 积分系数（很小的积分）
        "Kd": 0.75,          # 微分系数（增加阻尼）
        "error_threshold": 8, # 积分分离阈值
        "integral_limit": 10, # 积分限幅值
        "min_output": 1,     # 最小输出
        "max_output": 15,    # 最大输出限制
        "max_step": 2        # PID输出变化率限制
    }
    
    # 稳定性参数
    stability_params = {
        "stability_check_frames": 5,  # 连续稳定帧数要求
        "max_stable_error": 6,        # 认为稳定的最大误差
        "error_dead_zone": 5          # 误差死区
    }
    
    # 创建控制器
    controller = AdvancedServoController(
        pid_params=pid_params,
        stability_params=stability_params,
        debug=True  # 开启调试模式
    )
    
    print("✅ 高级舵机控制器创建成功")
    
    # 2. 模拟目标跟踪场景
    print("\n2. 模拟目标跟踪场景...")
    
    # 画面中心
    center_x, center_y = 224, 224
    
    # 模拟目标移动轨迹
    target_positions = [
        (274, 194, "目标在右上"),
        (270, 200, "目标微调1"),
        (268, 202, "目标微调2"),
        (226, 224, "目标接近中心"),
        (224, 224, "目标在中心"),
        (224, 224, "目标保持中心"),
        (174, 254, "目标移到左下"),
        (180, 250, "目标微调3"),
        (224, 224, "目标回到中心")
    ]
    
    print(f"画面中心: ({center_x}, {center_y})")
    print(f"模拟{len(target_positions)}个目标位置")
    
    # 3. 逐帧处理
    print("\n3. 逐帧处理结果:")
    print("-" * 50)
    
    for i, (target_x, target_y, description) in enumerate(target_positions):
        print(f"\n帧 {i+1}: {description}")
        print(f"目标位置: ({target_x}, {target_y})")
        
        # 使用高级控制器计算控制输出
        should_move, control_x, control_y, error_info = controller.compute_control(
            target_x, target_y, center_x, center_y
        )
        
        # 显示结果
        print(f"误差: X={error_info['err_x']:+.1f}, Y={error_info['err_y']:+.1f}")
        print(f"总误差: {error_info['total_error']:.1f}")
        print(f"控制输出: X={control_x:+.2f}, Y={control_y:+.2f}")
        print(f"舵机动作: {'移动' if should_move else '静止'}")
        print(f"系统状态: {'稳定' if error_info['is_stable'] else '不稳定'}")
        
        # 模拟舵机角度变化（假设当前角度为135度）
        if should_move:
            current_h_angle = 135.0  # 假设的当前水平角度
            current_v_angle = 135.0  # 假设的当前垂直角度
            speed_multiplier = 0.15  # 与main.py中相同的速度倍数
            
            new_h_angle = current_h_angle + control_x * speed_multiplier
            new_v_angle = current_v_angle + control_y * speed_multiplier
            
            print(f"角度变化: 水平 {current_h_angle:.1f}° → {new_h_angle:.1f}°")
            print(f"         垂直 {current_v_angle:.1f}° → {new_v_angle:.1f}°")
    
    # 4. 显示最终状态
    print("\n4. 最终控制器状态:")
    print("-" * 30)
    status = controller.get_full_status()
    print(f"PID X状态: 积分={status['pid_x']['integral']:.3f}, 上次误差={status['pid_x']['last_error']:.1f}")
    print(f"PID Y状态: 积分={status['pid_y']['integral']:.3f}, 上次误差={status['pid_y']['last_error']:.1f}")
    print(f"稳定性: {'稳定' if status['stability']['is_stable'] else '不稳定'}")
    print(f"偏移补偿: X={status['offset']['x']}, Y={status['offset']['y']}")
    
    print("\n✅ 示例完成")

def integration_guide():
    """集成指南"""
    print("\n" + "=" * 60)
    print("🔧 集成指南：如何在main.py中使用高级PID")
    print("=" * 60)
    
    print("\n1. 导入模块（已完成）:")
    print("   from advanced_pid_controller import AdvancedServoController")
    
    print("\n2. 在ServoController.__init__中初始化（已完成）:")
    print("   self.advanced_controller = AdvancedServoController(...)")
    
    print("\n3. 在主循环中使用（已完成）:")
    print("   moved = servo_controller.advanced_pid_control(")
    print("       target_x, target_y, center_x, center_y)")
    
    print("\n4. 可用的控制方法:")
    print("   - servo_controller.set_advanced_pid_debug(True/False)")
    print("   - servo_controller.get_advanced_pid_status()")
    print("   - servo_controller.reset_pid()  # 会同时重置高级PID")
    
    print("\n5. 高级PID的优势:")
    print("   ✅ 积分分离：大误差时不积分，防止积分饱和")
    print("   ✅ 积分限幅：限制积分项最大值")
    print("   ✅ 死区补偿：确保最小输出，克服机械死区")
    print("   ✅ 输出变化率限制：防止舵机运动过于剧烈")
    print("   ✅ 稳定性检测：连续稳定时停止调整")
    print("   ✅ 动态死区：根据稳定状态调整死区大小")
    print("   ✅ 防振荡优化：多重机制防止来回摆动")
    
    print("\n6. 参数调优建议:")
    print("   - Kp: 控制响应速度，过大会振荡")
    print("   - Ki: 消除稳态误差，过大会超调")
    print("   - Kd: 增加阻尼，提高稳定性")
    print("   - error_threshold: 积分分离阈值，建议5-10")
    print("   - error_dead_zone: 死区大小，建议3-8像素")
    
    print("\n7. 调试模式:")
    print("   - 使用测试选项 d 启用调试")
    print("   - 使用测试选项 e 禁用调试")
    print("   - 调试模式会显示详细的PID计算过程")

if __name__ == "__main__":
    # 运行使用示例
    example_usage()
    
    # 显示集成指南
    integration_guide()
    
    print("\n🎉 高级PID控制器已成功集成到main.py中！")
    print("💡 提示：运行main.py时选择测试选项 d 可开启调试模式")
