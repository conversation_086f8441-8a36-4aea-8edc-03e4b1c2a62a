'''
    2025电赛E题找A4 UV纸圆心，可以找到圆心和第三个圆圈，帧率 > 25fps。
    有多种设置和算法，根据实际情况选择。
    控制云台可以基于中心点误差 err_center 进行 PID 控制

    新增功能：
    - 集成UART0总线舵机控制
    - 基于圆心误差的高级PID自动跟踪
    - 移植丢失追踪.py的微小偏差控制算法
    - 积分分离和积分限幅技术
    - 偏移补偿功能
    - A29 GPIO控制继电器激光笔自动打靶
    - A28 GPIO控制设备自动触发（低电平打开，高电平关闭）

    高级PID控制特点：
    - 积分分离：大误差时不积分，防止积分饱和
    - 积分限幅：限制积分项最大值
    - 死区补偿：确保最小输出，克服机械死区
    - 输出变化率限制：防止舵机运动过于剧烈
    - 微小偏差控制：偏差大时转速快，偏差小时转速慢
    - 误差死区：当误差很小时舵机完全不动，避免抖动
    - 防振荡优化：稳定性检测、动态死区
    - 防超调优化：低增益PID、强阻尼、小步长限制

    舵机控制配置：
    - enable_servo_control: 启用/禁用舵机控制
    - servo_uart_device: 舵机串口设备路径
    - pid_error_threshold: 积分分离阈值
    - offset_x/offset_y: 偏移补偿参数

    激光控制配置：
    - enable_laser_control: 启用/禁用激光控制
    - laser_gpio_pin: 激光继电器控制引脚(A29，低电平触发)
    - laser_duration: 激光开启持续时间(0.5s)
    - laser_cooldown: 激光冷却时间(1.0s)
    - laser_trigger_threshold: 触发激光的误差阈值(3像素)

    A28控制配置：
    - enable_a28_control: 启用/禁用A28控制
    - a28_gpio_pin: A28控制引脚(低电平打开，高电平关闭)
    - a28_duration: A28开启持续时间(0.5s)
    - a28_cooldown: A28冷却时间(1.0s)
    - a28_trigger_threshold: 触发A28的误差阈值(3像素)

    <AUTHOR> & lxo@sieed 协助
    @license MIT
    @date 2025.7.30
'''

from maix import camera, display, image, nn, app, time, pinmap
from maix.peripheral import uart, gpio
disp = display.Display()
img = image.Image(disp.width(), disp.height())
msg = "Loading ..."
size = image.string_size(msg, scale = 1.5, thickness=2)
img.draw_string((img.width() - size.width()) // 2, (img.height() - size.height()) // 2, msg, scale=1.5, thickness=2)
disp.show(img)

import cv2
import numpy as np
import os

# 全局状态变量
now_state = 0  # 初始状态为0

# 状态切换清理标志
state_change_cleanup_needed = False
cleanup_old_state = 0
cleanup_new_state = 0

# 状态2强制激光定时器
class ForceLaserTimer:
    def __init__(self):
        self.timer_active = False
        self.start_time = 0
        self.force_fire_duration = 500  # 强制发射持续时间(ms)
        self.force_fire_start_time = 0
        self.is_force_firing = False
        self.timeout_duration = 1500  # 1.5秒超时

    def start_timer(self):
        """开始计时"""
        self.timer_active = True
        self.start_time = time.ticks_ms()
        self.is_force_firing = False
        print("=" * 50)
        print("🚀 状态2强制激光定时器启动！")
        print("⏰ 超时时间: 1.5秒")
        print("🔴 如果1.5秒内激光未发射，将强制发射0.5秒")
        print("=" * 50)

    def stop_timer(self):
        """停止计时"""
        self.timer_active = False
        self.is_force_firing = False
        print("🛑 状态2强制激光定时器已停止")

    def update(self):
        """更新定时器状态"""
        if not self.timer_active:
            return "inactive"

        current_time = time.ticks_ms()

        # 如果正在强制发射
        if self.is_force_firing:
            if current_time - self.force_fire_start_time >= self.force_fire_duration:
                # 强制发射完成
                self.is_force_firing = False
                self.timer_active = False
                print("🔴 状态2强制激光发射完成")
                return "force_fire_complete"
            return "force_firing"

        # 检查是否超时
        if current_time - self.start_time >= self.timeout_duration:
            # 超时，开始强制发射
            self.is_force_firing = True
            self.force_fire_start_time = current_time
            print("⚠️⚠️⚠️ 状态2定时器超时！⚠️⚠️⚠️")
            print(f"⏰ 已等待: {self.timeout_duration/1000:.1f}秒")
            print("🔴 激光未正常发射，启动强制发射程序")
            return "force_fire_start"

        return "timing"

    def get_remaining_time(self):
        """获取剩余时间(ms)"""
        if not self.timer_active:
            return 0
        current_time = time.ticks_ms()
        elapsed = current_time - self.start_time
        remaining = max(0, self.timeout_duration - elapsed)
        return remaining

# 创建状态2定时器实例
force_laser_timer = ForceLaserTimer()

# 状态3扫描控制器
class ScanController:
    def __init__(self):
        self.scanning = False
        self.scan_start_time = 0
        self.current_angle = 0
        self.target_angle = -35  # 扫描起始角度，稍后会从全局变量更新
        self.scan_direction = 1  # 1: 向右扫描, -1: 向左扫描
        self.target_found = False
        self.force_fire_triggered = False
        self.scan_phase = "moving_to_start"  # "moving_to_start", "scanning", "completed"
        self.move_start_time = 0

    def start_scan(self):
        """开始扫描"""
        global scan_start_angle, scan_end_angle, scan_step_speed, scan_timeout

        self.scanning = True
        self.scan_start_time = time.ticks_ms()
        self.move_start_time = time.ticks_ms()
        self.current_angle = 0  # 当前舵机位置（假设从中心开始）
        self.target_angle = scan_start_angle
        self.scan_direction = 1
        self.target_found = False
        self.force_fire_triggered = False
        self.scan_phase = "moving_to_start"  # 开始时先移动到起始位置
        print("=" * 50)
        print("🔍 状态3扫描模式启动！")
        print(f"📐 扫描范围: {scan_start_angle}° → {scan_end_angle}°")
        print(f"⚡ 扫描速度: {scan_step_speed}")
        print(f"⏰ 强制发射时间: {scan_timeout}秒")
        print(f"🚀 第一步: 快速移动到起始位置 {scan_start_angle}°")
        print("=" * 50)

    def stop_scan(self):
        """停止扫描"""
        self.scanning = False
        self.target_found = False
        self.scan_phase = "completed"
        print("🛑 状态3扫描已停止")

    def update(self, target_detected=False):
        """更新扫描状态"""
        if not self.scanning:
            print("🔍 [扫描控制器] 未在扫描状态")
            return "inactive"

        current_time = time.ticks_ms()
        elapsed_time = (current_time - self.scan_start_time) / 1000.0

        # 详细调试信息
        print(f"🔍 [扫描控制器] 目标检测: {target_detected}, 已发现: {self.target_found}, 已用时: {elapsed_time:.2f}s, 阶段: {self.scan_phase}")

        # 检查是否发现目标
        if target_detected and not self.target_found:
            self.target_found = True
            print("🎯 扫描中发现目标！切换到跟踪模式")
            return "target_found"

        # 检查是否超时需要强制发射
        global scan_timeout
        if elapsed_time >= scan_timeout and not self.force_fire_triggered:
            self.force_fire_triggered = True
            print("⚠️⚠️⚠️ 状态3扫描超时！⚠️⚠️⚠️")
            print(f"⏰ 已扫描: {elapsed_time:.1f}秒")
            print("🔴 未发现目标，启动强制发射程序")
            return "scan_timeout"

        # 如果已经发现目标，继续跟踪模式
        if self.target_found:
            print(f"🔍 [扫描控制器] 继续跟踪模式")
            return "tracking"

        # 如果已经强制发射，等待完成
        if self.force_fire_triggered:
            print(f"🔍 [扫描控制器] 强制发射中")
            return "force_firing"

        # 正常扫描过程
        print(f"🔍 [扫描控制器] 正常扫描中")
        return "scanning"

    def get_target_angle(self):
        """获取当前目标角度"""
        # 声明所有需要的全局变量
        global scan_start_angle, scan_end_angle, scan_timeout
        global scan_move_to_start_time, scan_step_angle, scan_step_duration

        if not self.scanning:
            return 0

        # 如果已经发现目标，停止扫描算法，保持当前角度
        if self.target_found:
            return self.target_angle

        current_time = time.ticks_ms()
        total_elapsed = (current_time - self.scan_start_time) / 1000.0

        if self.scan_phase == "moving_to_start":
            # 第一阶段：快速移动到起始位置
            move_elapsed = (current_time - self.move_start_time) / 1000.0
            move_duration = scan_move_to_start_time  # 使用配置参数

            if move_elapsed >= move_duration:
                # 移动完成，开始扫描
                self.scan_phase = "scanning"
                print(f"✅ 已到达起始位置 {scan_start_angle}°，开始扫描")
                return scan_start_angle
            else:
                # 正在移动到起始位置
                progress = move_elapsed / move_duration
                target_angle = 0 + (scan_start_angle - 0) * progress  # 从0度移动到起始角度
                self.target_angle = target_angle  # 保存当前目标角度
                return target_angle

        elif self.scan_phase == "scanning":
            # 第二阶段：定时步进扫描
            scan_elapsed = total_elapsed - scan_move_to_start_time  # 减去移动到起始位置的时间
            scan_duration = scan_timeout - scan_move_to_start_time  # 剩余时间用于扫描

            if scan_elapsed >= scan_duration:
                return scan_end_angle

            # 定时步进扫描算法（使用配置参数）
            step_angle = scan_step_angle      # 从配置读取每步角度
            step_duration = scan_step_duration  # 从配置读取每步时间

            scan_range = scan_end_angle - scan_start_angle
            total_steps = int(abs(scan_range) / step_angle)

            if total_steps == 0:
                return scan_start_angle

            current_step = int(scan_elapsed / step_duration)
            if current_step >= total_steps:
                return scan_end_angle

            # 计算当前步进位置的角度
            if scan_range > 0:  # 正向扫描
                target_angle = scan_start_angle + (current_step * step_angle)
            else:  # 反向扫描
                target_angle = scan_start_angle - (current_step * step_angle)

            # 调试输出（减少频率）
            if current_step != getattr(self, 'last_step', -1):
                print(f"📍 [步进扫描] 步骤 {current_step}/{total_steps}, 角度: {target_angle:.1f}°, 用时: {scan_elapsed:.2f}s")
                self.last_step = current_step

            self.target_angle = target_angle  # 保存当前目标角度
            return target_angle

        return 0

    def get_remaining_time(self):
        """获取剩余时间"""
        if not self.scanning:
            return 0
        global scan_timeout
        current_time = time.ticks_ms()
        elapsed_time = (current_time - self.scan_start_time) / 1000.0
        remaining = max(0, scan_timeout - elapsed_time)
        return remaining

# 状态4扫描控制器（从右往左扫描）
class Scan4Controller:
    def __init__(self):
        self.scanning = False
        self.scan_start_time = 0
        self.current_angle = 0
        self.target_angle = 35  # 扫描起始角度（从右边开始）
        self.scan_direction = -1  # -1: 向左扫描
        self.target_found = False
        self.force_fire_triggered = False
        self.scan_phase = "moving_to_start"  # "moving_to_start", "scanning", "completed"
        self.move_start_time = 0

    def start_scan(self):
        """开始扫描"""
        global scan4_start_angle, scan4_end_angle, scan4_step_speed, scan4_timeout

        self.scanning = True
        self.scan_start_time = time.ticks_ms()
        self.move_start_time = time.ticks_ms()
        self.current_angle = 0  # 当前舵机位置（假设从中心开始）
        self.target_angle = scan4_start_angle
        self.scan_direction = -1  # 向左扫描
        self.target_found = False
        self.force_fire_triggered = False
        self.scan_phase = "moving_to_start"  # 开始时先移动到起始位置
        print("=" * 50)
        print("🔍 状态4扫描模式启动！")
        print(f"📐 扫描范围: {scan4_start_angle}° → {scan4_end_angle}°")
        print(f"⚡ 扫描速度: {scan4_step_speed}")
        print(f"⏰ 强制发射时间: {scan4_timeout}秒")
        print(f"🚀 第一步: 快速移动到起始位置 {scan4_start_angle}°")
        # 显示定时步进参数
        global scan_step_angle, scan_step_duration, scan_move_to_start_time
        print(f"🔧 定时步进参数: 每步{scan_step_angle}°, 停留{scan_step_duration}s, 移动时间{scan_move_to_start_time}s")
        scan_range = abs(scan4_end_angle - scan4_start_angle)
        total_steps = int(scan_range / scan_step_angle)
        theoretical_time = total_steps * scan_step_duration
        actual_scan_time = scan4_timeout - scan_move_to_start_time
        print(f"📊 扫描计算: 范围{scan_range}°, 总步数{total_steps}, 理论时间{theoretical_time:.1f}s, 实际时间{actual_scan_time:.1f}s")
        print("=" * 50)

    def stop_scan(self):
        """停止扫描"""
        self.scanning = False
        self.target_found = False
        self.scan_phase = "completed"
        print("🛑 状态4扫描已停止")

    def update(self, target_detected=False):
        """更新扫描状态"""
        if not self.scanning:
            print("🔍 [状态4扫描控制器] 未在扫描状态")
            return "inactive"

        current_time = time.ticks_ms()
        elapsed_time = (current_time - self.scan_start_time) / 1000.0

        # 详细调试信息
        print(f"🔍 [状态4扫描控制器] 目标检测: {target_detected}, 已发现: {self.target_found}, 已用时: {elapsed_time:.2f}s, 阶段: {self.scan_phase}")

        # 检查是否发现目标
        if target_detected and not self.target_found:
            self.target_found = True
            print("🎯 扫描中发现目标！切换到跟踪模式")
            return "target_found"

        # 检查是否超时需要强制发射
        global scan4_timeout
        if elapsed_time >= scan4_timeout and not self.force_fire_triggered:
            self.force_fire_triggered = True
            print("⚠️⚠️⚠️ 状态4扫描超时！⚠️⚠️⚠️")
            print(f"⏰ 已扫描: {elapsed_time:.1f}秒")
            print("🔴 未发现目标，启动强制发射程序")
            return "scan_timeout"

        # 如果已经发现目标，继续跟踪模式
        if self.target_found:
            print(f"🔍 [状态4扫描控制器] 继续跟踪模式")
            return "tracking"

        # 如果已经强制发射，等待完成
        if self.force_fire_triggered:
            print(f"🔍 [状态4扫描控制器] 强制发射中")
            return "force_firing"

        # 正常扫描过程
        print(f"🔍 [状态4扫描控制器] 正常扫描中")
        return "scanning"

    def get_target_angle(self):
        """获取当前目标角度"""
        # 声明所有需要的全局变量
        global scan4_start_angle, scan4_end_angle, scan4_timeout
        global scan_move_to_start_time, scan_step_angle, scan_step_duration

        if not self.scanning:
            return 0

        # 如果已经发现目标，停止扫描算法，保持当前角度
        if self.target_found:
            return self.target_angle

        current_time = time.ticks_ms()
        total_elapsed = (current_time - self.scan_start_time) / 1000.0

        if self.scan_phase == "moving_to_start":
            # 第一阶段：快速移动到起始位置
            move_elapsed = (current_time - self.move_start_time) / 1000.0
            if move_elapsed >= scan_move_to_start_time:  # 使用配置参数
                self.scan_phase = "scanning"
                print(f"✅ 已到达起始位置 {scan4_start_angle}°，开始扫描")

            # 在移动阶段，目标角度逐渐从当前位置移动到起始位置
            progress = min(move_elapsed / scan_move_to_start_time, 1.0)
            target_angle = self.current_angle + (scan4_start_angle - self.current_angle) * progress
            self.target_angle = target_angle  # 保存当前目标角度
            return target_angle

        elif self.scan_phase == "scanning":
            # 第二阶段：定时步进扫描（与状态3保持一致）
            scan_elapsed = total_elapsed - scan_move_to_start_time  # 减去移动到起始位置的时间
            scan_duration = scan4_timeout - scan_move_to_start_time  # 剩余时间用于扫描

            if scan_elapsed >= scan_duration:
                return scan4_end_angle

            # 定时步进扫描算法（使用配置参数）
            step_angle = scan_step_angle      # 从配置读取每步角度
            step_duration = scan_step_duration  # 从配置读取每步时间

            scan_range = scan4_end_angle - scan4_start_angle
            total_steps = int(abs(scan_range) / step_angle)

            if total_steps == 0:
                return scan4_start_angle

            current_step = int(scan_elapsed / step_duration)
            if current_step >= total_steps:
                return scan4_end_angle

            # 计算当前步进位置的角度
            if scan_range > 0:  # 正向扫描
                target_angle = scan4_start_angle + (current_step * step_angle)
            else:  # 反向扫描
                target_angle = scan4_start_angle - (current_step * step_angle)

            self.target_angle = target_angle  # 保存当前目标角度
            return target_angle

        return 0

    def get_remaining_time(self):
        """获取剩余时间"""
        if not self.scanning:
            return 0
        global scan4_timeout
        current_time = time.ticks_ms()
        elapsed_time = (current_time - self.scan_start_time) / 1000.0
        remaining = max(0, scan4_timeout - elapsed_time)
        return remaining

# 创建状态3和状态4扫描控制器实例
scan_controller = ScanController()
scan4_controller = Scan4Controller()

print(f"🚀 程序启动，初始状态: now_state = {now_state}")
print("📋 状态功能说明:")
print("   状态0: 仅图像处理模式")
print("   状态1: 图像处理+舵机控制模式")
print("   状态2: 图像处理+舵机控制+强制激光模式(1.5秒超时)")
print("   状态3: 扫描搜索+跟踪模式(3.5秒超时)")
print("   状态4-6: 预留功能模式")
print("🔧 状态切换时将自动清空PID积分并关闭激光/A28设备")
print("-" * 50)

# UART0接收处理类（独立于舵机控制）
class UART0Receiver:
    def __init__(self, uart_device="/dev/ttyS0", baudrate=115200):
        """
        UART0接收处理器

        Args:
            uart_device: UART设备路径，默认"/dev/ttyS0"
            baudrate: 波特率，默认115200
        """
        self.uart_device = uart_device
        self.baudrate = baudrate
        self.uart_rx = None
        self.received_buffer = []  # 接收数据缓冲区

        # 初始化UART接收
        self.init_uart_receiver()

    def init_uart_receiver(self):
        """初始化UART接收器"""
        try:
            # 创建独立的UART实例用于接收
            self.uart_rx = uart.UART(self.uart_device, self.baudrate)

            # 设置接收回调函数
            self.uart_rx.set_received_callback(self.on_uart_data_received)

            print(f"✅ UART0接收器初始化成功: {self.uart_device} @ {self.baudrate}")
            return True
        except Exception as e:
            print(f"❌ UART0接收器初始化失败: {e}")
            self.uart_rx = None
            return False

    def on_uart_data_received(self, uart_obj, data):
        """
        UART0数据接收回调函数 - 类似UART接收中断
        当UART0有数据到达时自动调用此函数

        Args:
            uart_obj: UART对象
            data: 接收到的数据 (bytes类型)
        """
        try:
            # 将接收到的数据添加到缓冲区
            self.received_buffer.append(data)

            # 打印接收到的原始数据（简要信息）
            print(f"📨 UART0接收: {len(data)}字节")
            if DEBUG:
                try:
                    # 尝试解码为字符串
                    data_str = data.decode('utf-8', errors='ignore')
                    print(f"📨 简要预览: '{data_str[:20]}{'...' if len(data_str) > 20 else ''}'")
                except:
                    # 如果无法解码，显示十六进制
                    hex_str = ' '.join([f'{b:02X}' for b in data[:10]])
                    print(f"📨 HEX预览: {hex_str}{'...' if len(data) > 10 else ''}")

            # ========================================
            # 在这里添加你的数据处理逻辑
            # ========================================
            self.process_received_data(data)

        except Exception as e:
            print(f"❌ UART0数据处理出错: {e}")

    def process_received_data(self, data):
        """
        处理接收到的数据 - 状态切换版本

        Args:
            data: 接收到的数据 (bytes类型)
        """
        global now_state, state_change_cleanup_needed, cleanup_old_state, cleanup_new_state  # 声明使用全局变量

        # ========================================
        # 状态切换逻辑
        # ========================================

        print("=" * 50)
        print(f"📨 UART0接收测试 - 数据长度: {len(data)} 字节")

        # 1. 打印原始字节数据
        print(f"📊 原始字节: {data}")

        # 2. 打印十六进制格式
        hex_str = ' '.join([f'{b:02X}' for b in data])
        print(f"🔢 十六进制: {hex_str}")

        # 3. 处理状态切换逻辑
        for byte in data:
            old_state = now_state  # 保存旧状态
            state_changed = False  # 状态是否改变标志

            if byte == 0x30:  # 接收到 '0' (ASCII 48, 0x30)
                if now_state != 0:
                    now_state = 0
                    state_changed = True
                    print(f"🔄 状态切换: {old_state} → {now_state} (接收到字符 '0')")

            elif byte == 0x31:  # 接收到 '1' (ASCII 49, 0x31)
                if now_state != 1:
                    now_state = 1
                    state_changed = True
                    print(f"🔄 状态切换: {old_state} → {now_state} (接收到字符 '1')")

            elif byte == 0x32:  # 接收到 '2' (ASCII 50, 0x32)
                if now_state != 2:
                    now_state = 2
                    state_changed = True
                    print(f"🔄 状态切换: {old_state} → {now_state} (接收到字符 '2')")

            elif byte == 0x33:  # 接收到 '3' (ASCII 51, 0x33)
                if now_state != 3:
                    now_state = 3
                    state_changed = True
                    print(f"🔄 状态切换: {old_state} → {now_state} (接收到字符 '3')")

            elif byte == 0x34:  # 接收到 '4' (ASCII 52, 0x34)
                if now_state != 4:
                    now_state = 4
                    state_changed = True
                    print(f"🔄 状态切换: {old_state} → {now_state} (接收到字符 '4')")

            elif byte == 0x35:  # 接收到 '5' (ASCII 53, 0x35)
                if now_state != 5:
                    now_state = 5
                    state_changed = True
                    print(f"🔄 状态切换: {old_state} → {now_state} (接收到字符 '5')")



            elif byte == 0x36:  # 接收到 '6' (ASCII 54, 0x36)
                if now_state != 6:
                    now_state = 6
                    state_changed = True
                    print(f"🔄 状态切换: {old_state} → {now_state} (接收到字符 '6')")

            # 如果状态发生了改变，执行清理操作
            if state_changed:
                self.handle_state_change(old_state, now_state)

        # 4. 显示当前状态
        print(f"📍 当前状态: now_state = {now_state}")

        # 5. 尝试解码为字符串并打印每个字符（调试用）
        if DEBUG:
            try:
                data_str = data.decode('utf-8', errors='replace')
                print(f"📝 字符串: '{data_str}'")

                # 打印每个字符的详细信息
                print("🔤 字符详情:")
                for i, char in enumerate(data_str):
                    ascii_val = ord(char)
                    if char.isprintable():
                        print(f"  [{i}] '{char}' (ASCII: {ascii_val}, HEX: 0x{ascii_val:02X})")
                    else:
                        if char == '\n':
                            print(f"  [{i}] '\\n' (换行, ASCII: {ascii_val}, HEX: 0x{ascii_val:02X})")
                        elif char == '\r':
                            print(f"  [{i}] '\\r' (回车, ASCII: {ascii_val}, HEX: 0x{ascii_val:02X})")
                        elif char == '\t':
                            print(f"  [{i}] '\\t' (制表符, ASCII: {ascii_val}, HEX: 0x{ascii_val:02X})")
                        else:
                            print(f"  [{i}] '?' (不可打印字符, ASCII: {ascii_val}, HEX: 0x{ascii_val:02X})")

            except UnicodeDecodeError as e:
                print(f"❌ 字符串解码失败: {e}")
                print("🔍 尝试逐字节解码:")
                for i, byte in enumerate(data):
                    if 32 <= byte <= 126:  # 可打印ASCII字符
                        print(f"  [{i}] '{chr(byte)}' (0x{byte:02X})")
                    else:
                        print(f"  [{i}] 不可打印 (0x{byte:02X})")

        print("=" * 50)
        print()  # 空行分隔

    def handle_state_change(self, old_state, new_state):
        """
        处理状态切换时的清理操作

        Args:
            old_state: 旧状态值
            new_state: 新状态值
        """
        print(f"🧹 状态切换清理: {old_state} → {new_state}")

        # 设置全局标志，让主循环处理清理
        global state_change_cleanup_needed, cleanup_old_state, cleanup_new_state
        state_change_cleanup_needed = True
        cleanup_old_state = old_state
        cleanup_new_state = new_state

        print(f"🔔 已设置清理标志，等待主循环处理...")
        print("-" * 30)

    def get_received_buffer(self):
        """获取接收缓冲区数据"""
        return self.received_buffer.copy()

    def clear_buffer(self):
        """清空接收缓冲区"""
        self.received_buffer.clear()
        print("🧹 UART0接收缓冲区已清空")

    def get_latest_data(self):
        """获取最新接收到的数据"""
        if self.received_buffer:
            return self.received_buffer[-1]
        return None

    def close(self):
        """关闭UART接收器"""
        if self.uart_rx:
            try:
                self.uart_rx.close()
                print("✅ UART0接收器已关闭")
            except Exception as e:
                print(f"⚠️  关闭UART0接收器时出错: {e}")
            finally:
                self.uart_rx = None


DEBUG=False
PRINT_TIME = False
debug_draw_err_line = False
debug_draw_err_msg = False
debug_draw_circle = False
debug_draw_rect = False
debug_show_hires = False

################################ config #########################################

# DEBUG=True                 # 打开调试模式，取消注释即可
# PRINT_TIME = True          # 打印每一步消耗的时间，取消注释即可
debug_draw_err_line = True   # 画出圆心和画面中心的误差线，需要消耗1ms左右时间
# debug_draw_err_msg = True    # 画出圆心和画面中心的误差值和 FPS 信息，需要消耗7ms左右时间，慎用
debug_draw_circle = True       # 画出圆圈，实际是画点，需要再打开变量, debug 模式都会画，耗费时间比较多，慎用
# debug_draw_rect = True         # 画出矩形框
debug_show_hires = True        # 显示结果在高分辨率图上，而不是小分辨率图上， 开启了 hires_mode 才生效

# 舵机控制配置
enable_servo_control = True    # 是否启用舵机控制
servo_uart_device = "/dev/ttyS0"  # 舵机串口设备，UART0
servo_baudrate = 115200        # 舵机串口波特率

# 舵机控制配置
servo_pan_range = [500, 2500]  # 水平舵机PWM范围
servo_tilt_range = [500, 2500] # 垂直舵机PWM范围
servo_center = [1500, 1500]    # 舵机中心位置（PWM值）[pan, tilt]
servo_speed = 20               # 舵机运动速度 (1-100，越大越快)

# 简化PID控制参数（彻底防超调）
pid_kp = 0.2                  # 比例系数（进一步降低）
pid_ki = 0.008                   # 积分系数（完全关闭积分）
pid_kd = 0.75                  # 微分系数（增加阻尼）
pid_error_threshold = 8        # 积分分离阈值
pid_integral_limit = 10        # 积分限幅值
pid_min_output = 1            # 最小输出
pid_max_output = 15           # 最大输出限制（进一步降低）
pid_max_step = 2              # PID输出变化率限制（进一步降低）

# 误差死区参数（当误差很小时不动）
error_dead_zone = 5            # 误差死区，增大到±5像素
center_threshold = 0           # 原CENTER_THRESHOLD，用于PID计算

# 简化控制参数
stability_check_frames = 5     # 连续稳定帧数要求
max_stable_error = 6          # 认为稳定的最大误差

# 偏移补偿参数（激光器或机械偏差补偿）
offset_x = -12                   # X轴偏移补偿
offset_y = -12                   # Y轴偏移补偿

# 激光控制参数
enable_laser_control = True    # 是否启用激光控制
laser_gpio_pin = "A28"         # 激光继电器控制引脚（低电平触发）- 使用A28
laser_duration = 0.5           # 激光开启持续时间（秒）
laser_cooldown = 1.0           # 激光冷却时间（秒）
laser_trigger_threshold = 3    # 触发激光的误差阈值（像素）

# A28控制参数（现在与激光控制共用A28引脚）
enable_a28_control = False     # 禁用独立的A28控制，因为A28现在用于激光控制
a28_gpio_pin = "A28"          # A28引脚
a28_duration = 0.5            # A28开启持续时间（秒）
a28_cooldown = 1.0            # A28冷却时间（秒）
a28_trigger_threshold = 3     # 触发A28的误差阈值（像素）


# 状态3扫描参数（从左往右扫描）
scan_start_angle = -50       # 扫描起始角度（度）
scan_end_angle = 50          # 扫描结束角度（度）
scan_step_speed = 100         # 扫描步进速度（舵机速度值，1-100，越大越快）
scan_timeout = 3.5           # 扫描超时时间（秒），3.5秒后强制发射

# 状态4扫描参数（从右往左扫描）
scan4_start_angle = 50       # 扫描起始角度（度）- 从右边开始
scan4_end_angle = -50        # 扫描结束角度（度）- 到左边结束
scan4_step_speed = 100       # 扫描步进速度（舵机速度值，1-100，越大越快）
scan4_timeout = 3.5          # 扫描超时时间（秒），3.5秒后强制发射

# 定时步进扫描算法参数（状态3和状态4共用）
scan_step_angle = 8.0        # 每步扫描角度（度），值越小精度越高但速度越慢
                             # 推荐范围：1.0-5.0度，默认2.0度
scan_step_duration = 0.15    # 每步停留时间（秒），值越大检测越充分但速度越慢
                             # 推荐范围：0.1-0.3秒，默认0.15秒
scan_move_to_start_time = 0.5  # 移动到起始位置的时间（秒）
                               # 推荐范围：0.3-1.0秒，默认0.5秒

# 定时步进算法说明：
# - 总扫描时间 = scan_timeout (3.5秒)
# - 移动时间 = scan_move_to_start_time (0.5秒)
# - 实际扫描时间 = 3.5 - 0.5 = 3.0秒
# - 扫描范围 = 100度 (从-50到+50)
# - 总步数 = 100 / scan_step_angle = 50步 (默认)
# - 理论扫描时间 = 50 * 0.15 = 7.5秒 > 3.0秒，所以会在3.0秒时超时

crop_padding = 12            # 裁切图时的外扩距离，调试到保证最近和最远位置整个黑框在检测框里，可以打开 DEBUG 模式看
rect_min_limit = 12          # 找到的大黑边框四个点最小距离必须大于这个值才有效，防止找到错误的值，可以放到最远位置测试
std_from_white_rect = True   # 裁切标准图是裁切自A4纸内部白色部分（更精准），False则是带黑框的外围框（整个A4纸）（更快一点点）
circle_num_points = 50       # 生成的第三个圆圈的点数量，控制圆边的平滑程度，可以用来巡迹
std_res = [int(29.7 / 21 * 80), 80]        # 找中心点和圆圈的分辨率，越大越精确，更慢，A4 29.7 x 21cm
hires_mode = True           # 高分辨模式，适合 find_circle 模式使用，帧率会更低但是找圆圈更精准
                             # 不 find_circle 也可以使用，找4个角点更精准，需要配合设置合理的 std_res
                             # 注意开启了这个模式，输出的误差值也是基于大图的分辨率
high_res = 448               # 高分辨率模式宽高,越高越清晰但是帧率越低，注意 std_res 也要跟着改大点
model_path = "/root/models/model_3356.mud" # 检测黑框模型路径，从 https://maixhub.com/model/zoo/1159 下载并传到开发板的 /root/models 目录


find_circle = False          # 在找到黑框以内白框后是否继续找圆，如果圆圈画得标准，在纸正中心则不用找，如果画点不在纸正中心则需要找。
                             # 建议把A4纸制作正确就不用找了，帧率更高。
                             # 可以用hires_mode 更清晰才能识别到，另外设置合理的 std_res
cam_buff_num = 1             # 摄像头缓冲， 1 延迟更低帧率慢一点点， 2延迟更高帧率高一点点
find_laser = False           # 找激光点（未测试），实际使用时直接把摄像头中心和激光点保持移植就好了，不需要找激光点

auto_awb = True                            # 自动白平衡或者手动白平衡
awb_gain = [0.134, 0.0625, 0.0625, 0.1139]  # 手动白平衡，auto_awb为False才生效， R GR GB B 的值，调 R 和 B 即可
contrast = 80                               # 对比度，会影响到检测，阴影和圆圈痕迹都会更重

###################################################################################

# 限制步长函数（防止输出变化过大）
def limit_step(current, last, max_step=2):
    """限制输出变化率，防止舵机运动过于剧烈"""
    delta = current - last
    if abs(delta) > max_step:
        return last + (max_step if delta > 0 else -max_step)
    else:
        return current

# 高级PID控制器类（移植自丢失追踪.py）
class AdvancedPIDController:
    def __init__(self, Kp, Ki, Kd, error_threshold=8, integral_limit=80, min_output=3, max_output=40):
        """
        高级PID控制器，支持积分分离和积分限幅

        Args:
            Kp: 比例系数
            Ki: 积分系数
            Kd: 微分系数
            error_threshold: 积分分离阈值，小于此值才进行积分
            integral_limit: 积分限幅值
            min_output: 最小输出（死区补偿）
            max_output: 最大输出限制
        """
        self.Kp = Kp
        self.Ki = Ki
        self.Kd = Kd
        self.error_threshold = error_threshold  # 积分分离阈值
        self.integral_limit = integral_limit    # 积分限幅
        self.min_output = min_output           # 最小输出
        self.max_output = max_output           # 最大输出
        self.last_error = 0
        self.integral = 0
        self.last_output = 0

    def compute(self, error):
        """
        计算PID输出

        特点：
        1. 积分分离：只有误差小于阈值时才积分，防止积分饱和
        2. 积分限幅：限制积分项的最大值
        3. 输出变化率限制：防止输出突变
        4. 死区补偿：确保最小输出
        """
        # 调试：打印PID状态
        if DEBUG and abs(error) > 1:  # 只在有明显误差时打印
            print(f"🔧 PID计算前 - 误差:{error:.2f}, 积分:{self.integral:.3f}, 上次误差:{self.last_error:.3f}")

        # 微分项
        derivative = error - self.last_error
        self.last_error = error

        # 积分分离：小误差才积分，防止大误差时积分饱和
        if abs(error) > self.error_threshold:
            # 大误差时不积分，避免积分饱和
            pass
        else:
            # 小误差时进行积分，消除稳态误差
            self.integral += error
            # 积分限幅，防止积分项过大
            self.integral = max(min(self.integral, self.integral_limit), -self.integral_limit)

        # PID计算
        output = self.Kp * error + self.Ki * self.integral + self.Kd * derivative

        # 限制输出变化率，防止舵机运动过于剧烈
        output = limit_step(output, self.last_output, max_step=pid_max_step)
        self.last_output = output

        # 输出限幅和死区补偿
        if output > 0:
            output = max(min(output, self.max_output), self.min_output)
        elif output < 0:
            output = min(max(output, -self.max_output), -self.min_output)
        else:
            output = 0

        return output

# 激光控制器类
class LaserController:
    def __init__(self, gpio_pin="A29"):
        """
        初始化激光控制器

        Args:
            gpio_pin: GPIO引脚名称，如"A29"
        """
        self.gpio_pin = gpio_pin
        self.laser_gpio = None
        self.last_fire_time = 0
        self.is_firing = False
        self.fire_start_time = 0
        self.is_force_firing = False  # 是否正在强制发射
        self.force_duration = 0.5     # 强制发射持续时间

        # 初始化GPIO
        self.init_gpio()

    def init_gpio(self):
        """初始化GPIO引脚"""
        try:
            # 先设置引脚映射
            print(f"🔧 设置引脚映射: {self.gpio_pin}")
            pinmap.set_pin_function(self.gpio_pin, f"GPIO{self.gpio_pin}")

            # 初始化GPIO（使用与A28相同的方式）
            gpio_name = f"GPIO{self.gpio_pin}"
            print(f"🔧 创建GPIO对象: {gpio_name}")
            self.laser_gpio = gpio.GPIO(gpio_name, gpio.Mode.OUT)

            # 设置初始状态并验证
            self.laser_gpio.value(1)  # 初始状态为高电平（关闭继电器）
            actual_value = self.laser_gpio.value()
            print(f"🔍 GPIO初始化后状态: {actual_value} (期望: 1)")

            if actual_value != 1:
                print(f"⚠️  警告：GPIO初始状态设置可能失败")

            print(f"✅ 激光控制GPIO初始化成功: {gpio_name} (低电平触发)")
            return True
        except Exception as e:
            print(f"❌ 激光控制GPIO初始化失败: {e}")
            import traceback
            traceback.print_exc()
            self.laser_gpio = None
            return False

    def can_fire(self):
        """检查是否可以发射激光"""
        if self.laser_gpio is None:
            return False

        current_time = time.time()

        # 如果正在发射，检查是否需要关闭
        if self.is_firing:
            if current_time - self.fire_start_time >= laser_duration:
                self.stop_laser()
            return False

        # 检查冷却时间
        if current_time - self.last_fire_time >= laser_cooldown:
            return True

        return False

    def fire_laser(self):
        """发射激光（正常发射，需要检查冷却时间）"""
        # 如果正在强制发射，拒绝正常发射
        if self.is_force_firing:
            if DEBUG:
                print("🚫 拒绝正常激光发射：正在强制发射中")
            return False

        if not self.can_fire():
            return False

        try:
            # 添加正常发射的详细调试信息
            print(f"🔍 正常发射调试 - GPIO对象: {self.laser_gpio}")
            print(f"🔍 正常发射调试 - GPIO引脚: {self.gpio_pin}")

            before_value = self.laser_gpio.value()
            print(f"🔍 正常发射前GPIO状态: {before_value}")

            self.laser_gpio.value(0)  # 低电平触发继电器

            after_value = self.laser_gpio.value()
            print(f"🔍 正常发射后GPIO状态: {after_value}")

            self.is_firing = True
            self.fire_start_time = time.time()
            print(f"🔴 激光发射！持续时间: {laser_duration}s")
            print(f"🔍 正常发射开始时间: {self.fire_start_time}")

            return True
        except Exception as e:
            print(f"❌ 激光发射失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def force_fire_laser(self, duration=0.5):
        """强制发射激光（绕过所有检查）"""
        print(f"🔍 强制发射调试 - GPIO对象: {self.laser_gpio}")
        print(f"🔍 强制发射调试 - GPIO引脚: {self.gpio_pin}")

        if self.laser_gpio is None:
            print("❌ 激光GPIO未初始化，无法强制发射")
            return False

        try:
            # 检查当前GPIO状态
            current_value = self.laser_gpio.value()
            print(f"🔍 强制发射前GPIO状态: {current_value} (1=关闭, 0=打开)")

            # 如果正在发射，先停止
            if self.is_firing:
                print("🔄 检测到激光正在发射，先停止")
                self.stop_laser()
                time.sleep(0.1)  # 短暂延时确保停止
                current_value = self.laser_gpio.value()
                print(f"🔍 停止后GPIO状态: {current_value}")

            # 强制发射 - 多次尝试设置GPIO
            print("🚨 正在设置GPIO为低电平...")

            # 尝试多次设置GPIO
            for attempt in range(3):
                self.laser_gpio.value(0)  # 低电平触发继电器
                time.sleep(0.01)  # 短暂延时
                actual_value = self.laser_gpio.value()
                print(f"🔍 尝试{attempt+1}: 设置后GPIO实际状态: {actual_value}")

                if actual_value == 0:
                    print("✅ GPIO设置成功！")
                    break
                else:
                    print(f"❌ 尝试{attempt+1}失败，重试...")
                    if attempt == 2:  # 最后一次尝试
                        print("❌ 所有尝试都失败，可能是硬件问题")
                        # 尝试重新初始化GPIO
                        print("🔧 尝试重新初始化GPIO...")
                        try:
                            pinmap.set_pin_function(self.gpio_pin, f"GPIO{self.gpio_pin}")
                            gpio_name = f"GPIO{self.gpio_pin}"
                            self.laser_gpio = gpio.GPIO(gpio_name, gpio.Mode.OUT)
                            self.laser_gpio.value(0)
                            final_check = self.laser_gpio.value()
                            print(f"🔍 重新初始化后GPIO状态: {final_check}")
                            if final_check != 0:
                                print("❌ 重新初始化也失败，可能是硬件连接问题")
                                return False
                        except Exception as reinit_e:
                            print(f"❌ 重新初始化失败: {reinit_e}")
                            return False

            self.is_firing = True
            self.fire_start_time = time.time()

            # 设置强制发射标志（必须在设置is_firing之后立即设置）
            self.force_duration = duration
            self.is_force_firing = True

            print(f"🚨 强制激光发射！持续时间: {duration}s (绕过冷却检查)")
            print(f"🔍 发射开始时间: {self.fire_start_time}")
            print(f"🔍 强制发射标志: is_force_firing = {self.is_force_firing}")

            # 再次确认GPIO状态
            final_value = self.laser_gpio.value()
            print(f"🔍 最终GPIO状态确认: {final_value} (应该是0)")

            return True
        except Exception as e:
            print(f"❌ 强制激光发射失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def stop_laser(self):
        """停止激光"""
        if self.laser_gpio is None:
            print("❌ 停止激光失败：GPIO未初始化")
            return

        try:
            # 检查停止前的GPIO状态
            before_value = self.laser_gpio.value()
            print(f"🔍 停止前GPIO状态: {before_value}")

            self.laser_gpio.value(1)  # 高电平关闭继电器

            # 检查停止后的GPIO状态
            after_value = self.laser_gpio.value()
            print(f"🔍 停止后GPIO状态: {after_value}")

            was_force_firing = self.is_force_firing
            self.is_firing = False
            self.is_force_firing = False  # 重置强制发射标志
            self.last_fire_time = time.time()

            if was_force_firing:
                print(f"⚫ 强制激光关闭，冷却时间: {laser_cooldown}s")
            else:
                print(f"⚫ 激光关闭，冷却时间: {laser_cooldown}s")

            if after_value != 1:
                print("❌ 警告：GPIO关闭失败，实际值不是1！")

        except Exception as e:
            print(f"❌ 激光关闭失败: {e}")
            import traceback
            traceback.print_exc()

    def update(self):
        """更新激光状态（在主循环中调用）"""
        if self.is_firing:
            current_time = time.time()
            elapsed = current_time - self.fire_start_time

            # 根据是否是强制发射使用不同的持续时间
            duration = self.force_duration if self.is_force_firing else laser_duration

            # 强制发射时的详细调试
            if self.is_force_firing:
                gpio_value = self.laser_gpio.value() if self.laser_gpio else "N/A"
                print(f"🔍 强制发射状态 - 已持续: {elapsed:.2f}s/{duration}s, GPIO: {gpio_value}")

            if elapsed >= duration:
                if self.is_force_firing:
                    print(f"⚫ 强制激光发射完成，持续了 {elapsed:.2f}s")
                    self.is_force_firing = False
                self.stop_laser()

    def get_status(self):
        """获取激光状态信息"""
        if self.laser_gpio is None:
            return "GPIO_ERROR"

        current_time = time.time()

        if self.is_firing:
            duration = self.force_duration if self.is_force_firing else laser_duration
            remaining = duration - (current_time - self.fire_start_time)
            if self.is_force_firing:
                return f"FORCE_FIRING({remaining:.1f}s)"
            else:
                return f"FIRING({remaining:.1f}s)"

        cooldown_remaining = laser_cooldown - (current_time - self.last_fire_time)
        if cooldown_remaining > 0:
            return f"COOLDOWN({cooldown_remaining:.1f}s)"

        return "READY"

    def test_gpio(self):
        """测试GPIO控制（调试用）"""
        print("🧪 开始完整GPIO测试...")

        try:
            # 测试1：检查当前GPIO状态
            if self.laser_gpio is None:
                print("❌ GPIO未初始化，尝试重新初始化...")
                self.init_gpio()
                if self.laser_gpio is None:
                    print("❌ 重新初始化失败")
                    return

            print(f"🔍 当前GPIO对象: {self.laser_gpio}")
            print(f"🔍 GPIO引脚: {self.gpio_pin}")

            # 测试2：多次设置和读取
            for i in range(3):
                print(f"\n--- 测试轮次 {i+1} ---")

                # 设置高电平
                print("🔧 设置高电平(1)...")
                self.laser_gpio.value(1)
                time.sleep(0.1)
                value1 = self.laser_gpio.value()
                print(f"🔍 读取结果: {value1} {'✅' if value1 == 1 else '❌'}")

                time.sleep(0.5)

                # 设置低电平
                print("🔧 设置低电平(0)...")
                self.laser_gpio.value(0)
                time.sleep(0.1)
                value0 = self.laser_gpio.value()
                print(f"🔍 读取结果: {value0} {'✅' if value0 == 0 else '❌'}")

                if value0 == 0:
                    print("🎉 激光应该在这1秒内点亮！")
                    time.sleep(1.0)  # 保持1秒让用户观察
                else:
                    print("❌ GPIO设置失败，激光不会点亮")
                    time.sleep(0.5)

                # 恢复高电平
                print("🔧 恢复高电平(1)...")
                self.laser_gpio.value(1)
                time.sleep(0.1)
                value_final = self.laser_gpio.value()
                print(f"🔍 最终状态: {value_final} {'✅' if value_final == 1 else '❌'}")

            print("\n✅ GPIO测试完成")

        except Exception as e:
            print(f"❌ GPIO测试失败: {e}")
            import traceback
            traceback.print_exc()

    def force_hardware_test(self):
        """强制硬件测试 - 绕过所有软件逻辑"""
        print("🚨 开始强制硬件测试...")
        try:
            # 重新初始化GPIO
            pinmap.set_pin_function(self.gpio_pin, f"GPIO{self.gpio_pin}")
            gpio_name = f"GPIO{self.gpio_pin}"
            test_gpio = gpio.GPIO(gpio_name, gpio.Mode.OUT)

            print("🔧 直接控制GPIO...")
            test_gpio.value(0)  # 激光应该亮
            print("🔴 激光应该现在点亮了！(持续3秒)")
            time.sleep(3.0)

            test_gpio.value(1)  # 激光应该灭
            print("⚫ 激光应该现在熄灭了！")

            print("✅ 强制硬件测试完成")

        except Exception as e:
            print(f"❌ 强制硬件测试失败: {e}")
            import traceback
            traceback.print_exc()

    def compare_gpio_objects(self):
        """比较不同方式创建的GPIO对象"""
        print("🔍 开始GPIO对象比较测试...")

        try:
            # 方法1：使用当前的GPIO对象
            print(f"方法1 - 当前GPIO对象: {self.laser_gpio}")
            print(f"当前GPIO对象ID: {id(self.laser_gpio)}")

            if self.laser_gpio:
                print("测试当前GPIO对象...")
                self.laser_gpio.value(0)
                time.sleep(0.1)
                value1 = self.laser_gpio.value()
                print(f"当前GPIO设置0后读取: {value1}")
                time.sleep(1.0)
                self.laser_gpio.value(1)
                value2 = self.laser_gpio.value()
                print(f"当前GPIO设置1后读取: {value2}")

            # 方法2：重新创建GPIO对象（模拟正常发射的方式）
            print("\n方法2 - 重新创建GPIO对象...")
            pinmap.set_pin_function(self.gpio_pin, f"GPIO{self.gpio_pin}")
            gpio_name = f"GPIO{self.gpio_pin}"
            new_gpio = gpio.GPIO(gpio_name, gpio.Mode.OUT)
            print(f"新GPIO对象: {new_gpio}")
            print(f"新GPIO对象ID: {id(new_gpio)}")

            print("测试新GPIO对象...")
            new_gpio.value(0)
            time.sleep(0.1)
            value3 = new_gpio.value()
            print(f"新GPIO设置0后读取: {value3}")
            time.sleep(1.0)
            new_gpio.value(1)
            value4 = new_gpio.value()
            print(f"新GPIO设置1后读取: {value4}")

            # 方法3：直接使用字符串引脚名
            print("\n方法3 - 直接使用字符串引脚名...")
            pinmap.set_pin_function("A29", "GPIOA29")
            direct_gpio = gpio.GPIO("GPIOA29", gpio.Mode.OUT)
            print(f"直接GPIO对象: {direct_gpio}")
            print(f"直接GPIO对象ID: {id(direct_gpio)}")

            print("测试直接GPIO对象...")
            direct_gpio.value(0)
            time.sleep(0.1)
            value5 = direct_gpio.value()
            print(f"直接GPIO设置0后读取: {value5}")
            time.sleep(1.0)
            direct_gpio.value(1)
            value6 = direct_gpio.value()
            print(f"直接GPIO设置1后读取: {value6}")

            print("✅ GPIO对象比较测试完成")

        except Exception as e:
            print(f"❌ GPIO对象比较测试失败: {e}")
            import traceback
            traceback.print_exc()

    def close(self):
        """关闭激光控制器"""
        if self.laser_gpio:
            try:
                self.laser_gpio.value(1)  # 确保关闭（高电平）
                print("✅ 激光控制器已关闭")
            except Exception as e:
                print(f"⚠️  关闭激光控制器时出错: {e}")

# A28控制器类
class A28Controller:
    def __init__(self, gpio_pin="A28"):
        """
        初始化A28控制器

        Args:
            gpio_pin: GPIO引脚名称，如"A28"
        """
        self.gpio_pin = gpio_pin
        self.a28_gpio = None
        self.last_fire_time = 0
        self.is_firing = False
        self.fire_start_time = 0

        # 初始化GPIO
        self.init_gpio()

    def init_gpio(self):
        """初始化GPIO引脚"""
        try:
            # 设置引脚功能
            pinmap.set_pin_function(self.gpio_pin, f"GPIO{self.gpio_pin}")
            self.a28_gpio = gpio.GPIO(f"GPIO{self.gpio_pin}", gpio.Mode.OUT)
            self.a28_gpio.value(1)  # 初始状态为高电平（关闭）
            print(f"✅ A28控制GPIO初始化成功: {self.gpio_pin} (低电平打开)")
            return True
        except Exception as e:
            print(f"❌ A28控制GPIO初始化失败: {e}")
            self.a28_gpio = None
            return False

    def can_fire(self):
        """检查是否可以触发A28"""
        if self.a28_gpio is None:
            return False

        current_time = time.time()

        # 如果正在触发，检查是否需要关闭
        if self.is_firing:
            if current_time - self.fire_start_time >= a28_duration:
                self.stop_a28()
            return False

        # 检查冷却时间
        if current_time - self.last_fire_time >= a28_cooldown:
            return True

        return False

    def fire_a28(self):
        """触发A28"""
        if not self.can_fire():
            return False

        try:
            self.a28_gpio.value(0)  # 低电平打开
            self.is_firing = True
            self.fire_start_time = time.time()
            print(f"🟢 A28触发！持续时间: {a28_duration}s")
            return True
        except Exception as e:
            print(f"❌ A28触发失败: {e}")
            return False

    def stop_a28(self):
        """停止A28"""
        if self.a28_gpio is None:
            return

        try:
            self.a28_gpio.value(1)  # 高电平关闭
            self.is_firing = False
            self.last_fire_time = time.time()
            print(f"🔴 A28关闭，冷却时间: {a28_cooldown}s")
        except Exception as e:
            print(f"❌ A28关闭失败: {e}")

    def update(self):
        """更新A28状态（在主循环中调用）"""
        if self.is_firing:
            current_time = time.time()
            if current_time - self.fire_start_time >= a28_duration:
                self.stop_a28()

    def get_status(self):
        """获取A28状态信息"""
        if self.a28_gpio is None:
            return "GPIO_ERROR"

        current_time = time.time()

        if self.is_firing:
            remaining = a28_duration - (current_time - self.fire_start_time)
            return f"FIRING({remaining:.1f}s)"

        cooldown_remaining = a28_cooldown - (current_time - self.last_fire_time)
        if cooldown_remaining > 0:
            return f"COOLDOWN({cooldown_remaining:.1f}s)"

        return "READY"

    def close(self):
        """关闭A28控制器"""
        if self.a28_gpio:
            try:
                self.a28_gpio.value(1)  # 确保关闭（高电平）
                print("✅ A28控制器已关闭")
            except Exception as e:
                print(f"⚠️  关闭A28控制器时出错: {e}")

# 舵机控制器类
class ServoController:
    def __init__(self, uart_device="/dev/ttyS0", baudrate=115200):
        self.uart_device = uart_device
        self.baudrate = baudrate
        self.uart_servo = None

        # 初始化舵机位置
        self.pan_position = servo_center[0]    # PWM值
        self.tilt_position = servo_center[1]   # PWM值

        # 稳定性跟踪
        self.stable_frame_count = 0
        self.last_errors = []  # 记录最近几帧的误差
        self.is_stable = False

        # 激光控制器
        self.laser_controller = None
        if enable_laser_control:
            self.laser_controller = LaserController(laser_gpio_pin)

        # A28控制器
        self.a28_controller = None
        if enable_a28_control:
            self.a28_controller = A28Controller(a28_gpio_pin)

        # 初始化串口
        self.init_uart()

        # 舵机协议支持
        print(f"🔧 使用PWM舵机协议")
        print(f"📐 PWM范围: Pan({servo_pan_range[0]}~{servo_pan_range[1]}), Tilt({servo_tilt_range[0]}~{servo_tilt_range[1]})")
        print(f"📍 中心位置: Pan={servo_center[0]}, Tilt={servo_center[1]}")
        print(f"⚡ 速度设置: {servo_speed} (1-100范围)")

        # 初始化高级PID控制器（移植自丢失追踪.py）
        self.pid_pan = AdvancedPIDController(
            Kp=pid_kp, Ki=pid_ki, Kd=pid_kd,
            error_threshold=pid_error_threshold,  # 积分分离阈值
            integral_limit=pid_integral_limit,    # 积分限幅
            min_output=pid_min_output,           # 最小输出（死区补偿）
            max_output=pid_max_output            # 最大输出
        )
        self.pid_tilt = AdvancedPIDController(
            Kp=pid_kp, Ki=pid_ki, Kd=pid_kd,
            error_threshold=pid_error_threshold,  # 积分分离阈值
            integral_limit=pid_integral_limit,    # 积分限幅
            min_output=pid_min_output,           # 最小输出（死区补偿）
            max_output=pid_max_output            # 最大输出
        )

    def init_uart(self):
        """初始化UART串口"""
        try:
            self.uart_servo = uart.UART(self.uart_device, self.baudrate)
            print(f"✅ 舵机串口初始化成功: {self.uart_device} @ {self.baudrate}")
            return True
        except Exception as e:
            print(f"❌ 舵机串口初始化失败: {e}")
            self.uart_servo = None
            return False



    def send_servo_command(self, pan_val, tilt_val, speed=servo_speed):
        """发送舵机控制命令"""
        if self.uart_servo is None:
            return False

        # 限制舵机值在有效范围内
        pan_val = max(min(pan_val, servo_pan_range[1]), servo_pan_range[0])
        tilt_val = max(min(tilt_val, servo_tilt_range[1]), servo_tilt_range[0])

        # 构造总线舵机命令格式
        cmd = f"#000P{pan_val}T{speed:03d}!#001P{tilt_val}T{speed:03d}!"

        try:
            self.uart_servo.write(cmd.encode())
            self.pan_position = pan_val
            self.tilt_position = tilt_val
            if DEBUG:
                print(f"舵机命令: {cmd}")
            return True
        except Exception as e:
            print(f"❌ 舵机命令发送失败: {e}")
            return False

    def update_servo_by_error(self, err_x, err_y):
        """
        根据误差更新舵机位置 - 防振荡优化版

        特点：
        1. 稳定性检测：连续稳定时停止调整
        2. 智能死区：根据稳定状态动态调整
        3. 防振荡：多重机制防止来回摆动
        4. 渐进式控制：接近目标时更加谨慎
        """
        # 计算总误差
        total_error = (err_x**2 + err_y**2)**0.5

        # 更新误差历史
        self.last_errors.append(total_error)
        if len(self.last_errors) > stability_check_frames:
            self.last_errors.pop(0)

        # 检查稳定性
        if len(self.last_errors) >= stability_check_frames:
            max_recent_error = max(self.last_errors)
            if max_recent_error < max_stable_error:
                self.stable_frame_count += 1
                self.is_stable = True
            else:
                self.stable_frame_count = 0
                self.is_stable = False

        # 如果已经稳定，使用更大的死区
        current_dead_zone = error_dead_zone
        if self.is_stable:
            current_dead_zone = error_dead_zone * 1.5  # 稳定时死区增大50%

        # 死区判断：当误差很小时，舵机不动作
        if abs(err_x) < current_dead_zone and abs(err_y) < current_dead_zone:
            # 检查是否可以触发激光（瞄准靶心）
            # 注意：如果正在强制发射，则不触发正常激光
            if (self.laser_controller and
                abs(err_x) < laser_trigger_threshold and
                abs(err_y) < laser_trigger_threshold and
                self.laser_controller.can_fire() and
                not self.laser_controller.is_force_firing):  # 新增：强制发射时不触发正常激光

                self.laser_controller.fire_laser()
                if DEBUG:
                    print(f"🎯 瞄准成功！激光发射: X={err_x:+.1f}, Y={err_y:+.1f}")

            # 检查是否可以触发A28（瞄准靶心）
            if (self.a28_controller and
                abs(err_x) < a28_trigger_threshold and
                abs(err_y) < a28_trigger_threshold and
                self.a28_controller.can_fire()):

                self.a28_controller.fire_a28()
                if DEBUG:
                    print(f"🎯 瞄准成功！A28触发: X={err_x:+.1f}, Y={err_y:+.1f}")

            if DEBUG:
                status = "稳定" if self.is_stable else "普通"
                print(f"💤 误差在死区内: X={err_x:+.1f}, Y={err_y:+.1f} (死区±{current_dead_zone:.1f}, {status})")
            return False  # 误差太小，舵机不动

        # 如果刚刚稳定，给一个短暂的延迟
        if self.is_stable and self.stable_frame_count < stability_check_frames + 2:
            if DEBUG:
                print(f"⏳ 稳定延迟中: {self.stable_frame_count}/{stability_check_frames + 2}")
            return False

        moved = False

        # 简化控制策略：统一使用PID控制
        # 水平方向控制
        if abs(err_x) > center_threshold:
            pan_adjustment = self.pid_pan.compute(err_x)
            new_pan = self.pan_position + int(pan_adjustment)
            self.pan_position = new_pan
            moved = True

        # 垂直方向控制
        if abs(err_y) > center_threshold:
            tilt_adjustment = self.pid_tilt.compute(-err_y)  # Y轴方向相反
            new_tilt = self.tilt_position + int(tilt_adjustment)
            self.tilt_position = new_tilt
            moved = True

        # 发送舵机命令
        if moved:
            # 重置稳定计数
            self.stable_frame_count = 0
            self.is_stable = False

            if DEBUG:
                print(f"🎯 舵机动作: X={err_x:+.1f}, Y={err_y:+.1f}, 总误差={total_error:.1f}")
            return self.send_servo_command(self.pan_position, self.tilt_position)

        return False

    def center_servos(self):
        """舵机回到中心位置"""
        return self.send_servo_command(servo_center[0], servo_center[1])



    def close(self):
        """关闭串口连接和激光控制器"""
        if self.uart_servo:
            try:
                self.center_servos()
                time.sleep_ms(100)
                self.uart_servo.close()
                print("✅ 舵机串口已关闭")
            except Exception as e:
                print(f"⚠️  关闭串口时出错: {e}")
            finally:
                self.uart_servo = None

        # 关闭激光控制器
        if self.laser_controller:
            self.laser_controller.close()
            self.laser_controller = None

        # 关闭A28控制器
        if self.a28_controller:
            self.a28_controller.close()
            self.a28_controller = None

    def test_servo_speed(self):
        """测试舵机T参数的真实含义"""
        if self.uart_servo is None:
            print("❌ 舵机UART未初始化")
            return

        print("🧪 开始舵机T参数测试...")
        print("📐 测试方案：从1500移动到1800，测试不同T值的实际用时")

        # 测试不同的T值
        test_values = [10, 20, 50, 100, 200, 500, 1000]

        for t_val in test_values:
            print(f"\n🔍 测试 T{t_val:03d}...")

            # 先移动到起始位置
            cmd_start = f"#000P1500T100!#001P{self.tilt_position}T100!"
            self.uart_servo.write(cmd_start.encode())
            time.sleep(1.0)  # 等待到达起始位置

            # 记录开始时间
            start_time = time.ticks_ms()

            # 发送测试命令
            cmd_test = f"#000P1800T{t_val:03d}!#001P{self.tilt_position}T{t_val:03d}!"
            self.uart_servo.write(cmd_test.encode())
            print(f"📤 发送命令: {cmd_test}")

            # 等待足够长的时间确保舵机完成动作
            time.sleep(2.0)

            # 计算实际用时（这里只是估算，实际需要位置反馈）
            actual_time = time.ticks_ms() - start_time
            print(f"⏱️  T{t_val:03d} 发送后等待时间: {actual_time}ms")

        print("✅ 舵机T参数测试完成")
        print("💡 观察舵机实际移动速度来判断T参数含义")

if not os.path.exists(model_path):
    model_path1 = "model/model_3356.mud"
    if not os.path.exists(model_path1):
        print(f"load model failed, please put model in {model_path}, or {os.path.getcwd()}/{model_path1}")
    model_path = model_path1

# 初始化AI检测器
detector = nn.YOLOv5(model=model_path, dual_buff = True)

# 初始化UART0接收器（独立于舵机控制）
uart0_receiver = None
try:
    uart0_receiver = UART0Receiver(servo_uart_device, servo_baudrate)
    if uart0_receiver.uart_rx:
        print("📡 UART0接收器已启用，等待数据...")
    else:
        print("⚠️  UART0接收器初始化失败")
        uart0_receiver = None
except Exception as e:
    print(f"❌ UART0接收器创建失败: {e}")
    uart0_receiver = None

# 初始化舵机控制器
servo_controller = None
if enable_servo_control:
    servo_controller = ServoController(servo_uart_device, servo_baudrate)
    if servo_controller.uart_servo:
        print("🎯 舵机控制已启用")
        servo_controller.center_servos()  # 舵机回中心位置
    else:
        print("⚠️  舵机控制初始化失败，将以仅检测模式运行")
        servo_controller = None

# 初始化摄像头
if hires_mode:
    cam = camera.Camera(high_res, high_res, detector.input_format(), buff_num=cam_buff_num)
else:
    cam = camera.Camera(detector.input_width(), detector.input_height(), detector.input_format(), buff_num=cam_buff_num)
if not auto_awb:
    cam.awb_mode(camera.AwbMode.Manual)
    cam.set_wb_gain(awb_gain)
cam.constrast(contrast)
# cam.set_windowing([448, 448])

def find_laser_point(img, original_img):
    '''
        随便写的，有需要请自己修改算法
    '''

    # 这里需要调阈值
    ths = [[0, 100, -128, 127, -128, -18]]
    blobs = img_std.find_blobs(ths, x_stride=2, y_stride=2)
    max_s = 0
    max_b = None
    for b in blobs:
        s = b.w() * b.h()
        if s > max_s:
            max_s = s
            max_b = b
    if DEBUG:
        laser_binary = img.binary(ths, copy=True)
        original_img.draw_image(original_img.width() - laser_binary.width(), original_img.height() - laser_binary.height(), laser_binary)
    return max_b

_t = time.ticks_ms()
def debug_time(msg):
    if PRINT_TIME:
        global _t
        print(f"t: {time.ticks_ms() - _t:4d} {msg}")
        _t = time.ticks_ms()

# 状态切换清理函数
def perform_state_cleanup():
    """执行状态切换清理操作"""
    global state_change_cleanup_needed, cleanup_old_state, cleanup_new_state

    print(f"🧹 执行状态切换清理: {cleanup_old_state} → {cleanup_new_state}")
    print(f"🔍 舵机控制器状态: {servo_controller is not None}")

    # 1. 清空舵机PID积分量
    if servo_controller:
        print(f"🔍 舵机控制器类型: {type(servo_controller)}")

        # 强制重新初始化PID控制器
        try:
            if hasattr(servo_controller, 'pid_pan') and servo_controller.pid_pan:
                old_pan_integral = servo_controller.pid_pan.integral
                old_pan_last_error = servo_controller.pid_pan.last_error
                old_pan_last_output = servo_controller.pid_pan.last_output

                # 完全重置PID控制器
                servo_controller.pid_pan.integral = 0
                servo_controller.pid_pan.last_error = 0
                servo_controller.pid_pan.last_output = 0

                print(f"🔄 清空Pan轴PID - 积分: {old_pan_integral:.3f}→0, 误差: {old_pan_last_error:.3f}→0, 输出: {old_pan_last_output:.3f}→0")
            else:
                print("⚠️  Pan轴PID控制器不存在")

            if hasattr(servo_controller, 'pid_tilt') and servo_controller.pid_tilt:
                old_tilt_integral = servo_controller.pid_tilt.integral
                old_tilt_last_error = servo_controller.pid_tilt.last_error
                old_tilt_last_output = servo_controller.pid_tilt.last_output

                # 完全重置PID控制器
                servo_controller.pid_tilt.integral = 0
                servo_controller.pid_tilt.last_error = 0
                servo_controller.pid_tilt.last_output = 0

                print(f"🔄 清空Tilt轴PID - 积分: {old_tilt_integral:.3f}→0, 误差: {old_tilt_last_error:.3f}→0, 输出: {old_tilt_last_output:.3f}→0")
            else:
                print("⚠️  Tilt轴PID控制器不存在")

        except Exception as e:
            print(f"❌ PID清理出错: {e}")

        # 2. 强制重置舵机位置到中心
        try:
            if hasattr(servo_controller, 'pan_position') and hasattr(servo_controller, 'tilt_position'):
                old_pan_pos = servo_controller.pan_position
                old_tilt_pos = servo_controller.tilt_position

                servo_controller.pan_position = servo_center[0]
                servo_controller.tilt_position = servo_center[1]

                print(f"🔄 重置舵机位置 - Pan: {old_pan_pos}→{servo_center[0]}, Tilt: {old_tilt_pos}→{servo_center[1]}")

                # 发送舵机回中心命令
                servo_controller.center_servos()
                print("🎯 发送舵机回中心命令")
            else:
                print("⚠️  舵机位置属性不存在")
        except Exception as e:
            print(f"❌ 舵机位置重置出错: {e}")

        # 3. 关闭激光笔
        try:
            if hasattr(servo_controller, 'laser_controller') and servo_controller.laser_controller:
                if servo_controller.laser_controller.is_firing:
                    servo_controller.laser_controller.stop_laser()
                    print("🔴 强制关闭激光笔")
                else:
                    print("⚫ 激光笔已关闭")
            else:
                print("⚠️  激光控制器不存在")
        except Exception as e:
            print(f"❌ 激光控制出错: {e}")

        # 4. 关闭A28设备（现在A28功能已集成到激光控制器中）
        try:
            if hasattr(servo_controller, 'a28_controller') and servo_controller.a28_controller:
                if servo_controller.a28_controller.is_firing:
                    servo_controller.a28_controller.stop_a28()
                    print("🔴 强制关闭A28设备")
                else:
                    print("⚫ A28设备已关闭")
            else:
                print("ℹ️  A28控制器已集成到激光控制器中")
        except Exception as e:
            print(f"❌ A28控制出错: {e}")

        # 5. 重置舵机稳定性状态
        try:
            if hasattr(servo_controller, 'stable_frame_count'):
                servo_controller.stable_frame_count = 0
            if hasattr(servo_controller, 'is_stable'):
                servo_controller.is_stable = False
            if hasattr(servo_controller, 'last_errors'):
                servo_controller.last_errors.clear()
            # 重置状态1初始化标志，确保下次进入状态1时重新初始化
            if hasattr(servo_controller, '_state1_initialized'):
                servo_controller._state1_initialized = False
            # 重置状态2初始化标志
            if hasattr(servo_controller, '_state2_initialized'):
                servo_controller._state2_initialized = False
                print("🔄 重置状态2初始化标志: True → False")
            # 重置状态3初始化标志
            if hasattr(servo_controller, '_state3_initialized'):
                servo_controller._state3_initialized = False
                print("🔄 重置状态3初始化标志: True → False")
            # 重置状态4初始化标志
            if hasattr(servo_controller, '_state4_initialized'):
                servo_controller._state4_initialized = False
                print("🔄 重置状态4初始化标志: True → False")
            print("🔄 重置舵机稳定性状态和初始化标志")
        except Exception as e:
            print(f"❌ 稳定性状态重置出错: {e}")

        # 6. 停止状态2强制激光定时器
        try:
            global force_laser_timer
            if force_laser_timer.timer_active or force_laser_timer.is_force_firing:
                force_laser_timer.stop_timer()
                print("🔄 停止状态2强制激光定时器")
        except Exception as e:
            print(f"❌ 状态2定时器停止出错: {e}")

        # 7. 停止状态3扫描控制器
        try:
            global scan_controller
            if scan_controller.scanning:
                scan_controller.stop_scan()
                print("🔄 停止状态3扫描控制器")
        except Exception as e:
            print(f"❌ 状态3扫描控制器停止出错: {e}")

        # 8. 停止状态4扫描控制器
        try:
            global scan4_controller
            if scan4_controller.scanning:
                scan4_controller.stop_scan()
                print("🔄 停止状态4扫描控制器")
        except Exception as e:
            print(f"❌ 状态4扫描控制器停止出错: {e}")

    else:
        print("⚠️  舵机控制器未初始化，跳过PID清理")

    # 重置清理标志
    state_change_cleanup_needed = False
    print(f"✅ 状态切换清理完成: 当前状态 = {cleanup_new_state}")
    print("=" * 50)

err_center = [0, 0] # 距离中心的误差
center_pos = [cam.width() // 2, cam.height() // 2] # 画面的中心
last_center = center_pos # 上一次检测到的圆心距离
last_center_small = [detector.input_width(), detector.input_height()] # 高清模式时，在小图的中心坐标
servo_moved = False  # 舵机是否移动标志
while not app.need_exit():
    # 检查是否需要执行状态切换清理
    if state_change_cleanup_needed:
        perform_state_cleanup()

    debug_time("start")
    img = cam.read()
    debug_time("cam read")
    # AI 检测外框
    if hires_mode:
        img_ai = img.resize(detector.input_width(), detector.input_height())
    else:
        img_ai = img # new copy
    debug_time("resize")
    objs = detector.detect(img_ai, conf_th = 0.5, iou_th = 0.45)
    max_idx = -1
    max_s = 0
    for i, obj in enumerate(objs):
        s = obj.w * obj.h
        if s > max_s:
            max_s = s
            max_idx = i
        # img_ai.draw_rect(obj.x, obj.y, obj.w, obj.h, color = image.COLOR_RED, thickness=4)
        # msg = f'{detector.labels[obj.class_id]}: {obj.score:.2f}'
        # img_ai.draw_string(obj.x, obj.y, msg, color = image.COLOR_RED, scale=2)
    debug_time("detect")
    if max_idx >= 0:
        obj = objs[max_idx]
        w = obj.w + crop_padding * 2
        h = obj.h + crop_padding * 2
        w = w + 1 if w % 2 != 0 else w
        h = h + 1 if h % 2 != 0 else h
        x = obj.x - crop_padding
        y = obj.y - crop_padding
        if x < 0:
            w += x
            x = 0
        if y < 0:
            h += y
            y = 0
        if x + w > img_ai.width():
            w = img_ai.width() - x
        if y + h > img_ai.height():
            h = img_ai.height() - y
        crop_ai = img_ai.crop(x, y, w, h)
        crop_ai_rect = [x, y, w, h]
        # 算出裁切范围对应在大图的位置
        # 注意这里只考虑到了拉伸缩放(iamge.Fit.FILL)
        img_ai_scale = [img.width() / img_ai.width(), img.height() / img_ai.height()]
        # crop_rect = image.resize_map_pos_reverse(img.width(), img.height(), img_ai.width(), img_ai.height(), image.Fit.FIT_FILL, obj.x, obj.y, obj.w, obj.h)
        crop_rect = [int(obj.x * img_ai_scale[0]), int(obj.y * img_ai_scale[1]), int(obj.w * img_ai_scale[0]), int(h * img_ai_scale[0])]
        img_cv = image.image2cv(img, False, False)
        crop_ai_cv = image.image2cv(crop_ai, False, False)
        debug_time("crop")

        gray = crop_ai.to_format(image.Format.FMT_GRAYSCALE)
        gray_cv = image.image2cv(gray, False, False)
        debug_time("gray")

        # 二值化图，找出黑色外轮廓，可以用其它算法
        # 高斯模糊去噪声
        # blurred = cv2.GaussianBlur(gray_cv, (5, 5), 0)
        # 边缘检测，阈值 0，150
        # edged = cv2.Canny(blurred, 50, 150)
        # # 膨胀处理
        # kernel = np.ones((5, 5), np.uint8)
        # dilated = cv2.dilate(edged, kernel, iterations=1)
        # # 腐蚀处理
        # binary = cv2.erode(dilated, kernel, iterations=1)
        # 自适应二值化，最后两个参数可以调整
        binary = cv2.adaptiveThreshold(gray_cv, 255,
                       cv2.ADAPTIVE_THRESH_MEAN_C,
                       cv2.THRESH_BINARY_INV, 27, 31)
        debug_time("binary")


        if std_from_white_rect:
            # 执行洪泛填充找出内白色轮廓
            h, w = binary.shape[:2]
            mask = np.zeros((h + 2, w + 2), np.uint8)
            # 设置种子点（左上角和右下角），如果环境好，可以只点一个角
            seed_point = (2, 2)
            seed_point2 = (w - 2, h - 2)
            # 设置填充值（白色 255）
            fill_value = 255
            # 执行洪泛填充（以左上角像素值为基准）
            cv2.floodFill(binary, mask, seed_point, fill_value, loDiff=5, upDiff=5, flags=4)
            cv2.floodFill(binary, mask, seed_point2, fill_value, loDiff=5, upDiff=5, flags=4)
            binary = cv2.bitwise_not(binary)
            debug_time("fill")

        # 查找轮廓4个角点
        approx = None
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if len(contours) > 0:
            # 筛选出最大的轮廓
            largest_contour = max(contours, key=cv2.contourArea)
            # 近似多边形
            epsilon = 0.02 * cv2.arcLength(largest_contour, True)
            approx = cv2.approxPolyDP(largest_contour, epsilon, True)
            debug_time("find countours")
            # 如果找到的是一个四边形
            if len(approx) == 4:
                # 获取矩形四个角点
                # 对角点进行排序：左上、右上、右下、左下
                corners = approx.reshape((4, 2))
                # 按顺序排列角点（左上、右上、右下、左下）
                rect = np.zeros((4, 2), dtype="float32")
                s = corners.sum(axis=1)
                rect[0] = corners[np.argmin(s)] # 最小和，左上
                rect[2] = corners[np.argmax(s)] # 最大和，右下
                diff = np.diff(corners, axis=1) # y - x
                rect[3] = corners[np.argmax(diff)] # 差最大，左下
                rect[1] = corners[np.argmin(diff)] # 差最小，右上
                minW = min(rect[1][0] - rect[0][0], rect[2][0] - rect[3][0])
                minH = min(rect[3][1] - rect[0][1], rect[2][1] - rect[1][1])
                if minH > rect_min_limit and minW > rect_min_limit:
                    debug_time("find rect")

                    # 计算目标图像宽高（按最大边计算）
                    # (tl, tr, br, bl) = rect
                    # widthA = np.linalg.norm(br - bl)
                    # widthB = np.linalg.norm(tr - tl)
                    # maxWidth = int(max(widthA, widthB) * img_ai_scale[0] * std_scale)

                    # heightA = np.linalg.norm(tr - br)
                    # heightB = np.linalg.norm(tl - bl)
                    # maxHeight = int(max(heightA, heightB) * img_ai_scale[1] * std_scale)
                    # print(maxWidth, maxHeight)


                    maxWidth = std_res[0]
                    maxHeight = std_res[1]

                    # rect 映射到大图, 从大图中得到标准内框图
                    rect[:, 0] += crop_ai_rect[0]
                    rect[:, 1] += crop_ai_rect[1]
                    rect[:, 0] *= img_ai_scale[0]
                    rect[:, 1] *= img_ai_scale[1]
                    # 透视变换
                    dst = np.array([
                        [0, 0],
                        [maxWidth - 1, 0],
                        [maxWidth - 1, maxHeight - 1],
                        [0, maxHeight - 1]], dtype="float32")
                    M = cv2.getPerspectiveTransform(rect, dst)
                    M_inv = np.linalg.inv(M)
                    img_std_cv = cv2.warpPerspective(img_cv, M, (maxWidth, maxHeight))
                    img_std = image.cv2image(img_std_cv, False, False)
                    debug_time("get std img")

                    # 如果前面找到得标准图有黑框，用find_blobs 处理一下
                    # ths = [[0, 10, -128, 127, -128, 127]]
                    # blobs = img_std.find_blobs(ths, roi=[0, 0, 10, 10], x_stride=1, y_stride=1)
                    # A4 纸 21cm, 黑框 1.8*2=3.6cm， 白色区域为 17.4cm，圆圈2cm间距
                    # 得出 圆圈间距像素为 2/17.4 * 白色区域高度像素。（0.1149425287356322）
                    # 如果是黑色边框，则 2/21 * 黑框高度像素。(0.09523809523809523)
                    # if len(blobs) > 0: # 有黑框
                        # circle_dist = img_std.height() * 0.09523809523809523
                    # else:
                    if std_from_white_rect:
                        circle_dist = int(img_std.height() * 0.1149425287356322)
                    else:
                        circle_dist = img_std.height() * 0.09523809523809523
                    if circle_dist > 0:
                        center = [img_std.width() // 2, img_std.height() // 2]
                        # 是否找圆和圆心
                        center_new = None
                        if find_circle:
                            img_std_gray_cv = cv2.cvtColor(img_std_cv, cv2.COLOR_RGB2GRAY)
                            w = h = int(circle_dist * 3)
                            roi = [center[0] - w // 2, center[1] - h // 2, w, h]
                            img_small_circle_cv = img_std_gray_cv[roi[1]:roi[1] + roi[3], roi[0]:roi[0]+roi[2]]
                            if DEBUG:
                                img_small_circle = image.cv2image(img_small_circle_cv, False, False)
                                img.draw_image(crop_ai.width(), img_std.height(), img_small_circle)

                            # 用霍夫变换找圆
                            circles = cv2.HoughCircles(img_small_circle_cv, cv2.HOUGH_GRADIENT, dp=1.2,
                                                    minDist=roi[2] // 2,
                                                    param1=100, param2=20,
                                                    minRadius=roi[2] // 4, maxRadius=roi[2] // 2)
                            # 把找圆范围画出来
                            if DEBUG:
                                img_std.draw_rect(roi[0], roi[1], roi[2], roi[3], image.COLOR_ORANGE)
                                cv2.circle(img_std_cv, center, 1, (0, 255, 0), -1)
                            # 若检测到圆，得到中心和半径
                            circle_dist_new = 0
                            if circles is not None:
                                circles = np.uint16(np.around(circles))
                                for c in circles[0, :]:
                                    center_new = (c[0] + roi[0], c[1] + roi[1])  # 圆心坐标偏移回原图
                                    circle_dist_new = c[2]
                                    if DEBUG:
                                        cv2.circle(img_std_cv, center_new, circle_dist_new, (0, 255, 0), 1)
                                        cv2.circle(img_std_cv, center_new, 1, (0, 0, 255), 3)  # 圆心
                                    # 这里认为只能检测到一个圆，如果多个，那画面有问题，或者再优化这里的代码
                                    break
                            # binary = cv2.adaptiveThreshold(img_std_gray_cv, 255,
                            #                cv2.ADAPTIVE_THRESH_MEAN_C,
                            #                cv2.THRESH_BINARY_INV, 11, 3)
                            # # 膨胀加强线条
                            # kernel = np.ones((2, 2), np.uint8)
                            # enhanced = cv2.dilate(binary, kernel, iterations=1)
                            # eroded = cv2.erode(enhanced, kernel, iterations=1)
                            # circles = img3.find_circles(roi = roi, x_stride=4, y_stride = 4, threshold=2000, r_step = 4)
                            if center_new:
                                # 更新圆环中心和圆环间距离
                                center = center_new
                                circle_dist = circle_dist_new
                                # 在标准图中画出新中心和第三个圈
                                if DEBUG:
                                    cv2.circle(img_std_cv, center, 1, (0, 255, 0), -1)
                                    cv2.circle(img_std_cv, center, circle_dist * 3, (0, 255, 0), 1)
                            debug_time("find circle")

                            # 如果不找圆心，或者找到了圆心
                        if (not find_circle) or (center_new):
                            # 原图画圆中心
                            std_center_points = np.array([[center]], dtype=np.float32)
                            original_center_point = cv2.perspectiveTransform(std_center_points, M_inv)[0][0].astype(np.int32).tolist()
                            # 计算误差（加入偏移补偿，移植自丢失追踪.py）
                            err_center = [
                                original_center_point[0] - (center_pos[0] + offset_x),
                                original_center_point[1] - (center_pos[1] + offset_y),
                            ]
                            last_center = original_center_point
                            last_center_small = [int(last_center[0] / img_ai_scale[0]), int(last_center[1] / img_ai_scale[1])]

                            # 根据now_state决定是否进行舵机控制
                            servo_moved = False
                            if now_state == 1:  # 状态1：图像处理 + 舵机控制
                                if servo_controller and enable_servo_control:
                                    # 在状态1开始时，确保PID状态是干净的
                                    if not hasattr(servo_controller, '_state1_initialized') or not servo_controller._state1_initialized:
                                        print("🔧 状态1首次进入，强制清理PID状态")
                                        if servo_controller.pid_pan:
                                            servo_controller.pid_pan.integral = 0
                                            servo_controller.pid_pan.last_error = 0
                                            servo_controller.pid_pan.last_output = 0
                                        if servo_controller.pid_tilt:
                                            servo_controller.pid_tilt.integral = 0
                                            servo_controller.pid_tilt.last_error = 0
                                            servo_controller.pid_tilt.last_output = 0
                                        servo_controller._state1_initialized = True
                                        print("✅ 状态1 PID初始化完成")
                                    # 更新激光状态
                                    if servo_controller.laser_controller:
                                        servo_controller.laser_controller.update()

                                    # 更新A28状态
                                    if servo_controller.a28_controller:
                                        servo_controller.a28_controller.update()

                                    servo_moved = servo_controller.update_servo_by_error(err_center[0], err_center[1])
                                    if servo_moved:
                                        if DEBUG:
                                            # 显示详细的PID调试信息
                                            pan_integral = servo_controller.pid_pan.integral
                                            tilt_integral = servo_controller.pid_tilt.integral
                                            print(f"🎯 [状态1] 舵机调整: 误差 X={err_center[0]:+.1f}, Y={err_center[1]:+.1f}")
                                            print(f"PID积分: Pan={pan_integral:.2f}, Tilt={tilt_integral:.2f}")
                                            print(f"舵机位置: Pan={servo_controller.pan_position}, Tilt={servo_controller.tilt_position}")
                                    else:
                                        # 根据死区判断是否已居中
                                        if DEBUG:
                                            if abs(err_center[0]) < error_dead_zone and abs(err_center[1]) < error_dead_zone:
                                                print("🎯 [状态1] 圆心已居中！(在死区内)")
                                            else:
                                                print(f"⏸️  [状态1] 舵机暂停: 误差 X={err_center[0]:+.1f}, Y={err_center[1]:+.1f}")
                                else:
                                    if DEBUG:
                                        print("⚠️  [状态1] 舵机控制器未初始化")

                            elif now_state == 2:  # 状态2：图像处理 + 舵机控制 + 强制激光定时器
                                if servo_controller and enable_servo_control:
                                    # 在状态2开始时，确保PID状态是干净的并启动定时器
                                    if not hasattr(servo_controller, '_state2_initialized') or not servo_controller._state2_initialized:
                                        print("=" * 60)
                                        print("🎯 进入状态2：强制激光模式")
                                        print(f"🔍 初始化检查: hasattr={hasattr(servo_controller, '_state2_initialized')}")
                                        if hasattr(servo_controller, '_state2_initialized'):
                                            print(f"🔍 当前标志值: {servo_controller._state2_initialized}")
                                        print("🔧 正在清理PID状态...")
                                        if servo_controller.pid_pan:
                                            servo_controller.pid_pan.integral = 0
                                            servo_controller.pid_pan.last_error = 0
                                            servo_controller.pid_pan.last_output = 0
                                        if servo_controller.pid_tilt:
                                            servo_controller.pid_tilt.integral = 0
                                            servo_controller.pid_tilt.last_error = 0
                                            servo_controller.pid_tilt.last_output = 0
                                        servo_controller._state2_initialized = True
                                        print("✅ PID状态清理完成")
                                        # 启动强制激光定时器（每次进入状态2都重新启动）
                                        if force_laser_timer.timer_active:
                                            print("🔄 检测到定时器已激活，先停止旧定时器")
                                            force_laser_timer.stop_timer()
                                        force_laser_timer.start_timer()
                                        print("✅ 状态2初始化完成，开始监控激光发射")
                                        print("=" * 60)

                                    # 更新激光状态
                                    if servo_controller.laser_controller:
                                        servo_controller.laser_controller.update()

                                    # 更新A28状态
                                    if servo_controller.a28_controller:
                                        servo_controller.a28_controller.update()

                                    # 执行舵机控制（与状态1相同）
                                    # 检查是否正在强制发射
                                    if force_laser_timer.is_force_firing:
                                        if DEBUG:
                                            print(f"🔍 [状态2] 正在强制发射，舵机控制继续但激光触发被禁用")

                                    servo_moved = servo_controller.update_servo_by_error(err_center[0], err_center[1])
                                    if servo_moved:
                                        if DEBUG:
                                            # 显示详细的PID调试信息
                                            pan_integral = servo_controller.pid_pan.integral
                                            tilt_integral = servo_controller.pid_tilt.integral
                                            print(f"🎯 [状态2] 舵机调整: 误差 X={err_center[0]:+.1f}, Y={err_center[1]:+.1f}")
                                            print(f"PID积分: Pan={pan_integral:.2f}, Tilt={tilt_integral:.2f}")
                                            print(f"舵机位置: Pan={servo_controller.pan_position}, Tilt={servo_controller.tilt_position}")
                                    else:
                                        # 根据死区判断是否已居中
                                        if DEBUG:
                                            if abs(err_center[0]) < error_dead_zone and abs(err_center[1]) < error_dead_zone:
                                                print("🎯 [状态2] 圆心已居中！(在死区内)")
                                            else:
                                                print(f"⏸️  [状态2] 舵机暂停: 误差 X={err_center[0]:+.1f}, Y={err_center[1]:+.1f}")

                                    # 更新强制激光定时器
                                    timer_status = force_laser_timer.update()
                                    if timer_status == "force_fire_start":
                                        # 开始强制激光发射
                                        print("=" * 60)
                                        print("🚨🚨🚨 状态2强制激光发射开始！🚨🚨🚨")
                                        print("🔴 原因: 1.5秒超时，激光未正常发射")
                                        print("🔴 强制发射时长: 0.5秒")
                                        print("=" * 60)
                                        if servo_controller.laser_controller:
                                            success = servo_controller.laser_controller.force_fire_laser(0.5)
                                            if success:
                                                print("✅ 强制激光发射命令已发送")
                                            else:
                                                print("❌ 强制激光发射命令失败")
                                    elif timer_status == "force_fire_complete":
                                        # 强制激光发射完成（由update()方法自动处理停止）
                                        print("=" * 60)
                                        print("✅✅✅ 状态2强制激光发射完成！✅✅✅")
                                        print("⚫ 强制激光已自动关闭")
                                        print("⏰ 强制定时器已停止")
                                        print("=" * 60)

                                    # 检查正常激光是否发射，如果发射则停止定时器
                                    # 注意：只有在非强制发射时才认为是正常发射
                                    if (servo_controller.laser_controller and
                                        servo_controller.laser_controller.is_firing and
                                        not servo_controller.laser_controller.is_force_firing and
                                        not force_laser_timer.is_force_firing):
                                        if force_laser_timer.timer_active:
                                            remaining_time = force_laser_timer.get_remaining_time()
                                            force_laser_timer.stop_timer()
                                            print("=" * 50)
                                            print("✅ 检测到正常激光发射！")
                                            print(f"⏰ 剩余时间: {remaining_time/1000:.1f}秒")
                                            print("🛑 强制激光定时器已停止")
                                            print("=" * 50)

                                else:
                                    if DEBUG:
                                        print("⚠️  [状态2] 舵机控制器未初始化")

                            elif now_state == 3:  # 状态3：扫描搜索 + 跟踪模式（仅在有目标时处理跟踪）
                                # 状态3的有目标逻辑已移动到无目标逻辑中统一处理，避免重复调用
                                pass



                            elif now_state == 0:  # 状态0：仅图像处理
                                if DEBUG:
                                    print(f"📷 [状态0] 仅图像处理: 误差 X={err_center[0]:+.1f}, Y={err_center[1]:+.1f}")

                            else:  # 其他状态：预留功能
                                if DEBUG:
                                    print(f"🔧 [状态{now_state}] 预留功能: 误差 X={err_center[0]:+.1f}, Y={err_center[1]:+.1f}")
                            # 原图画圆
                            radius = circle_dist * 3 # 第三个圈的半径
                            # 构造圆上的轮廓点
                            debug_time("get points 3")
                            angles = np.linspace(0, 2 * np.pi, circle_num_points, endpoint=False)  # endpoint=False 避免首尾重复
                            cos_vals = np.cos(angles)
                            sin_vals = np.sin(angles)

                            # 向量方式生成所有点
                            x = center[0] + radius * cos_vals
                            y = center[1] + radius * sin_vals
                            circle_pts = np.stack((x, y), axis=1).astype(np.float32)  # shape: (N, 2)
                            circle_pts = circle_pts[np.newaxis, :, :]  # reshape to (1, N, 2)
                            debug_time("get points 1")

                            # 反变换回原图
                            orig_circle_pts = cv2.perspectiveTransform(circle_pts, M_inv)
                            debug_time("get points")

                            # 找激光点
                            original_lasert_point = None
                            if find_laser:
                                laser_point = find_laser_point(img_std, img if DEBUG else img_ai)
                                if laser_point:
                                    # 原图坐标
                                    points = np.array([[[laser_point.x(), laser_point.y()]]], dtype=np.float32)
                                    original_lasert_point = cv2.perspectiveTransform(points, M_inv)[0][0]
                            # 画在大图上
                            if DEBUG or debug_show_hires:
                                img.draw_circle(original_center_point[0], original_center_point[1], 4, image.COLOR_RED, thickness=-1)
                                pts = np.round(orig_circle_pts[0]).astype(np.int32)
                                cv2.polylines(img_cv, [pts], isClosed=True, color=(0, 0, 255), thickness=1)
                                if original_lasert_point is not None:
                                    img.draw_circle(original_lasert_point[0], original_lasert_point[1], 3, image.COLOR_GREEN, thickness=1)
                            else:
                            # 画在小图上显示
                                # too slow
                                # center_ai = image.resize_map_pos(img.width(), img.height(), img_ai.width(), img_ai.height(), image.Fit.FIT_FILL, original_center_point[0], original_center_point[1])
                                center_ai = [int(original_center_point[0] * img_ai_scale[0]), int(original_center_point[1] * img_ai_scale[1])]
                                img_ai.draw_circle(center_ai[0], center_ai[1], 2, image.COLOR_RED, thickness=-1)
                                pts = orig_circle_pts[0]  # shape: (N, 2)

                                scaled_pts = (pts * img_ai_scale).astype(np.int32)  # shape: (N, 2)
                                points = scaled_pts.reshape(-1).tolist()  # 转为 Python list（与原结果相同）
                                if debug_draw_circle:
                                    img_ai.draw_keypoints(points, image.COLOR_RED, 1, line_thickness=1)
                                if original_lasert_point is not None:
                                    img_ai.draw_circle(original_lasert_point[0], original_lasert_point[1], 3, image.COLOR_GREEN, thickness=1)
                            debug_time("draw points")
                        if DEBUG:
                            img.draw_image(crop_ai.width(), 0, img_std)
                    else:
                        print("detected circle too small", img_std.width(), img_std.height())
                else:
                    print(minW, minH, "rect not valid")

        # 绘制路径
        if approx is not None:
            cv2.drawContours(crop_ai_cv, [approx], -1, (255, 255, 255), 1)
        if DEBUG:
            img.draw_image(0, 0, crop_ai)
            img2 = image.cv2image(binary, False, False)
            img.draw_image(0, crop_ai.height(), img2)

        if debug_draw_rect:
            img.draw_rect(crop_rect[0], crop_rect[1], crop_rect[2], crop_rect[3], color = image.COLOR_RED, thickness=2)
            # msg = f'{detector.labels[obj.class_id]}: {obj.score:.2f}'
            # img.draw_string(obj.x, obj.y, msg, color = image.COLOR_RED, scale=2)
        debug_time("draw")

    # 状态3统一处理：扫描搜索 + 跟踪模式
    if now_state == 3:  # 状态3的所有逻辑统一处理
        if servo_controller and enable_servo_control:
            # 在状态3开始时，确保PID状态是干净的并启动扫描
            if not hasattr(servo_controller, '_state3_initialized') or not servo_controller._state3_initialized:
                print("=" * 60)
                print("🔍 进入状态3：扫描搜索模式（统一处理）")
                print(f"🔍 初始化检查: hasattr={hasattr(servo_controller, '_state3_initialized')}")
                if hasattr(servo_controller, '_state3_initialized'):
                    print(f"🔍 当前标志值: {servo_controller._state3_initialized}")
                print("🔧 正在清理PID状态...")
                if servo_controller.pid_pan:
                    servo_controller.pid_pan.integral = 0
                    servo_controller.pid_pan.last_error = 0
                    servo_controller.pid_pan.last_output = 0
                if servo_controller.pid_tilt:
                    servo_controller.pid_tilt.integral = 0
                    servo_controller.pid_tilt.last_error = 0
                    servo_controller.pid_tilt.last_output = 0
                servo_controller._state3_initialized = True
                print("✅ PID状态清理完成")
                # 启动扫描控制器
                if scan_controller.scanning:
                    print("🔄 检测到扫描器已激活，先停止旧扫描器")
                    scan_controller.stop_scan()
                scan_controller.start_scan()
                print("✅ 状态3初始化完成，开始扫描搜索（统一模式）")
                print("=" * 60)

            # 统一的目标检测逻辑
            target_detected = len(objs) > 0  # 如果检测到A4纸黑框就认为有目标

            # 更新扫描控制器
            scan_status = scan_controller.update(target_detected)

            # 添加详细的状态调试
            if DEBUG:
                current_time = time.ticks_ms()
                elapsed = (current_time - scan_controller.scan_start_time) / 1000.0 if scan_controller.scanning else 0
                print(f"🔍 [状态3调试] 目标检测: {target_detected}, 扫描状态: {scan_status}, 已用时: {elapsed:.2f}s, 扫描阶段: {scan_controller.scan_phase}")

            if scan_status == "target_found" or scan_status == "tracking":
                # 发现目标，执行跟踪模式（与状态2相同）
                # 更新激光状态
                if servo_controller.laser_controller:
                    servo_controller.laser_controller.update()

                # 更新A28状态
                if servo_controller.a28_controller:
                    servo_controller.a28_controller.update()

                # 执行舵机控制（与状态2相同）
                servo_moved = servo_controller.update_servo_by_error(err_center[0], err_center[1])
                if servo_moved:
                    if DEBUG:
                        pan_integral = servo_controller.pid_pan.integral
                        tilt_integral = servo_controller.pid_tilt.integral
                        print(f"🎯 [状态3-跟踪] 舵机调整: 误差 X={err_center[0]:+.1f}, Y={err_center[1]:+.1f}")
                        print(f"PID积分: Pan={pan_integral:.2f}, Tilt={tilt_integral:.2f}")
                else:
                    if DEBUG:
                        if abs(err_center[0]) < error_dead_zone and abs(err_center[1]) < error_dead_zone:
                            print("🎯 [状态3-跟踪] 圆心已居中！(在死区内)")
                        else:
                            print(f"⏸️  [状态3-跟踪] 舵机暂停: 误差 X={err_center[0]:+.1f}, Y={err_center[1]:+.1f}")

            elif scan_status == "scanning":
                # 正在扫描，控制舵机按扫描角度移动
                print(f"🚀 [状态3] 执行扫描分支！")
                target_angle = scan_controller.get_target_angle()
                remaining_time = scan_controller.get_remaining_time()
                print(f"🚀 [状态3] 目标角度: {target_angle:.1f}°, 剩余时间: {remaining_time:.1f}s")

                # 计算目标舵机位置（角度转换为舵机值）
                # 舵机范围：500-2500 (2000单位)，假设对应 -90°到+90° (180度)
                # 转换公式：servo_value = 1500 + (angle * 2000/180)
                angle_to_servo_ratio = 2000.0 / 180.0  # 每度对应的舵机单位数
                angle_to_servo_value = 1500 + (target_angle * angle_to_servo_ratio)

                # 限制在舵机范围内
                angle_to_servo_value = max(500, min(2500, angle_to_servo_value))

                # 发送扫描位置命令
                # 根据扫描阶段使用不同的速度
                print(f"🚀 [状态3] 准备发送舵机命令，阶段: {scan_controller.scan_phase}")
                if scan_controller.scan_phase == "moving_to_start":
                    # 移动到起始位置时使用最高速度
                    move_speed = 100  # 最高速度
                    # 将速度参数转换为合适的时间参数
                    # move_speed 100 转换为 5ms（最快）
                    move_time_ms = max(5, min(50, int(55 - move_speed * 0.2)))
                    print(f"🚀 [状态3] 发送移动命令: 角度{target_angle:.1f}° → 舵机值{int(angle_to_servo_value)}, 速度{move_speed}→时间{move_time_ms}ms")
                    servo_controller.send_servo_command(int(angle_to_servo_value), servo_controller.tilt_position, move_time_ms)
                else:
                    # 扫描时使用配置的扫描速度
                    # 将速度参数转换为合适的时间参数
                    # scan_step_speed 越大，时间越小（速度越快）
                    if scan_step_speed <= 100:
                        time_ms = max(5, min(50, int(55 - scan_step_speed * 0.5)))
                    else:
                        # 超过100的值，进一步减少时间
                        time_ms = max(1, int(5 - (scan_step_speed - 100) * 0.01))
                    print(f"🚀 [状态3] 发送扫描命令: 角度{target_angle:.1f}° → 舵机值{int(angle_to_servo_value)}, 速度{scan_step_speed}→时间{time_ms}ms")
                    servo_controller.send_servo_command(int(angle_to_servo_value), servo_controller.tilt_position, time_ms)

                if DEBUG:
                    phase_name = "移动到起始位置" if scan_controller.scan_phase == "moving_to_start" else "扫描中"
                    speed_used = 100 if scan_controller.scan_phase == "moving_to_start" else scan_step_speed
                    current_fps = time.fps()
                    print(f"🔍 [状态3-{phase_name}] 角度: {target_angle:.1f}°, 舵机值: {angle_to_servo_value:.0f}, 速度: {speed_used}, FPS: {current_fps:.1f}, 剩余: {remaining_time:.1f}s")

            elif scan_status == "scan_timeout":
                # 扫描超时，强制发射激光
                print("=" * 60)
                print("🚨🚨🚨 状态3扫描超时强制发射！🚨🚨🚨")
                print("🔴 原因: 3.5秒扫描超时，未发现目标")
                print("🔴 强制发射时长: 0.5秒")
                print("=" * 60)
                if servo_controller.laser_controller:
                    success = servo_controller.laser_controller.force_fire_laser(0.5)
                    if success:
                        print("✅ 状态3强制激光发射命令已发送")
                    else:
                        print("❌ 状态3强制激光发射命令失败")

            elif scan_status == "force_firing":
                # 正在强制发射，更新激光状态
                if servo_controller.laser_controller:
                    servo_controller.laser_controller.update()

        else:
            if DEBUG:
                print("⚠️  [状态3] 舵机控制器未初始化")

    if now_state == 4:  # 状态4的所有逻辑统一处理（从右往左扫描）
        if servo_controller and enable_servo_control:
            # 在状态4开始时，确保PID状态是干净的并启动扫描
            if not hasattr(servo_controller, '_state4_initialized') or not servo_controller._state4_initialized:
                print("=" * 60)
                print("🔍 进入状态4：扫描搜索模式（从右往左扫描）")
                print(f"🔍 初始化检查: hasattr={hasattr(servo_controller, '_state4_initialized')}")
                if hasattr(servo_controller, '_state4_initialized'):
                    print(f"🔍 当前标志值: {servo_controller._state4_initialized}")
                print("🔧 正在清理PID状态...")
                if servo_controller.pid_pan:
                    servo_controller.pid_pan.integral = 0
                    servo_controller.pid_pan.last_error = 0
                    servo_controller.pid_pan.last_output = 0
                if servo_controller.pid_tilt:
                    servo_controller.pid_tilt.integral = 0
                    servo_controller.pid_tilt.last_error = 0
                    servo_controller.pid_tilt.last_output = 0
                servo_controller._state4_initialized = True
                print("✅ PID状态清理完成")
                # 启动扫描控制器
                if scan4_controller.scanning:
                    print("🔄 检测到扫描器已激活，先停止旧扫描器")
                    scan4_controller.stop_scan()
                scan4_controller.start_scan()
                print("✅ 状态4初始化完成，开始扫描搜索（从右往左扫描）")
                print("=" * 60)

            # 统一的目标检测逻辑
            target_detected = len(objs) > 0  # 如果检测到A4纸黑框就认为有目标

            # 更新扫描控制器
            scan_status = scan4_controller.update(target_detected)

            # 添加详细的状态调试
            if DEBUG:
                current_time = time.ticks_ms()
                elapsed = (current_time - scan4_controller.scan_start_time) / 1000.0 if scan4_controller.scanning else 0
                print(f"🔍 [状态4调试] 目标检测: {target_detected}, 扫描状态: {scan_status}, 已用时: {elapsed:.2f}s, 扫描阶段: {scan4_controller.scan_phase}")

            if scan_status == "target_found" or scan_status == "tracking":
                # 发现目标，执行跟踪模式（与状态2相同）
                # 更新激光状态
                if servo_controller.laser_controller:
                    servo_controller.laser_controller.update()

                # 更新A28状态
                if servo_controller.a28_controller:
                    servo_controller.a28_controller.update()

                # 执行舵机控制（与状态2相同）
                servo_moved = servo_controller.update_servo_by_error(err_center[0], err_center[1])
                if servo_moved:
                    if DEBUG:
                        pan_integral = servo_controller.pid_pan.integral
                        tilt_integral = servo_controller.pid_tilt.integral
                        print(f"🎯 [状态4-跟踪] 舵机调整: 误差 X={err_center[0]:+.1f}, Y={err_center[1]:+.1f}")
                        print(f"PID积分: Pan={pan_integral:.2f}, Tilt={tilt_integral:.2f}")
                else:
                    if DEBUG:
                        if abs(err_center[0]) < error_dead_zone and abs(err_center[1]) < error_dead_zone:
                            print("🎯 [状态4-跟踪] 圆心已居中！(在死区内)")
                        else:
                            print(f"⏸️  [状态4-跟踪] 舵机暂停: 误差 X={err_center[0]:+.1f}, Y={err_center[1]:+.1f}")

            elif scan_status == "scanning":
                # 正在扫描，控制舵机按扫描角度移动
                print(f"🚀 [状态4] 执行扫描分支！")
                target_angle = scan4_controller.get_target_angle()
                remaining_time = scan4_controller.get_remaining_time()
                print(f"🚀 [状态4] 目标角度: {target_angle:.1f}°, 剩余时间: {remaining_time:.1f}s")

                # 计算目标舵机位置（角度转换为舵机值）
                # 舵机范围：500-2500 (2000单位)，假设对应 -90°到+90° (180度)
                # 转换公式：servo_value = 1500 + (angle * 2000/180)
                angle_to_servo_ratio = 2000.0 / 180.0  # 每度对应的舵机单位数
                angle_to_servo_value = 1500 + (target_angle * angle_to_servo_ratio)

                # 限制在舵机范围内
                angle_to_servo_value = max(500, min(2500, angle_to_servo_value))

                # 发送扫描位置命令
                # 根据扫描阶段使用不同的速度
                print(f"🚀 [状态4] 准备发送舵机命令，阶段: {scan4_controller.scan_phase}")
                if scan4_controller.scan_phase == "moving_to_start":
                    # 移动到起始位置时使用最高速度
                    move_speed = 100  # 最高速度
                    # 将速度参数转换为合适的时间参数
                    # move_speed 100 转换为 5ms（最快）
                    move_time_ms = max(5, min(50, int(55 - move_speed * 0.2)))
                    print(f"🚀 [状态4] 发送移动命令: 角度{target_angle:.1f}° → 舵机值{int(angle_to_servo_value)}, 速度{move_speed}→时间{move_time_ms}ms")
                    servo_controller.send_servo_command(int(angle_to_servo_value), servo_controller.tilt_position, move_time_ms)
                else:
                    # 扫描时使用配置的扫描速度
                    # 将速度参数转换为合适的时间参数
                    # scan4_step_speed 越大，时间越小（速度越快）
                    if scan4_step_speed <= 100:
                        time_ms = max(5, min(50, int(55 - scan4_step_speed * 0.5)))
                    else:
                        # 超过100的值，进一步减少时间
                        time_ms = max(1, int(5 - (scan4_step_speed - 100) * 0.01))
                    print(f"🚀 [状态4] 发送扫描命令: 角度{target_angle:.1f}° → 舵机值{int(angle_to_servo_value)}, 速度{scan4_step_speed}→时间{time_ms}ms")
                    servo_controller.send_servo_command(int(angle_to_servo_value), servo_controller.tilt_position, time_ms)

                if DEBUG:
                    phase_name = "移动到起始位置" if scan4_controller.scan_phase == "moving_to_start" else "扫描中"
                    speed_used = 100 if scan4_controller.scan_phase == "moving_to_start" else scan4_step_speed
                    current_fps = time.fps()
                    print(f"🔍 [状态4-{phase_name}] 角度: {target_angle:.1f}°, 舵机值: {angle_to_servo_value:.0f}, 速度: {speed_used}, FPS: {current_fps:.1f}, 剩余: {remaining_time:.1f}s")

            elif scan_status == "scan_timeout":
                # 扫描超时，强制发射激光
                print("=" * 60)
                print("🚨🚨🚨 状态4扫描超时强制发射！🚨🚨🚨")
                print("🔴 原因: 3.5秒扫描超时，未发现目标")
                print("🔴 强制发射时长: 0.5秒")
                print("=" * 60)
                if servo_controller.laser_controller:
                    success = servo_controller.laser_controller.force_fire_laser(0.5)
                    if success:
                        print("✅ 状态4强制激光发射命令已发送")
                    else:
                        print("❌ 状态4强制激光发射命令失败")

            elif scan_status == "force_firing":
                # 正在强制发射，更新激光状态
                if servo_controller.laser_controller:
                    servo_controller.laser_controller.update()

        else:
            if DEBUG:
                print("⚠️  [状态4] 舵机控制器未初始化")

    if DEBUG or debug_show_hires:
        if debug_draw_err_line:
            img.draw_line(center_pos[0], center_pos[1], last_center[0], last_center[1], image.COLOR_RED, thickness=3)
        if debug_draw_err_msg:
            # 根据now_state确定系统模式
            if now_state == 0:
                system_mode = "图像处理模式"
                mode_color = image.COLOR_BLUE
            elif now_state == 1:
                system_mode = "图像+舵机控制"
                mode_color = image.COLOR_GREEN
            elif now_state == 2:
                # 显示状态2的定时器信息
                remaining_time = force_laser_timer.get_remaining_time()
                if force_laser_timer.timer_active:
                    system_mode = f"强制激光模式({remaining_time/1000:.1f}s)"
                elif force_laser_timer.is_force_firing:
                    system_mode = "强制激光发射中"
                else:
                    system_mode = "强制激光模式"
                mode_color = image.COLOR_RED
            elif now_state == 3:
                # 显示状态3的扫描信息
                if scan_controller.scanning:
                    remaining_time = scan_controller.get_remaining_time()
                    target_angle = scan_controller.get_target_angle()
                    if scan_controller.target_found:
                        system_mode = "扫描跟踪模式"
                    elif scan_controller.force_fire_triggered:
                        system_mode = "扫描强制发射"
                    else:
                        system_mode = f"扫描模式({remaining_time:.1f}s,{target_angle:.0f}°)"
                else:
                    system_mode = "扫描搜索模式"
                mode_color = image.COLOR_ORANGE
            elif now_state == 4:
                # 显示状态4的扫描信息（从右往左扫描）
                if scan4_controller.scanning:
                    remaining_time = scan4_controller.get_remaining_time()
                    target_angle = scan4_controller.get_target_angle()
                    if scan4_controller.target_found:
                        system_mode = "右扫跟踪模式"
                    elif scan4_controller.force_fire_triggered:
                        system_mode = "右扫强制发射"
                    else:
                        system_mode = f"右扫模式({remaining_time:.1f}s,{target_angle:.0f}°)"
                else:
                    system_mode = "右扫搜索模式"
                mode_color = image.COLOR_PURPLE
            else:
                system_mode = f"预留模式{now_state}"
                mode_color = image.COLOR_YELLOW

            servo_status = "SERVO ON" if (servo_controller and enable_servo_control and (now_state == 1 or now_state == 2 or now_state == 3 or now_state == 4)) else "SERVO OFF"
            servo_pos_str = f"P:{servo_controller.pan_position} T:{servo_controller.tilt_position}" if servo_controller else ""

            # 获取激光状态
            laser_status = "LASER OFF"
            if servo_controller and servo_controller.laser_controller and (now_state == 1 or now_state == 2 or now_state == 3 or now_state == 4):
                if (now_state == 2 and force_laser_timer.is_force_firing) or (now_state == 3 and scan_controller.force_fire_triggered):
                    laser_status = "LASER FORCE_FIRING"
                else:
                    laser_status = f"LASER {servo_controller.laser_controller.get_status()}"

            # 获取A28状态（现在A28功能已集成到激光控制器中）
            a28_status = "A28 INTEGRATED"
            if servo_controller and servo_controller.a28_controller and (now_state == 1 or now_state == 2 or now_state == 3):
                a28_status = f"A28 {servo_controller.a28_controller.get_status()}"
            elif servo_controller and servo_controller.laser_controller and (now_state == 1 or now_state == 2 or now_state == 3):
                # A28功能已集成到激光控制器中，显示激光状态
                a28_status = f"A28→LASER {servo_controller.laser_controller.get_status()}"

            # 获取UART0接收状态
            uart0_status = "UART0 OFF"
            buffer_count = 0
            if uart0_receiver and uart0_receiver.uart_rx:
                buffer_count = len(uart0_receiver.received_buffer)
                uart0_status = f"UART0 RX({buffer_count}) STATE:{now_state}"

            # 判断状态（只在状态1、状态2和状态3时显示舵机状态）
            if servo_controller and (now_state == 1 or now_state == 2 or now_state == 3):
                current_dead_zone = error_dead_zone * 1.5 if servo_controller.is_stable else error_dead_zone
                in_dead_zone = abs(err_center[0]) < current_dead_zone and abs(err_center[1]) < current_dead_zone
                total_error = (err_center[0]**2 + err_center[1]**2)**0.5

                if servo_controller.is_stable:
                    dead_zone_status = f"STABLE±{current_dead_zone:.1f}"
                elif in_dead_zone:
                    dead_zone_status = f"DEAD_ZONE±{error_dead_zone}"
                else:
                    dead_zone_status = f"PID_ACTIVE"
            else:
                dead_zone_status = "SERVO_OFF" if now_state == 0 else f"MODE_{now_state}"
                in_dead_zone = False

            img.draw_string(2, img.height() - 152, f"MODE: {system_mode}", mode_color, scale=1.2, thickness=2)
            img.draw_string(2, img.height() - 132, f"err: {err_center[0]:5.1f}, {err_center[1]:5.1f}, fps: {time.fps():2.0f}", image.COLOR_RED, scale=1.5, thickness=2)
            img.draw_string(2, img.height() - 112, f"{servo_status} {servo_pos_str}", image.COLOR_GREEN, scale=1.2, thickness=2)
            img.draw_string(2, img.height() - 92, f"STATUS: {dead_zone_status}", image.COLOR_YELLOW if in_dead_zone else image.COLOR_CYAN, scale=1.2, thickness=2)
            img.draw_string(2, img.height() - 72, f"{laser_status}", image.COLOR_RED if "FIRING" in laser_status else image.COLOR_WHITE, scale=1.2, thickness=2)
            img.draw_string(2, img.height() - 52, f"{a28_status}", image.COLOR_GREEN if "FIRING" in a28_status else image.COLOR_WHITE, scale=1.2, thickness=2)
            img.draw_string(2, img.height() - 32, f"{uart0_status}", image.COLOR_CYAN if buffer_count > 0 else image.COLOR_WHITE, scale=1.2, thickness=2)
        disp.show(img)
    else:
        if debug_draw_err_line:
            img_ai.draw_line(center_pos[0], center_pos[1], last_center_small[0], last_center_small[1], image.COLOR_RED, thickness=3)
        if debug_draw_err_msg:
            # 根据now_state确定系统模式
            if now_state == 0:
                system_mode = "图像处理模式"
                mode_color = image.COLOR_BLUE
            elif now_state == 1:
                system_mode = "图像+舵机控制"
                mode_color = image.COLOR_GREEN
            elif now_state == 2:
                # 显示状态2的定时器信息
                remaining_time = force_laser_timer.get_remaining_time()
                if force_laser_timer.timer_active:
                    system_mode = f"强制激光模式({remaining_time/1000:.1f}s)"
                elif force_laser_timer.is_force_firing:
                    system_mode = "强制激光发射中"
                else:
                    system_mode = "强制激光模式"
                mode_color = image.COLOR_RED
            elif now_state == 3:
                # 显示状态3的扫描信息
                if scan_controller.scanning:
                    remaining_time = scan_controller.get_remaining_time()
                    target_angle = scan_controller.get_target_angle()
                    if scan_controller.target_found:
                        system_mode = "扫描跟踪模式"
                    elif scan_controller.force_fire_triggered:
                        system_mode = "扫描强制发射"
                    else:
                        system_mode = f"扫描模式({remaining_time:.1f}s,{target_angle:.0f}°)"
                else:
                    system_mode = "扫描搜索模式"
                mode_color = image.COLOR_ORANGE
            else:
                system_mode = f"预留模式{now_state}"
                mode_color = image.COLOR_YELLOW

            servo_status = "SERVO ON" if (servo_controller and enable_servo_control and (now_state == 1 or now_state == 2 or now_state == 3)) else "SERVO OFF"
            servo_pos_str = f"P:{servo_controller.pan_position} T:{servo_controller.tilt_position}" if servo_controller else ""

            # 获取激光状态
            laser_status = "LASER OFF"
            if servo_controller and servo_controller.laser_controller and (now_state == 1 or now_state == 2 or now_state == 3):
                if (now_state == 2 and force_laser_timer.is_force_firing) or (now_state == 3 and scan_controller.force_fire_triggered):
                    laser_status = "LASER FORCE_FIRING"
                else:
                    laser_status = f"LASER {servo_controller.laser_controller.get_status()}"

            # 获取A28状态（现在A28功能已集成到激光控制器中）
            a28_status = "A28 INTEGRATED"
            if servo_controller and servo_controller.a28_controller and (now_state == 1 or now_state == 2 or now_state == 3):
                a28_status = f"A28 {servo_controller.a28_controller.get_status()}"
            elif servo_controller and servo_controller.laser_controller and (now_state == 1 or now_state == 2 or now_state == 3):
                # A28功能已集成到激光控制器中，显示激光状态
                a28_status = f"A28→LASER {servo_controller.laser_controller.get_status()}"

            # 获取UART0接收状态
            uart0_status = "UART0 OFF"
            buffer_count = 0
            if uart0_receiver and uart0_receiver.uart_rx:
                buffer_count = len(uart0_receiver.received_buffer)
                uart0_status = f"UART0 RX({buffer_count}) STATE:{now_state}"

            # 判断状态（只在状态1、状态2和状态3时显示舵机状态）
            if servo_controller and (now_state == 1 or now_state == 2 or now_state == 3):
                current_dead_zone = error_dead_zone * 1.5 if servo_controller.is_stable else error_dead_zone
                in_dead_zone = abs(err_center[0]) < current_dead_zone and abs(err_center[1]) < current_dead_zone
                total_error = (err_center[0]**2 + err_center[1]**2)**0.5

                if servo_controller.is_stable:
                    dead_zone_status = f"STABLE±{current_dead_zone:.1f}"
                elif in_dead_zone:
                    dead_zone_status = f"DEAD_ZONE±{error_dead_zone}"
                else:
                    dead_zone_status = f"PID_ACTIVE"
            else:
                dead_zone_status = "SERVO_OFF" if now_state == 0 else f"MODE_{now_state}"
                in_dead_zone = False

            img_ai.draw_string(2, img_ai.height() - 152, f"MODE: {system_mode}", mode_color, scale=1, thickness=2)
            img_ai.draw_string(2, img_ai.height() - 132, f"err: {err_center[0]:5.1f}, {err_center[1]:5.1f}, fps: {time.fps():2.0f}", image.COLOR_RED, scale=1.2, thickness=2)
            img_ai.draw_string(2, img_ai.height() - 112, f"{servo_status} {servo_pos_str}", image.COLOR_GREEN, scale=1, thickness=2)
            img_ai.draw_string(2, img_ai.height() - 92, f"STATUS: {dead_zone_status}", image.COLOR_YELLOW if in_dead_zone else image.COLOR_CYAN, scale=1, thickness=2)
            img_ai.draw_string(2, img_ai.height() - 72, f"{laser_status}", image.COLOR_RED if "FIRING" in laser_status else image.COLOR_WHITE, scale=1, thickness=2)
            img_ai.draw_string(2, img_ai.height() - 52, f"{a28_status}", image.COLOR_GREEN if "FIRING" in a28_status else image.COLOR_WHITE, scale=1, thickness=2)
            img_ai.draw_string(2, img_ai.height() - 32, f"{uart0_status}", image.COLOR_CYAN if buffer_count > 0 else image.COLOR_WHITE, scale=1, thickness=2)
        disp.show(img_ai)
    debug_time("display img")

# 程序退出清理
try:
    if servo_controller:
        print("🧹 清理舵机资源...")
        servo_controller.close()

    if uart0_receiver:
        print("🧹 清理UART0接收器资源...")
        uart0_receiver.close()

except Exception as e:
    print(f"⚠️  清理资源时出错: {e}")
finally:
    print("✅ 程序结束")