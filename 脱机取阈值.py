# threshold_tuner.py - 完整的脱机调阈值代码  
from maix import touchscreen, camera, display, image, time, app  
from maix.image import Image  
import math  
  
class ThresholdTuner:  
    def __init__(self):  
        # 屏幕参数  
        self._image_width = 320  
        self._image_height = 240  
        self._btn_width = self._image_width // 6  
        self._btn_height = self._image_height // 6  
          
        # 状态变量  
        self._to_get_pixel = False  
        self._last_x = -1  
        self._last_y = -1  
        self._last_pressed = False  # 添加按键状态跟踪  
          
        # 初始化硬件  
        self._ts = touchscreen.TouchScreen()  
        self._disp = display.Display()  
        self._cam = camera.Camera(self._image_width, self._image_height)  
          
        # 加载字体  
        image.load_font("sourcehansans", "/maixapp/share/font/SourceHanSansCN-Regular.otf")  
        image.set_default_font("sourcehansans")  
          
        # 按钮区域定义（图像坐标系）  
        self._btn_area = [0, self._image_height - self._btn_height,   
                         self._btn_width, self._btn_height]  
          
        # 计算按钮在显示屏上的映射坐标  
        self._btn_area_disp = image.resize_map_pos(  
            self._image_width, self._image_height,  
            self._disp.width(), self._disp.height(),  
            image.Fit.FIT_CONTAIN,  
            self._btn_area[0], self._btn_area[1],  
            self._btn_area[2], self._btn_area[3]  
        )  
          
        # 加载保存的阈值  
        self._threshold = self._get_configured_threshold()  
        print("当前阈值:", self._threshold)  
  
    def _rgb_to_lab(self, rgb):  
        """RGB到LAB颜色空间转换"""  
        M = [  
            [0.412453, 0.357580, 0.180423],  
            [0.212671, 0.715160, 0.072169],  
            [0.019334, 0.119193, 0.950227]  
        ]  
          
        r, g, b = rgb[0] / 255.0, rgb[1] / 255.0, rgb[2] / 255.0  
          
        # 线性化RGB值  
        r = r / 12.92 if r <= 0.04045 else ((r + 0.055) / 1.055) ** 2.4  
        g = g / 12.92 if g <= 0.04045 else ((g + 0.055) / 1.055) ** 2.4  
        b = b / 12.92 if b <= 0.04045 else ((b + 0.055) / 1.055) ** 2.4  
          
        # 计算XYZ值  
        X = M[0][0] * r + M[0][1] * g + M[0][2] * b  
        Y = M[1][0] * r + M[1][1] * g + M[1][2] * b  
        Z = M[2][0] * r + M[2][1] * g + M[2][2] * b  
          
        # XYZ到LAB转换  
        X /= 0.95047  
        Y /= 1.0  
        Z /= 1.08883  
          
        def f(t):  
            return t ** (1/3) if t > 0.008856 else 7.787 * t + 16/116  
          
        L = 116 * f(Y) - 16  
        a = 500 * (f(X) - f(Y))  
        b = 200 * (f(Y) - f(Z))  
          
        return [L, a, b]  
  
    def _set_configured_threshold(self, threshold):  
        """保存阈值到配置文件"""  
        if len(threshold) < 6:  
            return  
          
        app.set_app_config_kv('threshold_tuner', 'lmin', str(threshold[0]), False)  
        app.set_app_config_kv('threshold_tuner', 'lmax', str(threshold[1]), False)  
        app.set_app_config_kv('threshold_tuner', 'amin', str(threshold[2]), False)  
        app.set_app_config_kv('threshold_tuner', 'amax', str(threshold[3]), False)  
        app.set_app_config_kv('threshold_tuner', 'bmin', str(threshold[4]), False)  
        app.set_app_config_kv('threshold_tuner', 'bmax', str(threshold[5]), True)  
  
    def _get_configured_threshold(self):  
        """从配置文件加载阈值"""  
        threshold = [0, 100, -128, 127, -128, 127]  # 默认阈值  
          
        value_str = app.get_app_config_kv('threshold_tuner', 'lmin', '', False)  
        if len(value_str) > 0:  
            threshold[0] = int(value_str)  
        value_str = app.get_app_config_kv('threshold_tuner', 'lmax', '', False)  
        if len(value_str) > 0:  
            threshold[1] = int(value_str)  
        value_str = app.get_app_config_kv('threshold_tuner', 'amin', '', False)  
        if len(value_str) > 0:  
            threshold[2] = int(value_str)  
        value_str = app.get_app_config_kv('threshold_tuner', 'amax', '', False)  
        if len(value_str) > 0:  
            threshold[3] = int(value_str)  
        value_str = app.get_app_config_kv('threshold_tuner', 'bmin', '', False)  
        if len(value_str) > 0:  
            threshold[4] = int(value_str)  
        value_str = app.get_app_config_kv('threshold_tuner', 'bmax', '', False)  
        if len(value_str) > 0:  
            threshold[5] = int(value_str)  
          
        return threshold  
  
    def _is_in_button(self, x, y, btn_pos):  
        """检测点是否在按钮区域内"""  
        return (x > btn_pos[0] and x < btn_pos[0] + btn_pos[2] and   
                y > btn_pos[1] and y < btn_pos[1] + btn_pos[3])  
  
    def _handle_touch(self, img):  
        """处理触摸事件"""  
        x, y, pressed = self._ts.read()  
          
        # 按钮状态检测 - 参考face_emotion项目的实现  
        if pressed:  
            if not self._last_pressed:  
                self._last_pressed = True  
                # 检查是否点击按钮（使用显示屏坐标）  
                if self._is_in_button(x, y, self._btn_area_disp):  
                    self._to_get_pixel = not self._to_get_pixel  
                    print("取阈值模式:", "开启" if self._to_get_pixel else "关闭")  
        else:  
            self._last_pressed = False  
          
        # 如果在取阈值模式下，处理像素采样  
        if self._to_get_pixel and pressed:  
            # 将显示屏坐标映射回图像坐标  
            img_x, img_y = image.resize_map_pos_reverse(  
                img.width(), img.height(),  
                self._disp.width(), self._disp.height(),  
                image.Fit.FIT_CONTAIN, x, y  
            )  
              
            # 确保坐标在有效范围内  
            img_x = max(0, min(img_x, img.width() - 1))  
            img_y = max(0, min(img_y, img.height() - 1))  
              
            if self._last_x != img_x or self._last_y != img_y:  
                self._last_x = img_x  
                self._last_y = img_y  
                  
                # 获取像素RGB值并转换为LAB  
                rgb = img.get_pixel(img_x, img_y, True)  
                lab = self._rgb_to_lab(rgb)  
                  
                if len(lab) >= 3:  
                    # 自动计算阈值范围  
                    self._threshold[0] = max(math.floor(lab[0]) - 30, 0)  
                    self._threshold[1] = min(math.ceil(lab[0]) + 30, 100)  
                    self._threshold[2] = max(math.floor(lab[1]) - 10, -128)  
                    self._threshold[3] = min(math.ceil(lab[1]) + 10, 127)  
                    self._threshold[4] = max(math.floor(lab[2]) - 10, -128)  
                    self._threshold[5] = min(math.ceil(lab[2]) + 10, 127)  
                      
                    print("更新阈值:", self._threshold)  
                    self._set_configured_threshold(self._threshold)  
              
            # 绘制十字标记  
            img.draw_cross(img_x, img_y, image.COLOR_YELLOW, 8, 2)  
  
    def _draw_ui(self, img):  
        """绘制用户界面"""  
        # 绘制按钮  
        btn_color = image.COLOR_GREEN if self._to_get_pixel else image.COLOR_RED  
        img.draw_rect(self._btn_area[0], self._btn_area[1],   
                     self._btn_area[2], self._btn_area[3], btn_color, 2)  
          
        # 按钮文字  
        label = "取阈值"  
        label_size = image.string_size(label)  
        label_x = self._btn_area[0] + (self._btn_area[2] - label_size.width()) // 2  
        label_y = self._btn_area[1] + (self._btn_area[3] - label_size.height()) // 2  
        img.draw_string(label_x, label_y, label, image.COLOR_WHITE)  
          
        # 显示当前阈值信息  
        threshold_text = f"LAB:[{self._threshold[0]},{self._threshold[1]},{self._threshold[2]},{self._threshold[3]},{self._threshold[4]},{self._threshold[5]}]"  
        img.draw_string(0, 0, threshold_text, image.COLOR_WHITE)  
  
    def run(self):  
        """主运行循环"""  
        while not app.need_exit():  
            # 读取摄像头图像  
            img = self._cam.read()  
              
            # 处理触摸事件  
            self._handle_touch(img)  
              
            # 绘制用户界面  
            self._draw_ui(img)  
              
            # 显示图像  
            self._disp.show(img)  
  
if __name__ == '__main__':  
    tuner = ThresholdTuner()  
    tuner.run()