# MG996R 360度舵机问题修复指南

## 🚨 问题描述
- **问题1**: 水平舵机只能向左转动，不能向右转动
- **问题2**: 红线必须在黑框之内才能转动（限幅太大）
- **问题3**: 水平舵机移动速度比垂直舵机快，速度不匹配

## ✅ 已应用的修复

### 1. PWM参数调整
```python
# 修改前（可能导致单向问题）
self.horizontal_cw_duty = 10.0     # 顺时针 (2.0ms)
self.horizontal_ccw_duty = 5.0     # 逆时针 (1.0ms)

# 修改后（更精确的控制范围）
self.horizontal_cw_duty = 8.5      # 顺时针 (1.7ms)
self.horizontal_ccw_duty = 6.5     # 逆时针 (1.3ms)
```

### 2. 误差阈值调整
```python
# 修改前（限制太严格）
servo_error_threshold = 8

# 修改后（允许更大范围控制）
servo_error_threshold = 15
```

### 3. 速度控制优化
- 减小死区阈值到0.3%
- 添加PWM范围限制确保不超出边界
- 改进线性控制算法

## 🔧 测试和调试步骤

### 步骤1: 运行调试程序
```bash
python servo_debug.py
```

### 步骤2: 观察舵机行为
运行双向转动测试，观察：
- 7.0% PWM: 应该逆时针转动（向右）
- 7.5% PWM: 应该停止
- 8.0% PWM: 应该顺时针转动（向左）

### 步骤3: 根据测试结果调整参数

#### 如果舵机完全不动：
1. 检查电源供应（舵机需要足够的电流）
2. 检查信号线连接（A18引脚）
3. 检查舵机本身是否损坏

#### 如果只能单向转动：
1. 可能是PWM范围设置问题
2. 尝试调整 `horizontal_cw_duty` 和 `horizontal_ccw_duty`
3. 某些舵机可能需要不同的PWM范围

#### 如果转动但方向错误：
修改 `main.py` 第254行：
```python
# 如果方向相反，修改这行
horizontal_error = -err_x  # 改为 horizontal_error = err_x
```

## 🎯 推荐的PWM参数

### 标准MG996R 360度舵机：
```python
self.horizontal_stop_duty = 7.5    # 停止
self.horizontal_cw_duty = 8.5      # 顺时针最大
self.horizontal_ccw_duty = 6.5     # 逆时针最大
```

### 如果上述参数不工作，尝试：
```python
# 方案A：更小范围
self.horizontal_cw_duty = 8.0
self.horizontal_ccw_duty = 7.0

# 方案B：更大范围
self.horizontal_cw_duty = 9.0
self.horizontal_ccw_duty = 6.0

# 方案C：偏移停止位置
self.horizontal_stop_duty = 7.4    # 有些舵机停止位置不是7.5%
```

## 🔍 常见问题排查

### 问题：舵机抖动或不稳定
**解决方案：**
- 检查电源是否稳定
- 增大死区范围
- 降低PID参数

### 问题：响应太慢或太快
**解决方案：**
调整全局速度倍数：
```python
GLOBAL_SPEED_MULTIPLIER = 0.3  # 更慢
GLOBAL_SPEED_MULTIPLIER = 0.8  # 更快
```

### 问题：精度不够
**解决方案：**
- 减小误差阈值
- 调整PID参数
- 使用更精确的PWM范围

## 📝 修改main.py的关键位置

### 1. PWM参数 (第108-111行)
```python
self.horizontal_cw_duty = 8.5      # 根据测试结果调整
self.horizontal_ccw_duty = 6.5     # 根据测试结果调整
```

### 2. 误差阈值 (第682行)
```python
servo_error_threshold = 15         # 根据需要调整
```

### 3. 速度倍数 (第30行)
```python
GLOBAL_SPEED_MULTIPLIER = 0.6      # 根据需要调整
```

### 4. 方向控制 (第254行)
```python
horizontal_error = -err_x          # 如果方向错误，去掉负号
```

## 🚀 快速测试命令

在main.py中修改第757行来启用测试：
```python
test_choice = "2"  # 快速测试
test_choice = "3"  # 完整校准
test_choice = "4"  # 强制测试
```

## 📞 如果问题仍然存在

1. **硬件检查**：
   - 舵机电源是否充足（建议外部5V供电）
   - 信号线是否连接正确
   - 舵机本身是否为360度版本

2. **软件检查**：
   - PWM频率是否正确（50Hz）
   - 引脚配置是否正确（A18 -> PWM6）

3. **替代方案**：
   - 尝试使用不同的PWM通道
   - 考虑更换舵机型号
   - 使用步进电机替代

记住：每个舵机的特性可能略有不同，需要根据实际测试结果进行微调！
