# 自动重启问题解决指南

## 🔍 问题分析

你遇到的"运动运动着老是会自动重启"问题，通常由以下原因造成：

### 1. **电源供电不稳定** ⚡ (最常见)
- 舵机运动时功耗增大，电压下降
- 电源适配器功率不足
- 电源线接触不良

### 2. **内存泄漏** 💾
- 图像处理占用大量内存
- 变量未及时释放
- 垃圾回收不及时

### 3. **程序异常** 💥
- 未捕获的异常导致崩溃
- 数组越界、空指针等
- 硬件通信错误

### 4. **系统过热** 🌡️
- CPU温度过高触发保护
- 舵机过载发热
- 散热不良

## 🛠️ 已实施的修复

我已经对main.py进行了以下改进：

### 1. **内存管理优化**
```python
# 添加了定期垃圾回收
frame_count = 0
gc_interval = 100  # 每100帧回收一次

if frame_count % gc_interval == 0:
    import gc
    collected = gc.collect()
    if DEBUG and collected > 0:
        print(f"🗑️ 垃圾回收: 帧{frame_count}, 回收{collected}个对象")
```

### 2. **异常处理增强**
```python
# 主循环异常捕获
try:
    # 主循环代码
    pass
except Exception as e:
    error_count += 1
    print(f"❌ 主循环异常 (第{error_count}次): {e}")
    
    # 自动恢复机制
    gc.collect()
    time.sleep(0.1)
    target_detected = False
    no_target_count = 0
```

### 3. **错误计数和保护**
```python
# 错误次数限制
if error_count > 50:
    print("💥 错误次数过多，强制退出主循环")
    break
```

## 🔧 诊断工具

### 1. **自动重启诊断工具**
```bash
python auto_restart_diagnosis.py
```
- 监控内存使用
- 检查CPU温度
- 分析错误频率
- 生成诊断报告

### 2. **电源稳定性检查**
```bash
python power_stability_check.py
```
- 测试舵机功耗影响
- 检查电压变化
- 分析系统负载
- 评估电源稳定性

## 📋 立即检查清单

### 1. **电源检查** (优先级最高)
```bash
# 检查电源适配器规格
- 电压: 5V
- 电流: ≥3A (推荐5A)
- 功率: ≥15W (推荐25W)

# 检查连接
- USB线是否为数据线（非充电线）
- 接口是否松动
- 是否使用延长线或分线器
```

### 2. **运行诊断**
```bash
# 运行内存诊断
python auto_restart_diagnosis.py

# 运行电源诊断  
python power_stability_check.py
```

### 3. **调整配置**
```python
# 在main.py中调整以下参数

# 降低垃圾回收间隔
gc_interval = 50  # 从100改为50

# 启用调试模式
DEBUG = True

# 降低舵机速度
GLOBAL_SPEED_MULTIPLIER = 0.2  # 从0.3改为0.2
```

## 🎯 分步解决方案

### 步骤1: 电源优化 (最重要)
1. **使用高功率电源适配器**
   - 推荐: 5V/5A (25W)
   - 最低: 5V/3A (15W)

2. **检查电源线质量**
   - 使用粗线径的USB线
   - 避免过长的延长线
   - 确保接触良好

3. **独立舵机供电** (推荐)
   ```
   MaixCAM Pro: 5V/2A
   舵机系统: 5V/3A (独立供电)
   ```

### 步骤2: 软件优化
1. **启用改进的main.py**
   - 已添加内存管理
   - 已添加异常处理
   - 已添加错误恢复

2. **调整运行参数**
   ```python
   # 降低处理频率
   time.sleep(0.05)  # 20fps instead of 30fps
   
   # 减少调试输出
   DEBUG = False  # 正式运行时关闭
   
   # 降低舵机速度
   GLOBAL_SPEED_MULTIPLIER = 0.2
   ```

### 步骤3: 系统优化
1. **检查散热**
   - 确保通风良好
   - 避免阳光直射
   - 考虑添加散热片

2. **减少系统负载**
   - 关闭不必要的后台程序
   - 降低图像处理复杂度

## 🚨 紧急解决方案

如果问题仍然存在，尝试以下紧急方案：

### 1. **最小化运行模式**
```python
# 修改main.py第1152行
test_choice = "1"  # 跳过所有测试

# 修改调试设置
DEBUG = False
ENABLE_LASER_DETECTION = False  # 暂时禁用激光检测
```

### 2. **降低处理负载**
```python
# 降低摄像头分辨率
cam = camera.Camera(224, 224)  # 从448x448降到224x224

# 增加处理间隔
time.sleep(0.1)  # 10fps
```

### 3. **分离测试**
```python
# 只测试舵机，不运行图像处理
test_choice = "5"  # 完整舵机功能测试
```

## 📊 监控指标

运行时关注以下指标：

### 1. **内存使用**
```bash
# 正常范围: <70%
# 警告范围: 70-85%
# 危险范围: >85%
```

### 2. **CPU温度**
```bash
# 正常范围: <60°C
# 警告范围: 60-70°C  
# 危险范围: >70°C
```

### 3. **错误频率**
```bash
# 正常: <1次/分钟
# 警告: 1-5次/分钟
# 危险: >5次/分钟
```

## 🔄 测试流程

### 1. **基础稳定性测试**
```bash
# 运行3分钟稳定性测试
python auto_restart_diagnosis.py
```

### 2. **电源负载测试**
```bash
# 测试舵机功耗影响
python power_stability_check.py
```

### 3. **实际运行测试**
```bash
# 运行改进的main.py
python main.py
```

## 💡 预防措施

### 1. **定期维护**
- 每周清理灰尘
- 检查连接线
- 更新软件

### 2. **监控运行状态**
- 观察内存使用趋势
- 记录错误日志
- 监控温度变化

### 3. **备用方案**
- 准备备用电源
- 保存稳定的配置
- 记录工作参数

## 📞 进一步支持

如果问题仍未解决，请提供：

1. **诊断工具输出**
2. **电源适配器规格**
3. **重启前的错误信息**
4. **运行环境温度**
5. **具体的重启时机**

这样可以进行更精确的问题定位和解决。
