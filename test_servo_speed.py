#!/usr/bin/env python3
"""
舵机速度测试脚本
用于快速测试不同速度设置下的舵机响应
"""

from maix import pwm, pinmap, time, app
import math

class ServoSpeedTester:
    def __init__(self):
        """初始化舵机测试器"""
        try:
            # 配置引脚功能为PWM
            pinmap.set_pin_function("A19", "PWM7")  # 垂直舵机
            pinmap.set_pin_function("A18", "PWM6")  # 水平舵机

            # 初始化PWM
            self.vertical_pwm = pwm.PWM(7, freq=50, duty=7.5, enable=True)
            self.horizontal_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)

            print("舵机测试器初始化成功")
            
            # 舵机参数
            self.vertical_min_duty = 2.5
            self.vertical_max_duty = 12.5
            self.vertical_center_duty = 7.5
            self.vertical_current_angle = 90

            self.horizontal_stop_duty = 7.5
            self.horizontal_cw_duty = 8.0
            self.horizontal_ccw_duty = 7.0

        except Exception as e:
            print(f"舵机测试器初始化失败: {e}")
            self.vertical_pwm = None
            self.horizontal_pwm = None

    def set_vertical_angle(self, angle):
        """设置垂直舵机角度"""
        if self.vertical_pwm is None:
            return
        
        angle = max(0, min(180, angle))
        duty = self.vertical_min_duty + (angle / 180.0) * (self.vertical_max_duty - self.vertical_min_duty)
        
        try:
            self.vertical_pwm.duty(duty)
            self.vertical_current_angle = angle
            print(f"垂直角度: {angle}°, 占空比: {duty:.2f}%")
        except Exception as e:
            print(f"设置垂直舵机失败: {e}")

    def set_horizontal_speed(self, speed, speed_multiplier=1.0):
        """设置水平舵机速度（带速度倍数）"""
        if self.horizontal_pwm is None:
            return
        
        # 应用速度倍数
        speed = speed * speed_multiplier
        speed = max(-100, min(100, speed))
        
        if abs(speed) < 2:
            speed = 0
        
        if speed == 0:
            duty = self.horizontal_stop_duty
        elif speed > 0:
            # 顺时针
            duty_range = self.horizontal_cw_duty - self.horizontal_stop_duty
            duty_change = (abs(speed) / 100.0) * duty_range * 0.5
            duty = self.horizontal_stop_duty + duty_change
        else:
            # 逆时针
            duty_range = self.horizontal_stop_duty - self.horizontal_ccw_duty
            duty_change = (abs(speed) / 100.0) * duty_range * 0.5
            duty = self.horizontal_stop_duty - duty_change
        
        try:
            self.horizontal_pwm.duty(duty)
            print(f"水平速度: {speed:.1f}% (倍数: {speed_multiplier:.2f}), 占空比: {duty:.3f}%")
        except Exception as e:
            print(f"设置水平舵机失败: {e}")

    def test_speed_multipliers(self):
        """测试不同的速度倍数"""
        print("\n=== 测试不同速度倍数 ===")
        
        # 测试速度倍数列表
        multipliers = [0.2, 0.3, 0.4, 0.5, 0.6, 0.8, 1.0]
        base_speed = 20  # 基础速度
        
        for multiplier in multipliers:
            print(f"\n测试速度倍数: {multiplier}")
            
            # 顺时针测试
            print("顺时针旋转...")
            self.set_horizontal_speed(base_speed, multiplier)
            time.sleep(2)
            
            # 停止
            print("停止...")
            self.set_horizontal_speed(0, multiplier)
            time.sleep(1)
            
            # 逆时针测试
            print("逆时针旋转...")
            self.set_horizontal_speed(-base_speed, multiplier)
            time.sleep(2)
            
            # 停止
            print("停止...")
            self.set_horizontal_speed(0, multiplier)
            time.sleep(1)
            
            print(f"速度倍数 {multiplier} 测试完成")

    def test_vertical_servo(self):
        """测试垂直舵机"""
        print("\n=== 测试垂直舵机 ===")

        angles = [45, 90, 135, 90]  # 测试角度序列

        for angle in angles:
            print(f"移动到 {angle}°")
            self.set_vertical_angle(angle)
            time.sleep(1.5)

        print("垂直舵机测试完成")

    def test_vertical_speed(self):
        """测试垂直舵机不同速度（模拟步长控制）"""
        print("\n=== 测试垂直舵机速度 ===")

        # 模拟不同的步长（实际应用中由PID控制）
        step_sizes = [1, 2, 5, 10]  # 不同的角度步长

        for step in step_sizes:
            print(f"\n测试步长: {step}°")
            current_angle = 90
            target_angle = 45

            print(f"从 {current_angle}° 移动到 {target_angle}°，步长 {step}°")

            while abs(current_angle - target_angle) > step:
                if current_angle > target_angle:
                    current_angle -= step
                else:
                    current_angle += step

                self.set_vertical_angle(current_angle)
                time.sleep(0.2)  # 步长间隔

            # 最后移动到目标位置
            self.set_vertical_angle(target_angle)
            time.sleep(1)

            print(f"步长 {step}° 测试完成")

        # 回到中位
        self.set_vertical_angle(90)
        print("垂直舵机速度测试完成")

    def interactive_test(self):
        """交互式测试"""
        print("\n=== 交互式速度测试 ===")
        print("输入命令:")
        print("  h <速度> <倍数> - 设置水平舵机速度 (例: h 20 0.3)")
        print("  v <角度> - 设置垂直舵机角度 (例: v 90)")
        print("  s - 停止水平舵机")
        print("  q - 退出")
        
        while not app.need_exit():
            try:
                cmd = input("\n请输入命令: ").strip().split()
                
                if not cmd:
                    continue
                
                if cmd[0] == 'q':
                    break
                elif cmd[0] == 's':
                    self.set_horizontal_speed(0)
                elif cmd[0] == 'h' and len(cmd) >= 2:
                    speed = float(cmd[1])
                    multiplier = float(cmd[2]) if len(cmd) > 2 else 1.0
                    self.set_horizontal_speed(speed, multiplier)
                elif cmd[0] == 'v' and len(cmd) >= 2:
                    angle = float(cmd[1])
                    self.set_vertical_angle(angle)
                else:
                    print("无效命令")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"命令执行错误: {e}")

    def cleanup(self):
        """清理资源"""
        try:
            if self.vertical_pwm:
                self.vertical_pwm.close()
            if self.horizontal_pwm:
                self.horizontal_pwm.close()
            print("舵机测试器资源已清理")
        except Exception as e:
            print(f"清理资源时出错: {e}")

def main():
    """主函数"""
    print("舵机速度测试程序")
    print("=" * 40)
    
    tester = ServoSpeedTester()
    
    if tester.vertical_pwm is None or tester.horizontal_pwm is None:
        print("舵机初始化失败，请检查连接")
        return
    
    try:
        # 回到中位
        print("舵机回中位...")
        tester.set_vertical_angle(90)
        tester.set_horizontal_speed(0)
        time.sleep(1)
        
        while True:
            print("\n选择测试模式:")
            print("1. 自动测试水平舵机不同速度倍数")
            print("2. 测试垂直舵机位置控制")
            print("3. 测试垂直舵机速度控制")
            print("4. 交互式测试")
            print("5. 退出")

            choice = input("请选择 (1-5): ").strip()

            if choice == '1':
                tester.test_speed_multipliers()
            elif choice == '2':
                tester.test_vertical_servo()
            elif choice == '3':
                tester.test_vertical_speed()
            elif choice == '4':
                tester.interactive_test()
            elif choice == '5':
                break
            else:
                print("无效选择")
    
    except KeyboardInterrupt:
        print("\n程序被中断")
    finally:
        # 停止舵机并清理资源
        print("停止舵机...")
        tester.set_horizontal_speed(0)
        tester.set_vertical_angle(90)
        time.sleep(0.5)
        tester.cleanup()
        print("测试程序结束")

if __name__ == "__main__":
    main()
