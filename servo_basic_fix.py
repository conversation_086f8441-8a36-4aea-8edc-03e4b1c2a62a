#!/usr/bin/env python3
"""
舵机基础问题修复
先解决舵机双向转动问题，再解决PID控制

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def test_servo_direction():
    """测试舵机方向问题"""
    print("🔧 舵机方向测试")
    print("="*20)
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        print("\n测试不同PWM占空比的舵机反应：")
        
        # 测试序列 - 从停止位置开始，逐步增大变化
        test_sequence = [
            (7.5, "停止位置", 2),
            (7.6, "微小增加 +0.1%", 3),
            (7.5, "回到停止", 1),
            (7.4, "微小减少 -0.1%", 3),
            (7.5, "回到停止", 1),
            (7.8, "小幅增加 +0.3%", 3),
            (7.5, "回到停止", 1),
            (7.2, "小幅减少 -0.3%", 3),
            (7.5, "回到停止", 1),
            (8.0, "中等增加 +0.5%", 3),
            (7.5, "回到停止", 1),
            (7.0, "中等减少 -0.5%", 3),
            (7.5, "回到停止", 1),
            (8.5, "大幅增加 +1.0%", 3),
            (7.5, "回到停止", 1),
            (6.5, "大幅减少 -1.0%", 3),
            (7.5, "最终停止", 2)
        ]
        
        print("请仔细观察每个PWM值的舵机反应：")
        print("- 如果PWM增加和减少都是同一个方向，说明停止位置不对")
        print("- 如果完全不动，说明PWM变化不够大")
        
        for duty, description, duration in test_sequence:
            change = duty - 7.5
            print(f"\n设置: {duty:.1f}% ({description}) [变化: {change:+.1f}%]")
            
            try:
                servo_pwm.duty(duty)
                
                if duty > 7.5:
                    print(f"  预期：顺时针转动（向左）")
                elif duty < 7.5:
                    print(f"  预期：逆时针转动（向右）")
                else:
                    print(f"  预期：完全停止")
                
                time.sleep(duration)
                
            except Exception as e:
                print(f"  ❌ 设置失败: {e}")
        
        print("\n" + "="*20)
        print("方向测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def find_correct_stop_position():
    """寻找正确的停止位置"""
    print("\n🎯 寻找正确的停止位置")
    print("="*25)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        print("\n测试不同的停止位置：")
        print("真正的360度舵机应该在某个PWM值完全停止")
        
        # 测试不同的可能停止位置
        stop_candidates = [7.0, 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 7.8, 7.9, 8.0]
        
        for stop_duty in stop_candidates:
            print(f"\n测试停止位置: {stop_duty:.1f}%")
            servo_pwm.duty(stop_duty)
            print(f"  观察5秒，舵机是否完全停止...")
            time.sleep(5)
            
            # 测试这个停止位置的双向控制
            print(f"  测试双向控制：")
            
            # 向一个方向
            test_duty1 = stop_duty + 0.3
            print(f"    设置 {test_duty1:.1f}% (停止位置+0.3%)")
            servo_pwm.duty(test_duty1)
            time.sleep(3)
            
            # 回到停止
            servo_pwm.duty(stop_duty)
            time.sleep(1)
            
            # 向另一个方向
            test_duty2 = stop_duty - 0.3
            print(f"    设置 {test_duty2:.1f}% (停止位置-0.3%)")
            servo_pwm.duty(test_duty2)
            time.sleep(3)
            
            # 回到停止
            servo_pwm.duty(stop_duty)
            time.sleep(1)
            
            print(f"  如果舵机能双向转动且在{stop_duty:.1f}%停止，这就是正确的停止位置")
        
        print("\n寻找停止位置测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_extreme_range():
    """测试极限PWM范围"""
    print("\n⚡ 极限PWM范围测试")
    print("="*25)
    
    try:
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        print("\n⚠️ 使用极限PWM值测试，请小心观察")
        
        # 极限测试序列
        extreme_sequence = [
            (7.5, "停止位置", 2),
            (5.0, "极限逆时针 (1.0ms)", 4),
            (7.5, "停止", 2),
            (10.0, "极限顺时针 (2.0ms)", 4),
            (7.5, "停止", 2),
            (4.0, "超极限逆时针 (0.8ms)", 4),
            (7.5, "停止", 2),
            (11.0, "超极限顺时针 (2.2ms)", 4),
            (7.5, "最终停止", 2)
        ]
        
        for duty, description, duration in extreme_sequence:
            change = duty - 7.5
            ms_value = duty / 50.0  # 转换为毫秒
            
            print(f"\n设置: {duty:.1f}% ({description})")
            print(f"  PWM变化: {change:+.1f}% ({ms_value:.2f}ms)")
            
            try:
                servo_pwm.duty(duty)
                print(f"  ✓ PWM设置成功，观察{duration}秒...")
                time.sleep(duration)
            except Exception as e:
                print(f"  ❌ PWM设置失败: {e}")
        
        print("\n极限测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🔧 舵机基础问题诊断和修复")
    print("="*30)
    
    try:
        print("这个测试将帮助找出舵机的基础问题：")
        print("1. 正确的停止位置")
        print("2. 双向转动能力")
        print("3. 所需的PWM范围")
        
        test_servo_direction()
        find_correct_stop_position()
        test_extreme_range()
        
        print("\n" + "="*30)
        print("🏁 所有测试完成！")
        print("\n📋 根据测试结果：")
        print("1. 找出真正的停止位置（舵机完全不动的PWM值）")
        print("2. 确定双向转动的PWM范围")
        print("3. 更新main.py中的PWM参数")
        print("\n如果舵机在所有测试中都不能双向转动：")
        print("- 可能不是真正的360度舵机")
        print("- 或者需要更大的PWM范围")
        print("- 或者舵机本身有问题")
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()
