#!/usr/bin/env python3
"""
高级PID控制器
参考222.py的PID实现，支持积分分离、积分限幅、防振荡等高级特性

特点：
1. 积分分离：大误差时不积分，防止积分饱和
2. 积分限幅：限制积分项最大值
3. 死区补偿：确保最小输出，克服机械死区
4. 输出变化率限制：防止舵机运动过于剧烈
5. 微小偏差控制：偏差大时转速快，偏差小时转速慢
6. 误差死区：当误差很小时舵机完全不动，避免抖动
7. 防振荡优化：稳定性检测、动态死区
8. 防超调优化：低增益PID、强阻尼、小步长限制

@author: AI Assistant (参考222.py)
@date: 2025.8.2
"""

import time
import math

def limit_step(current, last, max_step=2):
    """限制输出变化率，防止舵机运动过于剧烈"""
    delta = current - last
    if abs(delta) > max_step:
        return last + (max_step if delta > 0 else -max_step)
    else:
        return current

class AdvancedPIDController:
    """高级PID控制器（移植自222.py）"""
    
    def __init__(self, Kp, Ki, Kd, error_threshold=8, integral_limit=80, min_output=3, max_output=40, max_step=2):
        """
        高级PID控制器，支持积分分离和积分限幅

        Args:
            Kp: 比例系数
            Ki: 积分系数
            Kd: 微分系数
            error_threshold: 积分分离阈值，小于此值才进行积分
            integral_limit: 积分限幅值
            min_output: 最小输出（死区补偿）
            max_output: 最大输出限制
            max_step: 输出变化率限制
        """
        self.Kp = Kp
        self.Ki = Ki
        self.Kd = Kd
        self.error_threshold = error_threshold  # 积分分离阈值
        self.integral_limit = integral_limit    # 积分限幅
        self.min_output = min_output           # 最小输出
        self.max_output = max_output           # 最大输出
        self.max_step = max_step               # 输出变化率限制
        
        # 内部状态
        self.last_error = 0
        self.integral = 0
        self.last_output = 0
        
        # 调试信息
        self.debug = False

    def compute(self, error):
        """
        计算PID输出

        特点：
        1. 积分分离：只有误差小于阈值时才积分，防止积分饱和
        2. 积分限幅：限制积分项的最大值
        3. 输出变化率限制：防止输出突变
        4. 死区补偿：确保最小输出
        """
        # 调试：打印PID状态
        if self.debug and abs(error) > 1:  # 只在有明显误差时打印
            print(f"🔧 PID计算前 - 误差:{error:.2f}, 积分:{self.integral:.3f}, 上次误差:{self.last_error:.3f}")

        # 微分项
        derivative = error - self.last_error
        self.last_error = error

        # 积分分离：小误差才积分，防止大误差时积分饱和
        if abs(error) > self.error_threshold:
            # 大误差时不积分，避免积分饱和
            if self.debug:
                print(f"🚫 大误差({abs(error):.1f} > {self.error_threshold})，跳过积分")
        else:
            # 小误差时进行积分，消除稳态误差
            self.integral += error
            # 积分限幅，防止积分项过大
            self.integral = max(min(self.integral, self.integral_limit), -self.integral_limit)
            if self.debug:
                print(f"✅ 小误差积分，当前积分值: {self.integral:.3f}")

        # PID计算
        p_term = self.Kp * error
        i_term = self.Ki * self.integral
        d_term = self.Kd * derivative
        output = p_term + i_term + d_term

        if self.debug:
            print(f"📊 PID项: P={p_term:.2f}, I={i_term:.2f}, D={d_term:.2f}, 总输出={output:.2f}")

        # 限制输出变化率，防止舵机运动过于剧烈
        output = limit_step(output, self.last_output, max_step=self.max_step)
        self.last_output = output

        # 输出限幅和死区补偿
        if output > 0:
            output = max(min(output, self.max_output), self.min_output)
        elif output < 0:
            output = min(max(output, -self.max_output), -self.min_output)
        else:
            output = 0

        if self.debug:
            print(f"🎯 最终输出: {output:.2f} (限幅后)")

        return output
    
    def reset(self):
        """重置PID状态"""
        self.last_error = 0
        self.integral = 0
        self.last_output = 0
        if self.debug:
            print("🔄 PID状态已重置")
    
    def set_debug(self, debug):
        """设置调试模式"""
        self.debug = debug
    
    def get_status(self):
        """获取PID状态信息"""
        return {
            "last_error": self.last_error,
            "integral": self.integral,
            "last_output": self.last_output,
            "Kp": self.Kp,
            "Ki": self.Ki,
            "Kd": self.Kd
        }

class ServoStabilityController:
    """舵机稳定性控制器（参考222.py）"""
    
    def __init__(self, stability_check_frames=5, max_stable_error=6, error_dead_zone=5):
        """
        初始化稳定性控制器
        
        Args:
            stability_check_frames: 连续稳定帧数要求
            max_stable_error: 认为稳定的最大误差
            error_dead_zone: 误差死区
        """
        self.stability_check_frames = stability_check_frames
        self.max_stable_error = max_stable_error
        self.error_dead_zone = error_dead_zone
        
        # 稳定性跟踪
        self.stable_frame_count = 0
        self.last_errors = []  # 记录最近几帧的误差
        self.is_stable = False
        
        # 调试信息
        self.debug = False
    
    def update_stability(self, err_x, err_y):
        """
        更新稳定性状态
        
        Args:
            err_x: X轴误差
            err_y: Y轴误差
            
        Returns:
            bool: 是否稳定
        """
        # 计算总误差
        total_error = math.sqrt(err_x**2 + err_y**2)
        
        # 更新误差历史
        self.last_errors.append(total_error)
        if len(self.last_errors) > self.stability_check_frames:
            self.last_errors.pop(0)
        
        # 检查稳定性
        if len(self.last_errors) >= self.stability_check_frames:
            max_recent_error = max(self.last_errors)
            if max_recent_error < self.max_stable_error:
                self.stable_frame_count += 1
                self.is_stable = True
                if self.debug:
                    print(f"✅ 系统稳定: 最大误差{max_recent_error:.1f} < {self.max_stable_error}")
            else:
                self.stable_frame_count = 0
                self.is_stable = False
                if self.debug:
                    print(f"❌ 系统不稳定: 最大误差{max_recent_error:.1f} >= {self.max_stable_error}")
        
        return self.is_stable
    
    def should_move(self, err_x, err_y):
        """
        判断是否应该移动舵机
        
        Args:
            err_x: X轴误差
            err_y: Y轴误差
            
        Returns:
            bool: 是否应该移动
        """
        # 如果已经稳定，使用更大的死区
        current_dead_zone = self.error_dead_zone
        if self.is_stable:
            current_dead_zone = self.error_dead_zone * 1.5  # 稳定时死区增大50%
        
        # 死区判断：当误差很小时，舵机不动作
        if abs(err_x) < current_dead_zone and abs(err_y) < current_dead_zone:
            if self.debug:
                status = "稳定" if self.is_stable else "普通"
                print(f"💤 误差在死区内: X={err_x:+.1f}, Y={err_y:+.1f} (死区±{current_dead_zone:.1f}, {status})")
            return False  # 误差太小，舵机不动
        
        # 如果刚刚稳定，给一个短暂的延迟
        if self.is_stable and self.stable_frame_count < self.stability_check_frames + 2:
            if self.debug:
                print(f"⏳ 稳定延迟中: {self.stable_frame_count}/{self.stability_check_frames + 2}")
            return False
        
        return True
    
    def reset_stability(self):
        """重置稳定性状态"""
        self.stable_frame_count = 0
        self.is_stable = False
        self.last_errors.clear()
        if self.debug:
            print("🔄 稳定性状态已重置")
    
    def set_debug(self, debug):
        """设置调试模式"""
        self.debug = debug
    
    def get_status(self):
        """获取稳定性状态信息"""
        return {
            "is_stable": self.is_stable,
            "stable_frame_count": self.stable_frame_count,
            "last_errors": self.last_errors.copy(),
            "error_dead_zone": self.error_dead_zone
        }

class AdvancedServoController:
    """高级舵机控制器（整合PID和稳定性控制）"""
    
    def __init__(self, pid_params=None, stability_params=None, debug=False):
        """
        初始化高级舵机控制器
        
        Args:
            pid_params: PID参数字典
            stability_params: 稳定性参数字典
            debug: 调试模式
        """
        # 默认PID参数（参考222.py的优化参数）
        if pid_params is None:
            pid_params = {
                "Kp": 0.2,           # 比例系数（进一步降低）
                "Ki": 0.008,         # 积分系数（很小的积分）
                "Kd": 0.75,          # 微分系数（增加阻尼）
                "error_threshold": 8, # 积分分离阈值
                "integral_limit": 10, # 积分限幅值
                "min_output": 1,     # 最小输出
                "max_output": 15,    # 最大输出限制
                "max_step": 2        # PID输出变化率限制
            }
        
        # 默认稳定性参数
        if stability_params is None:
            stability_params = {
                "stability_check_frames": 5,  # 连续稳定帧数要求
                "max_stable_error": 6,        # 认为稳定的最大误差
                "error_dead_zone": 5          # 误差死区
            }
        
        # 创建PID控制器
        self.pid_x = AdvancedPIDController(**pid_params)
        self.pid_y = AdvancedPIDController(**pid_params)
        
        # 创建稳定性控制器
        self.stability = ServoStabilityController(**stability_params)
        
        # 设置调试模式
        self.debug = debug
        self.pid_x.set_debug(debug)
        self.pid_y.set_debug(debug)
        self.stability.set_debug(debug)
        
        # 偏移补偿参数
        self.offset_x = -12
        self.offset_y = -12
        
        print("🎯 高级舵机控制器初始化完成")
        print(f"📊 PID参数: Kp={pid_params['Kp']}, Ki={pid_params['Ki']}, Kd={pid_params['Kd']}")
        print(f"🔧 稳定性参数: 死区={stability_params['error_dead_zone']}, 稳定阈值={stability_params['max_stable_error']}")
        print(f"🎯 偏移补偿: X={self.offset_x}, Y={self.offset_y}")
    
    def compute_control(self, target_x, target_y, center_x, center_y):
        """
        计算控制输出
        
        Args:
            target_x, target_y: 目标位置
            center_x, center_y: 画面中心
            
        Returns:
            tuple: (should_move, control_x, control_y, error_info)
        """
        # 计算误差（加入偏移补偿）
        err_x = target_x - (center_x + self.offset_x)
        err_y = target_y - (center_y + self.offset_y)
        
        # 更新稳定性状态
        is_stable = self.stability.update_stability(err_x, err_y)
        
        # 判断是否应该移动
        should_move = self.stability.should_move(err_x, err_y)
        
        control_x = 0
        control_y = 0
        
        if should_move:
            # 计算PID控制输出
            control_x = self.pid_x.compute(err_x)
            control_y = self.pid_y.compute(-err_y)  # Y轴方向相反
            
            # 重置稳定计数
            self.stability.reset_stability()
            
            if self.debug:
                print(f"🎮 控制输出: X={control_x:.2f}, Y={control_y:.2f}")
        
        # 误差信息
        error_info = {
            "err_x": err_x,
            "err_y": err_y,
            "is_stable": is_stable,
            "should_move": should_move,
            "total_error": math.sqrt(err_x**2 + err_y**2)
        }
        
        return should_move, control_x, control_y, error_info
    
    def reset_all(self):
        """重置所有状态"""
        self.pid_x.reset()
        self.pid_y.reset()
        self.stability.reset_stability()
        if self.debug:
            print("🔄 所有控制器状态已重置")
    
    def set_offset(self, offset_x, offset_y):
        """设置偏移补偿"""
        self.offset_x = offset_x
        self.offset_y = offset_y
        print(f"🎯 偏移补偿已更新: X={offset_x}, Y={offset_y}")
    
    def set_debug(self, debug):
        """设置调试模式"""
        self.debug = debug
        self.pid_x.set_debug(debug)
        self.pid_y.set_debug(debug)
        self.stability.set_debug(debug)
    
    def get_full_status(self):
        """获取完整状态信息"""
        return {
            "pid_x": self.pid_x.get_status(),
            "pid_y": self.pid_y.get_status(),
            "stability": self.stability.get_status(),
            "offset": {"x": self.offset_x, "y": self.offset_y}
        }

# 测试函数
def test_advanced_pid():
    """测试高级PID控制器"""
    print("🧪 测试高级PID控制器")
    print("=" * 40)
    
    # 创建控制器
    controller = AdvancedServoController(debug=True)
    
    # 模拟测试数据
    test_cases = [
        (274, 194, 224, 224, "目标在右上"),
        (174, 254, 224, 224, "目标在左下"),
        (224, 224, 224, 224, "目标在中心"),
        (300, 150, 224, 224, "目标在右上角"),
        (150, 300, 224, 224, "目标在左下角")
    ]
    
    for target_x, target_y, center_x, center_y, description in test_cases:
        print(f"\n📍 测试: {description}")
        print(f"   目标: ({target_x}, {target_y}), 中心: ({center_x}, {center_y})")
        
        should_move, control_x, control_y, error_info = controller.compute_control(
            target_x, target_y, center_x, center_y
        )
        
        print(f"   误差: X={error_info['err_x']:+.1f}, Y={error_info['err_y']:+.1f}")
        print(f"   控制: X={control_x:+.2f}, Y={control_y:+.2f}")
        print(f"   状态: {'移动' if should_move else '静止'}, {'稳定' if error_info['is_stable'] else '不稳定'}")
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    test_advanced_pid()
