#!/usr/bin/env python3
"""
增强参数舵机测试
使用更大的PWM范围和速度倍数

@author: AI Assistant
@date: 2025.8.1
"""

from maix import pwm, pinmap, time

def test_enhanced_servo():
    """测试增强参数下的舵机性能"""
    print("🚀 增强参数舵机测试")
    print("="*30)
    
    try:
        # 初始化PWM
        pinmap.set_pin_function("A18", "PWM6")
        servo_pwm = pwm.PWM(6, freq=50, duty=7.5, enable=True)
        print("✓ PWM初始化成功")
        
        # 增强参数
        stop_duty = 7.5
        cw_duty = 9.0      # 增大到9.0%
        ccw_duty = 6.0     # 减小到6.0%
        
        print(f"PWM参数: 停止={stop_duty}%, 顺时针={cw_duty}%, 逆时针={ccw_duty}%")
        
        # 测试序列 - 使用更大的PWM变化
        test_sequence = [
            (7.5, "停止位置", 2),
            (8.0, "小幅顺时针", 3),
            (7.5, "停止", 1),
            (7.0, "小幅逆时针", 3),
            (7.5, "停止", 1),
            (8.5, "中等顺时针", 3),
            (7.5, "停止", 1),
            (6.5, "中等逆时针", 3),
            (7.5, "停止", 1),
            (9.0, "最大顺时针", 3),
            (7.5, "停止", 1),
            (6.0, "最大逆时针", 3),
            (7.5, "最终停止", 2)
        ]
        
        print("\n开始增强测试序列...")
        print("注意观察PWM变化幅度是否足够大：")
        
        for duty, description, duration in test_sequence:
            change = duty - 7.5
            print(f"\n设置: {duty:.1f}% ({description}) [变化: {change:+.1f}%]")
            servo_pwm.duty(duty)
            
            if duty > 7.5:
                print(f"  ← 预期：顺时针转动（向左）")
            elif duty < 7.5:
                print(f"  → 预期：逆时针转动（向右）")
            else:
                print(f"  ⏸ 预期：完全停止")
            
            time.sleep(duration)
        
        print("\n" + "="*30)
        print("增强测试完成！")
        
        # 分析结果
        print("\n📊 PWM变化分析：")
        print(f"最大顺时针变化: +{9.0-7.5:.1f}% (7.5% → 9.0%)")
        print(f"最大逆时针变化: {6.0-7.5:.1f}% (7.5% → 6.0%)")
        print(f"总变化范围: {9.0-6.0:.1f}% (6.0% ~ 9.0%)")
        
        print("\n✅ 如果舵机现在能转动：")
        print("   - PWM范围问题已解决")
        print("   - 可以正常使用main.py")
        
        print("\n❌ 如果舵机仍不能转动：")
        print("   - 可能是硬件问题（电源/连接/舵机故障）")
        print("   - 或者需要更大的PWM范围")
        
        # 额外测试：极限PWM值
        print("\n🔬 极限PWM测试（小心使用）：")
        extreme_tests = [
            (5.5, "极限逆时针"),
            (7.5, "停止"),
            (9.5, "极限顺时针"),
            (7.5, "停止")
        ]
        
        for duty, desc in extreme_tests:
            print(f"测试: {duty:.1f}% ({desc})")
            servo_pwm.duty(duty)
            time.sleep(2)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n程序结束")

def simulate_pid_with_enhanced_params():
    """模拟PID控制使用增强参数"""
    print("\n🧮 模拟PID控制（增强参数）")
    print("="*35)
    
    # 模拟PID计算过程
    test_cases = [
        (30, "中等误差"),
        (10, "小误差"),
        (-20, "负误差")
    ]
    
    for err_x, desc in test_cases:
        print(f"\n测试: {desc} (err_x={err_x})")
        
        # PID逻辑（简化版）
        horizontal_error = -err_x
        
        if abs(horizontal_error) <= 25:  # 正常区域
            horizontal_speed = horizontal_error * 0.5
            horizontal_speed = max(-12, min(12, horizontal_speed))
        else:  # PID区域
            horizontal_speed = horizontal_error * 0.8  # 更大的比例系数
            horizontal_speed = max(-16, min(16, horizontal_speed))
        
        # 应用增强参数
        speed_multiplier = 1.0  # 新的全局倍数
        speed_scale = 2.0       # 新的缩放因子
        final_speed = horizontal_speed * speed_scale * speed_multiplier
        
        # PWM计算
        if final_speed > 0:
            duty_change = (abs(final_speed) / 100.0) * (9.0 - 7.5)  # 使用新的PWM范围
            final_duty = 7.5 + duty_change
        elif final_speed < 0:
            duty_change = (abs(final_speed) / 100.0) * (7.5 - 6.0)  # 使用新的PWM范围
            final_duty = 7.5 - duty_change
        else:
            final_duty = 7.5
        
        print(f"  horizontal_error: {horizontal_error}")
        print(f"  horizontal_speed: {horizontal_speed:.1f}%")
        print(f"  final_speed: {final_speed:.1f}%")
        print(f"  final_duty: {final_duty:.3f}%")
        print(f"  PWM变化: {final_duty-7.5:+.3f}%")

if __name__ == "__main__":
    test_enhanced_servo()
    simulate_pid_with_enhanced_params()
